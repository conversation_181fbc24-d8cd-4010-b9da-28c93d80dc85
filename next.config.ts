import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    API_BASE_URL: "https://api.exchangim.com/api/",
  },
  images: {
    domains: ["api.exchangim.com"], // Simple way to allow all subpaths
    // OR use remotePatterns (more explicit)
    remotePatterns: [
      {
        protocol: "http",
        hostname: "api.exchangim.com",
        port: "",
        pathname: "/storage/**",
      },
    ],
  },
  httpAgentOptions: {
    keepAlive: false,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};
module.exports = nextConfig;
