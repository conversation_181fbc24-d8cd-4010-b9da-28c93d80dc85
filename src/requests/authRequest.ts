"use server";

import createAxiosInstance from "@/config/axiosAdapter";
import { cookies } from "next/headers";

// handling user signin
export async function loginUser(phone: string) {
  try {
    const axiosInstance = await createAxiosInstance();

    const response = await axiosInstance.post("auth/sendOtp", {
      phone,
    });
    console.log(response.data);
    return {
      isError: false,
      status: response.data.result.is_new_user,
      message: response.data.result.message,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling verify otp
export async function verifyOtp(phone: string | null, otp: string) {
  const cookieStore = await cookies();
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("auth/verifyOtp", {
      phone,
      otp,
    });

    cookieStore.set("token", response.data.result.token, {
      secure: true,
      maxAge: 60 * 60 * 24 * 7,
    });
    return {
      isError: false,
      message: response.data.result.message,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling register
export async function register(
  otp: string,
  phone: string | null,
  firstname: string,
  lastname: string,
  gender: string,
  national_id: string
) {
  const cookieStore = await cookies();

  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("auth/verifyOtp", {
      phone,
      otp,
      firstname,
      lastname,
      gender,
      national_id,
    });

    cookieStore.set("token", response.data.result.token, {
      secure: true,
      maxAge: 60 * 60 * 24 * 7,
    });
    return {
      isError: false,
      status: response.data.status,
      message: response.data.result.message,
    };
  } catch {
    return {
      isError: true,
    };
  }
}
