"use client";

export async function resendOtp(phone: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.exchangim.com/api/'}auth/sendOtp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        isError: true,
        message: "خطایی رخ داد. لطفا دوباره تلاش کنید."
      };
    }

    return {
      isError: false,
      status: data.result.is_new_user,
      message: data.result.message,
    };
  } catch (error) {
    return {
      isError: true,
      message: "خطایی رخ داد. لطفا دوباره تلاش کنید."
    };
  }
}
