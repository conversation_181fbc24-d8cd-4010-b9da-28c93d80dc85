"use client";

import { useEffect, useState, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import LoadingIndicator from "./LoadingIndicator";

export default function RouterTransition() {
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigationStartTimeRef = useRef<number | null>(null);

  // حداقل زمان نمایش لودینگ (میلی‌ثانیه) - کاهش برای سرعت بیشتر
  const MIN_LOADING_TIME = 1500;
  // حداکثر زمان انتظار قبل از نمایش لودینگ (میلی‌ثانیه)
  const LOADING_DELAY = 0; // افزایش برای جلوگیری از نمایش لودینگ در ناوبری‌های سریع

  useEffect(() => {
    // تابع برای شروع لودینگ با تأخیر
    const startLoading = () => {
      // اگر قبلاً در حال لودینگ هستیم، کاری انجام نمی‌دهیم
      if (isLoading) return;

      // ذخیره زمان شروع ناوبری
      navigationStartTimeRef.current = Date.now();

      // استفاده از تایمر برای تأخیر در نمایش لودینگ
      // این باعث می‌شود در ناوبری‌های سریع، لودینگ نمایش داده نشود
      if (LOADING_DELAY > 0) {
        loadingTimeoutRef.current = setTimeout(() => {
          // بررسی وضعیت بارگذاری صفحه
          if (document.readyState !== "complete") {
            setIsLoading(true);
          }
        }, LOADING_DELAY);
      } else {
        // اگر تأخیر نداریم، فوراً لودینگ را نمایش می‌دهیم
        setIsLoading(true);
      }
    };

    // اضافه کردن لیسنرها برای تغییر مسیر
    const handleAnchorClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const anchor = target.closest("a");

      // بررسی می‌کنیم که آیا لینک داخلی است
      if (
        anchor &&
        anchor.href &&
        anchor.href.startsWith(window.location.origin) &&
        !anchor.target
      ) {
        startLoading();
      }
    };

    // اضافه کردن لیسنر برای رویدادهای ناوبری
    const handleRouteChangeStart = () => {
      startLoading();
    };

    // اضافه کردن لیسنر برای کلیک روی لینک‌ها
    document.addEventListener("click", handleAnchorClick);
    window.addEventListener("route-change-start", handleRouteChangeStart);

    // پاکسازی لیسنرها
    return () => {
      document.removeEventListener("click", handleAnchorClick);
      window.removeEventListener("route-change-start", handleRouteChangeStart);

      // پاکسازی تایمر
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  // وقتی مسیر تغییر می‌کند، لودینگ را متوقف می‌کنیم
  useEffect(() => {
    if (isLoading) {
      const checkIfPageLoaded = () => {
        // اگر صفحه کاملاً بارگذاری شده است
        if (document.readyState === "complete") {
          // محاسبه زمان سپری شده از شروع ناوبری
          const elapsedTime = navigationStartTimeRef.current
            ? Date.now() - navigationStartTimeRef.current
            : 0;

          // اگر زمان کافی گذشته باشد، لودینگ را فوراً متوقف می‌کنیم
          // در غیر این صورت، حداقل زمان نمایش را رعایت می‌کنیم (برای جلوگیری از فلش سریع)
          if (elapsedTime >= MIN_LOADING_TIME) {
            setIsLoading(false);
            navigationStartTimeRef.current = null;
          } else {
            // فقط اگر زمان قابل توجهی باقی مانده باشد، تایمر تنظیم می‌کنیم
            const remainingTime = MIN_LOADING_TIME - elapsedTime;
            if (remainingTime > 50) {
              setTimeout(() => {
                setIsLoading(false);
                navigationStartTimeRef.current = null;
              }, remainingTime);
            } else {
              // اگر زمان باقی‌مانده کم است، فوراً متوقف می‌کنیم
              setIsLoading(false);
              navigationStartTimeRef.current = null;
            }
          }
        } else {
          // اگر صفحه هنوز در حال بارگذاری است، با فاصله کمتری دوباره بررسی می‌کنیم
          setTimeout(checkIfPageLoaded, 30);
        }
      };

      checkIfPageLoaded();
    }

    // پاکسازی تایمر هنگام تغییر مسیر
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
    };
  }, [pathname, searchParams, isLoading]);

  return isLoading ? <LoadingIndicator /> : null;
}
