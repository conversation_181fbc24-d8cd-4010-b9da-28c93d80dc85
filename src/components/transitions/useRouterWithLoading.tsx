"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";

export function useRouterWithLoading() {
  const router = useRouter();

  // Override push method with loading event
  const push = useCallback(
    (href: string, options?: { scroll?: boolean }) => {
      // Dispatch custom event for loading start
      window.dispatchEvent(new Event("route-change-start"));

      // Navigate to new page
      router.push(href, options);
    },
    [router]
  );

  // Override replace method with loading event
  const replace = useCallback(
    (href: string, options?: { scroll?: boolean }) => {
      // Dispatch custom event for loading start
      window.dispatchEvent(new Event("route-change-start"));

      // Navigate to new page
      router.replace(href, options);
    },
    [router]
  );

  return {
    ...router,
    push,
    replace,
  };
}
