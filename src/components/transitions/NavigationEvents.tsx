"use client";
import { useEffect, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";

export function NavigationEvents({
  onRouteChangeStart,
  onRouteChangeComplete,
}: {
  onRouteChangeStart: () => void;
  onRouteChangeComplete: () => void;
}) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Store previous values to detect actual changes
  const prevPathnameRef = useRef(pathname);

  // Flag to prevent execution on first render
  const isFirstRender = useRef(true);

  // Flag to track if we're currently in a route transition
  const isTransitioning = useRef(false);

  // Convert searchParams to string for comparison
  const searchParamsString = searchParams
    ? Array.from(searchParams.entries()).toString()
    : "";
  const prevSearchParamsString = useRef(searchParamsString);

  // Detect route change and show loader
  useEffect(() => {
    // Skip on first render
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Check for actual route change
    if (
      prevPathnameRef.current !== pathname ||
      prevSearchParamsString.current !== searchParamsString
    ) {
      // Prevent multiple triggers during the same transition
      if (!isTransitioning.current) {
        isTransitioning.current = true;

        // Show loader before route change
        onRouteChangeStart();

        // Update previous values
        prevPathnameRef.current = pathname;
        prevSearchParamsString.current = searchParamsString;

        // Check if page is fully loaded
        const checkIfPageLoaded = () => {
          // If document is fully loaded and DOM is ready
          if (document.readyState === "complete") {
            onRouteChangeComplete();
            isTransitioning.current = false;
          } else {
            // Otherwise check again after a short delay
            setTimeout(checkIfPageLoaded, 100);
          }
        };

        // Start checking if page is loaded
        // Use a small delay to ensure the route change has started
        setTimeout(checkIfPageLoaded, 50);
      }
    }
  }, [pathname, searchParamsString, onRouteChangeStart, onRouteChangeComplete]);

  // Add listener for navigation with browser back/forward buttons
  useEffect(() => {
    const handlePopState = () => {
      if (!isTransitioning.current) {
        isTransitioning.current = true;
        onRouteChangeStart();

        // Check if page is loaded after popstate
        setTimeout(() => {
          const checkIfPageLoaded = () => {
            if (document.readyState === "complete") {
              onRouteChangeComplete();
              isTransitioning.current = false;
            } else {
              setTimeout(checkIfPageLoaded, 100);
            }
          };
          checkIfPageLoaded();
        }, 50);
      }
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [onRouteChangeStart, onRouteChangeComplete]);

  // Add listener for page unload (when navigating away from the site)
  useEffect(() => {
    const handleBeforeUnload = () => {
      onRouteChangeStart();
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [onRouteChangeStart]);

  // Add listener for clicks on links to detect client-side navigation
  useEffect(() => {
    const handleLinkClick = (e: MouseEvent) => {
      // Check if the clicked element is a link
      const link = (e.target as HTMLElement).closest("a");

      if (
        link &&
        link.href &&
        link.href.startsWith(window.location.origin) &&
        !link.target &&
        !link.hasAttribute("download") &&
        !e.ctrlKey &&
        !e.metaKey &&
        !e.shiftKey
      ) {
        // This is likely a client-side navigation
        if (!isTransitioning.current) {
          isTransitioning.current = true;
          onRouteChangeStart();
          // The route change will be detected by the pathname/searchParams effect
        }
      }
    };

    document.addEventListener("click", handleLinkClick);

    return () => {
      document.removeEventListener("click", handleLinkClick);
    };
  }, [onRouteChangeStart]);

  return null;
}
