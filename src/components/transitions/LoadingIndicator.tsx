"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

export default function LoadingIndicator() {
  const [progress, setProgress] = useState(0);

  // Generate random particles - کاهش تعداد ذرات برای بهبود عملکرد
  const particles = Array.from({ length: 6 }).map((_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 10 + 5,
    duration: Math.random() * 0.8 + 0.8, // کاهش بیشتر مدت زمان انیمیشن
    delay: Math.random() * 0.2, // کاهش بیشتر تأخیر
  }));

  useEffect(() => {
    // Simulate loading progress - افزایش سرعت پیشرفت برای نمایش سریع‌تر
    const interval = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress < 100) {
          return prevProgress + Math.random() * 8 + 5; // افزایش بیشتر سرعت پیشرفت
        } else {
          clearInterval(interval);
          return 100;
        }
      });
    }, 20); // کاهش بیشتر فاصله زمانی بین به‌روزرسانی‌ها

    return () => clearInterval(interval);
  }, []);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }} // کاهش مدت زمان انیمیشن
        className="fixed inset-0 z-[9999] flex flex-col items-center justify-center overflow-hidden"
      >
        {/* Background with gradient and blur */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-b from-[#18191D]/90 to-[#1C1E24]/90 backdrop-filter backdrop-blur-lg"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }} // کاهش مدت زمان انیمیشن
        />

        {/* Animated particles */}
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full bg-blue-500/30"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: particle.size,
              height: particle.size,
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              opacity: [0, 0.8, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: particle.duration,
              repeat: Infinity,
              delay: particle.delay,
              ease: "easeInOut",
            }}
          />
        ))}

        {/* Center logo/brand animation */}
        <div className="relative z-10 flex flex-col items-center">
          {/* Animated rings */}
          <div className="relative w-32 h-32 mb-8">
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-blue-500/20"
              animate={{ scale: [0.8, 1.2, 0.8], opacity: [0.2, 0.5, 0.2] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }} // کاهش مدت زمان انیمیشن
            />
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-blue-400/40"
              animate={{ scale: [1, 1.3, 1], opacity: [0.4, 0.6, 0.4] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.1 }} // کاهش مدت زمان انیمیشن و تأخیر
            />

            {/* Main spinner */}
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }} // کاهش مدت زمان چرخش
            >
              <motion.div
                className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full"
                animate={{
                  boxShadow: [
                    "0 0 10px rgba(59, 130, 246, 0.5)",
                    "0 0 20px rgba(59, 130, 246, 0.7)",
                    "0 0 10px rgba(59, 130, 246, 0.5)"
                  ]
                }}
                transition={{ duration: 1, repeat: Infinity }} // کاهش مدت زمان انیمیشن
              />
            </motion.div>

            {/* Inner pulsing circle */}
            <motion.div
              className="absolute inset-0 m-auto w-8 h-8 bg-blue-500 rounded-full"
              style={{ left: 0, right: 0, top: 0, bottom: 0 }}
              animate={{
                scale: [0.8, 1.2, 0.8],
                opacity: [0.7, 1, 0.7],
                boxShadow: [
                  "0 0 10px rgba(59, 130, 246, 0.5)",
                  "0 0 30px rgba(59, 130, 246, 0.8)",
                  "0 0 10px rgba(59, 130, 246, 0.5)"
                ]
              }}
              transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }} // کاهش مدت زمان انیمیشن
            />
          </div>

          {/* Animated text */}
          <motion.p
            className="text-white text-lg font-medium mb-8"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }} // کاهش مدت زمان انیمیشن
          >
            در حال بارگذاری...
          </motion.p>
        </div>

        {/* Advanced progress bar */}
        <motion.div
          className="absolute bottom-10 w-[80%] max-w-md bg-gray-800/50 h-2 rounded-full overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <motion.div
            className="h-full bg-gradient-to-r from-blue-600 to-blue-400 rounded-full"
            style={{ width: `${Math.min(progress, 100)}%` }}
            initial={{ width: "0%" }}
            transition={{ ease: "easeOut" }}
          />

          {/* Glow effect on progress bar */}
          <motion.div
            className="absolute top-0 h-full w-20 bg-gradient-to-r from-transparent via-blue-400/50 to-transparent"
            animate={{ x: [-100, 500] }}
            transition={{
              duration: 1, // کاهش مدت زمان انیمیشن
              repeat: Infinity,
              ease: "linear",
              repeatDelay: 0.2 // کاهش تأخیر
            }}
          />
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}