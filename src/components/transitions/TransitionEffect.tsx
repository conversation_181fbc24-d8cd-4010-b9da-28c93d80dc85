"use client";
import { motion, AnimatePresence } from "framer-motion";
import { usePathname, useSearchParams } from "next/navigation";
import { useEffect, useState, useRef } from "react";

const TransitionEffect = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isAnimating, setIsAnimating] = useState(false);
  const prevPathRef = useRef(pathname);
  const prevSearchParamsRef = useRef(searchParams?.toString() || "");
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // حداکثر زمان انتظار قبل از نمایش انیمیشن (میلی‌ثانیه)
  const ANIMATION_DELAY = 100; // افزایش برای جلوگیری از نمایش انیمیشن در ناوبری‌های سریع
  // حداکثر زمان نمایش انیمیشن (میلی‌ثانیه) - کاهش برای سرعت بیشتر
  const MAX_ANIMATION_TIME = 500;

  // Generate hexagons for the grid effect - کاهش تعداد و مدت زمان برای بهبود عملکرد
  const hexagons = Array.from({ length: 20 }).map((_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 30 + 20,
    delay: Math.random() * 0.3, // کاهش تأخیر
    duration: Math.random() * 0.5 + 0.8, // کاهش مدت زمان انیمیشن
  }));

  useEffect(() => {
    // بررسی می‌کنیم که آیا مسیر واقعاً تغییر کرده است
    const currentSearchParams = searchParams?.toString() || "";

    if (prevPathRef.current !== pathname || prevSearchParamsRef.current !== currentSearchParams) {
      // به‌روزرسانی مقادیر قبلی
      prevPathRef.current = pathname;
      prevSearchParamsRef.current = currentSearchParams;

      // پاکسازی تایمرهای قبلی
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }

      // استفاده از تایمر برای تأخیر در نمایش انیمیشن
      // این باعث می‌شود در ناوبری‌های سریع، انیمیشن نمایش داده نشود
      if (ANIMATION_DELAY > 0) {
        loadingTimeoutRef.current = setTimeout(() => {
          // بررسی وضعیت بارگذاری صفحه
          if (document.readyState !== "complete") {
            // فقط اگر صفحه هنوز بارگذاری نشده باشد، انیمیشن را نمایش می‌دهیم
            setIsAnimating(true);

            // تنظیم حداکثر زمان نمایش انیمیشن
            animationTimeoutRef.current = setTimeout(() => {
              setIsAnimating(false);
            }, MAX_ANIMATION_TIME);
          }
        }, ANIMATION_DELAY);
      } else {
        // اگر تأخیر نداریم، فوراً انیمیشن را نمایش می‌دهیم
        setIsAnimating(true);

        // تنظیم حداکثر زمان نمایش انیمیشن
        animationTimeoutRef.current = setTimeout(() => {
          setIsAnimating(false);
        }, MAX_ANIMATION_TIME);
      }
    }

    // پاکسازی تایمرها
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, [pathname, searchParams]);

  // اضافه کردن یک useEffect جدید برای بررسی وضعیت بارگذاری صفحه
  useEffect(() => {
    if (isAnimating) {
      // تابع برای بررسی وضعیت بارگذاری صفحه
      const checkPageLoaded = () => {
        if (document.readyState === "complete") {
          // اگر صفحه کاملاً بارگذاری شده است، انیمیشن را با تأخیر کوتاهی متوقف می‌کنیم
          // تأخیر کوتاه‌تر برای سرعت بیشتر
          setTimeout(() => {
            setIsAnimating(false);
          }, 100);
        } else {
          // اگر صفحه هنوز در حال بارگذاری است، با فاصله کمتری دوباره بررسی می‌کنیم
          setTimeout(checkPageLoaded, 30);
        }
      };

      // شروع بررسی وضعیت بارگذاری
      checkPageLoaded();
    }
  }, [isAnimating]);

  return (
    <AnimatePresence>
      {isAnimating && (
        <motion.div
          className="fixed inset-0 z-50 pointer-events-none overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }} // کاهش مدت زمان انیمیشن
        >
          {/* Background with gradient overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-[#18191D]/95 via-[#1C1E24]/95 to-[#18191D]/95 backdrop-filter backdrop-blur-md"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }} // کاهش مدت زمان انیمیشن
          />

          {/* Animated hexagon grid */}
          {hexagons.map((hex) => (
            <motion.div
              key={hex.id}
              className="absolute bg-blue-500/10 rounded-md"
              style={{
                left: `${hex.x}%`,
                top: `${hex.y}%`,
                width: hex.size,
                height: hex.size,
              }}
              initial={{ opacity: 0, scale: 0, rotate: 0 }}
              animate={{
                opacity: [0, 0.3, 0],
                scale: [0, 1, 0],
                rotate: [0, 90, 180],
              }}
              transition={{
                duration: hex.duration,
                delay: hex.delay,
                ease: "easeInOut",
              }}
            />
          ))}

          {/* Top loading bar with glow effect */}
          <div className="fixed top-0 left-0 right-0 h-1 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-blue-600 via-blue-400 to-blue-600"
              initial={{ scaleX: 0, originX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{
                duration: 2,
                ease: "easeInOut",
              }}
              style={{ boxShadow: "0 0 10px rgba(59, 130, 246, 0.7)" }}
            />
          </div>

          {/* Bottom loading bar */}
          <div className="fixed bottom-0 left-0 right-0 h-1 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-blue-600 via-blue-400 to-blue-600"
              initial={{ scaleX: 0, originX: 1 }}
              animate={{ scaleX: 1 }}
              transition={{
                duration: 2,
                ease: "easeInOut",
              }}
              style={{ transformOrigin: "right", boxShadow: "0 0 10px rgba(59, 130, 246, 0.7)" }}
            />
          </div>

          {/* Center animation */}
          <motion.div
            className="fixed inset-0 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* Logo container with rings */}
            <div className="relative">
              {/* Outer rings */}
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-blue-500/30"
                initial={{ scale: 0.8 }}
                animate={{
                  scale: [0.8, 1.5, 0.8],
                  opacity: [0, 0.5, 0],
                  rotate: [0, 180, 360]
                }}
                transition={{
                  duration: 1.2, // کاهش مدت زمان انیمیشن
                  ease: "easeInOut",
                  times: [0, 0.5, 1]
                }}
                style={{ width: "100%", height: "100%" }}
              />

              <motion.div
                className="absolute inset-0 rounded-full border-2 border-blue-400/20"
                initial={{ scale: 0.8 }}
                animate={{
                  scale: [0.8, 1.3, 0.8],
                  opacity: [0, 0.3, 0],
                  rotate: [0, -180, -360]
                }}
                transition={{
                  duration: 1.2, // کاهش مدت زمان انیمیشن
                  ease: "easeInOut",
                  times: [0, 0.5, 1],
                  delay: 0.1
                }}
                style={{ width: "100%", height: "100%" }}
              />

              {/* Main spinner with glow */}
              <motion.div
                className="relative w-16 h-16 flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, ease: "linear" }} // کاهش مدت زمان انیمیشن
              >
                <motion.div
                  className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent"
                  animate={{
                    boxShadow: [
                      "0 0 5px rgba(59, 130, 246, 0.3)",
                      "0 0 15px rgba(59, 130, 246, 0.7)",
                      "0 0 5px rgba(59, 130, 246, 0.3)"
                    ]
                  }}
                  transition={{ duration: 0.8, repeat: 1, ease: "easeInOut" }} // کاهش مدت زمان انیمیشن
                />

                {/* Inner circle pulse */}
                <motion.div
                  className="w-6 h-6 bg-blue-500 rounded-full"
                  animate={{
                    scale: [0.8, 1.2, 0.8],
                    opacity: [0.7, 1, 0.7],
                    boxShadow: [
                      "0 0 5px rgba(59, 130, 246, 0.5)",
                      "0 0 15px rgba(59, 130, 246, 0.8)",
                      "0 0 5px rgba(59, 130, 246, 0.5)"
                    ]
                  }}
                  transition={{ duration: 0.8, repeat: 1, ease: "easeInOut" }} // کاهش مدت زمان انیمیشن
                />
              </motion.div>
            </div>
          </motion.div>

          {/* Side bars */}
          <motion.div
            className="fixed left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-600 via-blue-400 to-blue-600"
            initial={{ scaleY: 0 }}
            animate={{ scaleY: 1 }}
            transition={{ duration: 0.8, ease: "easeInOut" }} // کاهش مدت زمان انیمیشن
            style={{ boxShadow: "0 0 10px rgba(59, 130, 246, 0.7)" }}
          />

          <motion.div
            className="fixed right-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-600 via-blue-400 to-blue-600"
            initial={{ scaleY: 0 }}
            animate={{ scaleY: 1 }}
            transition={{ duration: 0.8, ease: "easeInOut" }} // کاهش مدت زمان انیمیشن
            style={{ boxShadow: "0 0 10px rgba(59, 130, 246, 0.7)" }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TransitionEffect;
