"use client";
import { motion, AnimatePresence } from "framer-motion";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

interface PageTransitionProps {
  children: ReactNode;
}

const PageTransition = ({ children }: PageTransitionProps) => {
  const pathname = usePathname();

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pathname}
        initial={{ opacity: 0, y: 20 }} // کاهش فاصله برای انیمیشن سریع‌تر
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }} // کاهش فاصله برای انیمیشن سریع‌تر
        transition={{
          type: "tween",
          ease: "easeOut", // تغییر به easeOut برای شروع سریع‌تر
          duration: 0.3 // کاهش مدت زمان انیمیشن
        }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

export default PageTransition;
