import { getUserCurrency } from "@/requests/dashboardRequest";
import Image from "next/image";
import React, { useEffect, useState } from "react";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  name: string;
  id: number;
}

interface CryptoModaltModalProps {
  setIsCryptoModal: React.Dispatch<React.SetStateAction<boolean>>;
  setCurrencyId: React.Dispatch<React.SetStateAction<number>>;
  setCurrency: React.Dispatch<React.SetStateAction<string>>;
  userCurrency: CurrencyItem[];
}

const CryptoModal: React.FC<CryptoModaltModalProps> = ({
  setIsCryptoModal,
  setCurrencyId,
  setCurrency,
  userCurrency,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCurrencies, setFilteredCurrencies] = useState<CurrencyItem[]>(
    []
  );

  useEffect(() => {
    // Simulate loading for demonstration
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (userCurrency) {
      if (searchTerm.trim() === "") {
        setFilteredCurrencies(userCurrency);
      } else {
        const filtered = userCurrency.filter(
          (item) =>
            item.coin_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredCurrencies(filtered);
      }
    }
  }, [searchTerm, userCurrency]);

  // Loading spinner component
  const LoadingSpinner = () => (
    <div className="flex justify-center items-center py-10">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-[100] p-4">
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] w-full max-w-[90vw] sm:max-w-[400px] md:max-w-[500px] p-5 rounded-xl overflow-y-auto max-h-[90vh] shadow-2xl border border-[#353945]/50 animate-fadeIn relative">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none rounded-xl"></div>

        {/* Animated background elements */}
        <div className="absolute top-10 right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-10 w-20 h-20 bg-purple-500/10 rounded-full blur-xl"></div>

        <div className="flex justify-between items-center mb-5 relative z-10">
          <div className="flex items-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-2 rounded-lg mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-lg font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              انتخاب رمز ارز
            </h2>
          </div>
          <button
            onClick={() => setIsCryptoModal(false)}
            className="bg-[#23262F] p-2 rounded-lg hover:bg-[#2A2D38] transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-gray-400 hover:text-white transition-colors"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="mb-5 relative z-10">
          <div className="relative">
            <input
              type="text"
              placeholder="جستجوی نام یا نماد ارز..."
              className="w-full bg-[#23262F] border border-[#353945]/50 focus:border-blue-500/50 rounded-lg p-3 pr-10 text-sm outline-none transition-colors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
        </div>

        <div className="relative z-10 max-h-[50vh] overflow-y-auto pr-1 custom-scrollbar">
          {isLoading ? (
            <LoadingSpinner />
          ) : filteredCurrencies.length > 0 ? (
            <div className="space-y-2">
              {filteredCurrencies.map((item, index) => (
                <div
                  onClick={() => {
                    setCurrencyId(item.id);
                    setIsCryptoModal(false);
                    setCurrency(item.coin_type);
                  }}
                  key={index}
                  className="flex items-center justify-between p-3 bg-[#23262F]/70 hover:bg-[#2A2D38] rounded-lg cursor-pointer border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 hover:shadow-md group"
                >
                  <div className="flex items-center">
                    <div className="bg-[#353945]/50 p-1.5 rounded-full mr-3 group-hover:bg-blue-500/20 transition-colors">
                      <Image
                        className="w-6 h-6 sm:w-7 sm:h-7"
                        src={`https://api.exchangim.com/storage/${item.coin_icon}`}
                        height={1000}
                        width={1000}
                        alt={item.name}
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        {item.coin_type}
                      </p>
                      <p className="text-xs text-gray-400">
                        {item.name}
                      </p>
                    </div>
                  </div>
                  <div className="text-right bg-[#1A1D21]/70 py-1 px-2 rounded">
                    <p className="text-xs font-medium">
                      {Number(item.coin_price).toFixed(2)} $
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="bg-[#353945]/30 p-3 rounded-full mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-gray-400 text-sm">هیچ ارزی با این مشخصات یافت نشد</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CryptoModal;
