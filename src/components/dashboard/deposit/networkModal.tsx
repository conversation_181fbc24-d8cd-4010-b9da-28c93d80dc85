import { getUserCurrency } from "@/requests/dashboardRequest";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";

interface NetworkModalProps {
  setIsNetworkModal: React.Dispatch<React.SetStateAction<boolean>>;
  setNetworkId: React.Dispatch<React.SetStateAction<number>>;
  setNetwork: React.Dispatch<React.SetStateAction<string>>;
  currencyId: number;
}

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  name: string;
  id: number;
  coin_network: {
    network_id: number;
    network: {
      name: string;
    };
  }[];
}

const NetworkModal: React.FC<NetworkModalProps> = ({
  setIsNetworkModal,
  currencyId,
  setNetworkId,
  setNetwork,
}) => {
  const [userCurrency, setUserCurrency] = useState<CurrencyItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredNetworks, setFilteredNetworks] = useState<any[]>([]);

  useEffect(() => {
    getCurrencyHandler();
  }, []);

  const getCurrencyHandler = async () => {
    try {
      setIsLoading(true);
      const result = await getUserCurrency();
      setUserCurrency(result.data);
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات شبکه‌ها");
    } finally {
      setIsLoading(false);
    }
  };

  const filteredData = userCurrency.find((item) => item.id === currencyId);

  useEffect(() => {
    if (filteredData?.coin_network) {
      if (searchTerm.trim() === "") {
        setFilteredNetworks(filteredData.coin_network);
      } else {
        const filtered = filteredData.coin_network.filter((network) =>
          network.network.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredNetworks(filtered);
      }
    }
  }, [searchTerm, filteredData]);

  // Loading spinner component
  const LoadingSpinner = () => (
    <div className="flex justify-center items-center py-10">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#18191D] w-full max-w-[90vw] sm:max-w-[400px] md:max-w-[500px] p-4 sm:p-6 rounded-2xl overflow-y-auto max-h-[90vh]">
        <div className="flex justify-between border-b border-[#353945] pb-4">
          <span className="text-sm sm:text-base">
            لطفا شبکه واریز مورد نظر را انتخاب کنید
          </span>
          <Image
            onClick={() => setIsNetworkModal(false)}
            className="w-4 h-4 sm:w-5 sm:h-5 cursor-pointer"
            src="/images/close.svg"
            height={1000}
            width={1000}
            alt="close"
          />
        </div>
        <div className="flex items-center mt-4 bg-[#1C1E24] p-2 sm:p-3 rounded-lg">
          <Image
            className="w-5 h-5 sm:w-6 sm:h-6 bg-[#F6BA47] rounded-full flex-shrink-0"
            src="/images/taajob.png"
            height={1000}
            width={1000}
            alt="warning"
          />
          <p className="text-xs sm:text-sm mr-3 sm:mr-5">
            مطمئن شوید که رمز ارز انتخابی شما و شبکه واریز مطابقت دارند، در غیر
            این صورت ممکن است دارایی های شما از دست بروند.
          </p>
        </div>
        <div className="bg-[#23262F] flex items-center gap-x-2 p-2 rounded-lg mt-4">
          <Image
            className="w-4 h-4 sm:w-5 sm:h-5"
            src="/images/search.svg"
            height={1000}
            width={1000}
            alt="search"
          />
          <input
            type="text"
            className="w-full outline-none bg-transparent text-sm"
            placeholder="جست و جوی شبکه"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="mt-6 sm:mt-8 space-y-3 sm:space-y-4">
          {isLoading ? (
            <LoadingSpinner />
          ) : filteredNetworks && filteredNetworks.length > 0 ? (
            filteredNetworks.map((network, index) => (
              <div
                onClick={() => {
                  setNetworkId(network.network_id);
                  setNetwork(network.network.name);
                  setIsNetworkModal(false);
                }}
                key={index}
                className="flex flex-col gap-x-2 w-full cursor-pointer hover:bg-[#23262F] p-2 rounded-lg transition-colors"
              >
                <p className="text-right text-sm sm:text-base">
                  {network.network?.name}
                </p>
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-[#B1B5C3]">
              {currencyId
                ? "هیچ شبکه‌ای یافت نشد"
                : "لطفا ابتدا یک ارز انتخاب کنید"}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NetworkModal;
