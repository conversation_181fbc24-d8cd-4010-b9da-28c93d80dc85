"use client";
import BTN from "@/components/form/BTN";
import { sendTicket } from "@/requests/dashboardRequest";
import Image from "next/image";
import React, { useState } from "react";
import toast from "react-hot-toast";

const AddTicket = ({
  setIsOpen,
  getTicketsHandler,
}: {
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  getTicketsHandler: () => void;
}) => {
  const [subject, setSubject] = useState<string>("");
  const [message, setMessage] = useState<string>("");
  const [file, setFile] = useState<File | null>(null);
  const [unit_id, setUnit_id] = useState<number>(1);
  const [level_id, setLevel_id] = useState<number>(1);
  const [selectedFileName, setSelectedFileName] = useState<string>("");

  const submitForm = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!subject || !message || !unit_id || !level_id) {
      toast.error("پر کردن همه فیلدهای اجباری است");
      return;
    }

    const formData = new FormData();
    formData.append("subject", subject);
    formData.append("message", message);
    formData.append("unit_id", unit_id.toString());
    formData.append("level_id", level_id.toString());
    if (file) {
      formData.append("file", file);
    }

    const result = await sendTicket(formData);
    if (result.isError) {
      toast.error("خطایی رخ دادس");
    } else {
      toast.success("تیکت با موفقیت ارسال شد");
      setIsOpen(false);
      getTicketsHandler();
    }
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    setFile(selectedFile);
    setSelectedFileName(selectedFile ? selectedFile.name : "");
  };

  return (
    <div className="fixed inset-0 backdrop-blur-sm bg-transparent z-10 flex items-center justify-center">
      <div className="w-[90%] sm:w-[85%] md:w-[80%] lg:w-[75%] max-w-[1200px] max-h-[90vh] overflow-y-auto bg-[#18191D] px-4 py-9 sm:p-5 md:p-6 rounded-2xl">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <h2 className="text-xl sm:text-2xl">ایجاد تیکت جدید</h2>
            <p className="text-sm sm:text-base">
              با پر کردن فرم های زیر درخواست جدید پشتیبانی ثبت کنید
            </p>
          </div>
          <div
            onClick={() => setIsOpen(false)}
            className="bg-[#23262F] rounded-lg p-2 cursor-pointer self-end sm:self-auto"
          >
            <Image
              className="w-5 h-5"
              src="/images/arrow-left.svg"
              height={1000}
              width={1000}
              alt="back"
            />
          </div>
        </div>

        <div className="relative">
          <p className="text-sm mt-8 mb-4 bg-[#1C1E24] py-3 px-5 rounded-lg relative overflow-hidden text-[#FCEAC8]">
            لطفا تا پاسخ تیکت های قبلی از ایجاد تیکت های تکراری خودداری نمایید.
            این کار باعث تاخیر بیشتر در روند پاسخگویی می شود.
          </p>
        </div>

        <form onSubmit={submitForm} className="mt-6 space-y-6">
          <div className="flex flex-col md:flex-row gap-4 md:gap-x-3">
            <fieldset className="flex w-full md:w-1/3 relative">
              <label className="absolute -top-3 right-3  px-1 font-light text-sm">
                سطح اولویت
              </label>
              <select
                value={level_id}
                onChange={(e) => setLevel_id(Number(e.target.value))}
                dir="ltr"
                className="w-full bg-[#23262F] p-3 rounded-md text-[#777E90] border-2 border-[#353945]"
              >
                <option value="1">عادی</option>
                <option value="2">متوسط</option>
                <option value="3">فوری</option>
              </select>
            </fieldset>

            <fieldset className="flex w-full md:w-1/3 relative">
              <label className="absolute -top-3 right-3  px-1 font-light text-sm">
                واحد مربوطه
              </label>
              <select
                dir="ltr"
                value={unit_id}
                onChange={(e) => setUnit_id(Number(e.target.value))}
                className="w-full bg-[#23262F] p-3 rounded-md text-[#777E90] border-2 border-[#353945]"
              >
                <option value="1">احراز هویت</option>
                <option value="2">مالی</option>
                <option value="3">سایر</option>
              </select>
            </fieldset>

            <fieldset className="flex w-full md:w-1/3 relative">
              <label className="absolute -top-3 right-3  px-1 font-light text-sm">
                موضوع تیکت
              </label>
              <input
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                className="w-full bg-[#23262F] p-3 rounded-md text-right text-[#777E90] border-2 border-[#353945]"
                type="text"
                placeholder="خلاصه‌ای از مشکل خود را وارد کنید"
              />
            </fieldset>
          </div>

          <div className="relative">
            <label className="absolute -top-3 right-3  px-1 font-light text-sm">
              متن پیام
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={8}
              className="w-full bg-[#23262F] p-3 rounded-md resize-none border-2 border-[#353945]"
              placeholder="توضیحات کامل مشکل خود را اینجا بنویسید..."
            ></textarea>
          </div>

          <div className="relative">
            <span className="absolute -top-3 right-3  px-1 font-light text-sm">
              افزودن فایل
            </span>
            <div className="w-full bg-[#23262F] p-3 rounded-md text-right border-2 border-[#353945] flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
              <div className="flex items-center gap-2">
                <label htmlFor="file" className="cursor-pointer">
                  <Image
                    className="w-5 h-5"
                    src="/images/file.svg"
                    height={1000}
                    width={1000}
                    alt="file"
                  />
                </label>
                <span className="text-[#777E90] text-sm">
                  {selectedFileName || "فایلی انتخاب نشده"}
                </span>
              </div>
              <p className="text-[#777E90] text-xs sm:text-sm">
                نهایتا ۲۰ مگابات و فقط JPG PNG PDF
              </p>
              <input
                id="file"
                onChange={handleFileChange}
                hidden
                className="w-full"
                type="file"
                accept=".jpg,.jpeg,.png,.pdf"
              />
            </div>
          </div>

          <div className="flex justify-end">
            <BTN
              type="submit"
              label="تایید"
              className="bg-green-700 px-8 sm:px-12 py-1.5 rounded-2xl text-white cursor-pointer"
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddTicket;
