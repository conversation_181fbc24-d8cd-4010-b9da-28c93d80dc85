"use client";

import { getProfile } from "@/requests/dashboardRequest";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";

interface IUserProfile {
  national_id?: string;
  firstname?: string;
  lastname?: string;
  birth_date?: string;
}

interface InfoItemProps {
  iconSrc: string;
  label: string;
  value?: string;
}

const InfoItem: React.FC<InfoItemProps> = ({ iconSrc, label, value }) => (
  <div className="flex justify-between items-center w-full">
    <div className="flex gap-x-2">
      <p>{label}</p>
      <Image
        className="w-6 h-6"
        src={iconSrc}
        height={24}
        width={24}
        alt={label}
      />
    </div>
    <p>{value || "-"}</p>
  </div>
);

const UserinfoTab: React.FC = () => {
  const [info, setInfo] = useState<IUserProfile>({});

  useEffect(() => {
    getProfileHandler();
  }, []);

  async function getProfileHandler() {
    const result = await getProfile();
    if (result.isError) {
      toast.error("خطایی رخ داد");
    } else {
      setInfo(result.data);
    }
  }

  return (
    <div>
      <h1 className="text-2xl font-medium mb-8 text-center">اطلاعات شخصی</h1>

      {/* Desktop View */}
      <div className="hidden md:block landscape:block">
        <div className="bg-[#18191D] rounded-lg py-6 px-8">
          <div className="flex justify-start gap-2.5 items-start text-right mb-8">
            <div>
              <Image
                className="w-12 h-12"
                src="/images/auth-glass.png"
                height={48}
                width={48}
                alt="avatar"
              />
              <p className="text-[#FCEAC8] text-xl">
                احراز هویت شما در دست بررسی می باشد.
              </p>
              <p className="text-[#FCEAC8] text-xs">
                بعد از تایید آن میتوانید خرید و فروش بیشتری انجام دهید.
              </p>
            </div>
          </div>

          <div className="flex flex-col items-end gap-y-8">
            <InfoItem
              iconSrc="/images/national-code.svg"
              label="کد ملی"
              value={info.national_id}
            />
            <InfoItem
              iconSrc="/images/user-info.svg"
              label="نام و نام خانوادگی"
              value={
                info.firstname && info.lastname
                  ? `${info.firstname} ${info.lastname}`
                  : undefined
              }
            />
            <InfoItem
              iconSrc="/images/birthdate.svg"
              label="تاریخ تولد"
              value={info.birth_date}
            />
          </div>
        </div>
      </div>

      {/* Mobile View */}
      <div className="md:hidden landscape:hidden min-h-screen">
        <div className="bg-[#18191D] p-4 rounded-lg w-[80vw] max-w-[550px] mx-auto">
          <div className="flex flex-col items-center text-center mb-6">
            <Image
              className="w-12 h-12 mb-2"
              src="/images/auth-glass.png"
              height={48}
              width={48}
              alt="avatar"
            />
            <p className="text-[#FCEAC8] text-lg">
              احراز هویت شما در دست بررسی می باشد.
            </p>
            <p className="text-[#FCEAC8] text-xs">
              بعد از تایید آن میتوانید خرید و فروش بیشتری انجام دهید.
            </p>
          </div>

          <div className="space-y-6">
            <MobileInfoItem
              iconSrc="/images/national-code.svg"
              label="کد ملی"
              value={info.national_id}
            />
            <MobileInfoItem
              iconSrc="/images/user-info.svg"
              label="نام و نام خانوادگی"
              value={
                info.firstname && info.lastname
                  ? `${info.firstname} ${info.lastname}`
                  : undefined
              }
            />
            <MobileInfoItem
              iconSrc="/images/birthdate.svg"
              label="تاریخ تولد"
              value={info.birth_date}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const MobileInfoItem: React.FC<InfoItemProps> = ({ iconSrc, label, value }) => (
  <div className="flex flex-col items-center text-center">
    <p className="text-[#FCFCFD]">{value || "-"}</p>
    <div className="flex items-center gap-x-2 mb-2">
      <span className="text-[#B1B5C3]">{label}:</span>
      <Image
        className="w-6 h-6"
        src={iconSrc}
        height={24}
        width={24}
        alt={label}
      />
    </div>
  </div>
);

export default UserinfoTab;
