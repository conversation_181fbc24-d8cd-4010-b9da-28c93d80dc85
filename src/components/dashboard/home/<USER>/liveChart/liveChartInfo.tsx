import Image from "next/image";
import React from "react";

const LiveChartInfo = () => {
  return (
    <>
      <div className="w-full bg-[#23262F] p-1.5 rounded-lg">
        <button className="text-white w-1/2 rounded-lg p-1.5">برداشت</button>
        <button className="w-1/2 bg-white rounded-lg p-1.5 text-black">
          واریز
        </button>
      </div>
      <div className="w-full"> 
        <div className="bg-[#23262F] flex items-center gap-x-2 p-2 rounded-lg mt-5">
          <Image
            className="w-5 h-5"
            src="/images/search.svg"
            height={1000}
            width={1000}
            alt="avatar"
          />
          <input
            type="text"
            className="w-full outline-none"
            placeholder="جست و جو"
          />
        </div>
        <div className="mt-10 space-y-4">
          <div className="flex justify-between">
            <div>
              <p>0.00</p>
              <p className="text-[#B1B5C3] text-[14px]"> 0.00 USDT = </p>
            </div>
            <div className="flex items-center gap-x-2">
              <div>
                <p className="text-left">ADA</p>
                <p className="text-left text-[#B1B5C3] text-[14px]">Cardano</p>
              </div>
              <Image
                className="w-8 h-8"
                src="/images/ada.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
            </div>
          </div>
          <div className="flex justify-between">
            <div>
              <p>0.00</p>
              <p className="text-[#B1B5C3] text-[14px]"> 0.00 USDT = </p>
            </div>
            <div className="flex items-center gap-x-2">
              <div>
                <p className="text-left">ADA</p>
                <p className="text-left text-[#B1B5C3] text-[14px]">Cardano</p>
              </div>
              <Image
                className="w-8 h-8"
                src="/images/eth.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
            </div>
          </div>
          <div className="flex justify-between">
            <div>
              <p>0.00</p>
              <p className="text-[#B1B5C3] text-[14px]"> 0.00 USDT = </p>
            </div>
            <div className="flex items-center gap-x-2">
              <div>
                <p className="text-left">ADA</p>
                <p className="text-left text-[#B1B5C3] text-[14px]">Cardano</p>
              </div>
              <Image
                className="w-8 h-8"
                src="/images/btc.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
            </div>
          </div>
          <div className="flex justify-between">
            <div>
              <p>0.00</p>
              <p className="text-[#B1B5C3] text-[14px]"> 0.00 USDT = </p>
            </div>
            <div className="flex items-center gap-x-2">
              <div>
                <p className="text-left">ADA</p>
                <p className="text-left text-[#B1B5C3] text-[14px]">Cardano</p>
              </div>
              <Image
                className="w-8 h-8"
                src="/images/doge.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LiveChartInfo;
