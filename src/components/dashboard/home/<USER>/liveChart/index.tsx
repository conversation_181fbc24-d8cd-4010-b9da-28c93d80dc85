import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const StockChart: React.FC = () => {
  const [chartOptions, setChartOptions] = useState({
    chart: {
      backgroundColor: "transparent",
      width: 750,
    },
    title: {
      text: "",
    },
    rangeSelector: {
      selected: 1,
      inputEnabled: false,
      buttonTheme: {
        fill: "transparent",
        stroke: "#ccc",
        style: {
          color: "#333",
          fontWeight: "bold",
        },
        states: {
          hover: {
            fill: "#f0f0f0",
            style: {
              color: "#000",
            },
          },
          select: {
            fill: "#23262F",
            style: {
              color: "#fff",
            },
          },
        },
      },
      buttons: [
        {
          type: "day",
          count: 3,
          text: "1D",
        },
        {
          type: "day",
          count: 7,
          text: "7D",
        },
        {
          type: "month",
          count: 20,
          text: "1M",
        },
        {
          type: "all",
          text: "All",
        },
      ],
    },
    navigator: {
      enabled: false,
    },
    xAxis: {
      gridLineWidth: 0,
    },
    yAxis: {
      gridLineWidth: 0,
    },
    series: [
      {
        name: "AAPL Stock Price",
        type: "areaspline",
        data: [],
        threshold: null,
        tooltip: {
          valueDecimals: 2,
        },
        color: "#2FA76666",
        fillColor: {
          linearGradient: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 1,
          },
          stops: [
            [0, "rgba(0, 128, 0, 0.8)"],
            [1, "rgba(0, 50, 0, 0)"],
          ],
        },
      },
    ],
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(
          "https://demo-live-data.highcharts.com/aapl-c.json"
        );
        const data = await response.json();

        setChartOptions((prevOptions) => ({
          ...prevOptions,
          series: [
            {
              ...prevOptions.series[0],
              data: data,
            },
          ],
        }));
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  return <HighchartsReact highcharts={Highcharts} options={chartOptions} />;
};

export default StockChart;
