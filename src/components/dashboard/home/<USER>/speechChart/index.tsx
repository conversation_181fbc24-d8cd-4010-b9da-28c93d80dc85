import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const SpeechChart = () => {
  const chartOptions = {
    chart: {
      backgroundColor: "transparent",
    },
    title: {
      text: "",
      margin: 25,
      style: {
        color: "#fff",
        fontSize: "26px",
        fontWeight: "bold",
      },
    },

    sonification: {
      duration: 8000,
      defaultInstrumentOptions: {
        mapping: {
          pitch: {
            min: "c3",
            max: "d6",
          },
        },
      },
      globalContextTracks: [
        {
          instrument: "piano",
          valueInterval: 1 / 3, // Play 3 times for every X-value
          mapping: {
            pitch: {
              mapTo: "y",
              value: 0, // Map to a fixed Y value
            },
            volume: 0.1,
          },
        },
        {
          instrument: "shaker",
          activeWhen: {
            valueProp: "x", // Active when X is between these values.
            min: 4,
            max: 9,
          },
          timeInterval: 100, // Play every 100 milliseconds
          mapping: {
            volume: 0.1,
          },
        },
        {
          type: "speech",
          valueInterval: 1,
          activeWhen: {
            crossingUp: 4, // Active when crossing over x = 4
          },
          mapping: {
            text: "Summer",
            rate: 2.5,
            volume: 0.3,
          },
        },
      ],
    },

    yAxis: {
      plotLines: [
        {
          value: 0,
          color: "#59D",
          dashStyle: "shortDash",
          width: 2,
        },
      ],
      title: {
        enabled: false,
      },
      labels: {
        format: "",
        style: {
          display: "none",
        },
      },
      gridLineWidth: 0,
    },

    xAxis: {
      plotBands: [
        {
          from: 3.5,
          to: 8.5,
          color: "transparent",
          label: {
            text: "$6,100.23",
            align: "left",
            x: 10,
            style: {
              color: "#fff",
            },
          },
        },
      ],

      crosshair: true,
      categories: [
        "22",
        "30",
        "11",
        "50",
        "111",
        "330",
        "500",
        "12",
        "54",
        "43",
        "11",
        "88",
      ],
    },

    legend: {
      enabled: false,
    },

    tooltip: {
      valueSuffix: "",
    },

    series: [
      {
        name: "",
        data: [-5, -6, -2, 4, 10, 14, 17, 15, 10, 6, 0, -4],
        color: "#fff",
      },
    ],
  };

  return (
    <div>
      <div className="flex justify-between">
        <select className="w-20 border border-[#353945] rounded-md mb-10">
          <option value="" className="">
            USDT
          </option>
        </select>
        <p className="text-2xl font-medium">مجموع دارایی ها</p>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </div>
  );
};

export default SpeechChart;
