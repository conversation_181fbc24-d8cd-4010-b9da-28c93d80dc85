import React from "react";

const PieChartGrid = () => {
  return (
    <div>
      <select className="w-1/3 border border-[#353945] rounded-md mb-10">
        <option value="">USDT</option>
      </select>
      <div className="space-y-5">
        <div className="flex gap-x-4">
          <span className="text-[#777E90]"> 1950.08 USDT = </span>
          <span>5,759.24 ADA</span>
          <span>32%</span>
          <span>ADA</span>
          <div className="w-4 h-4 bg-red-400 rounded-full"></div>
        </div>
        <div className="flex gap-x-4">
          <span className="text-[#777E90]"> 1950.08 USDT = </span>
          <span>5,759.24 ADA</span>
          <span>32%</span>
          <span>ADA</span>
          <div className="w-4 h-4 bg-green-400 rounded-full"></div>
        </div>
        <div className="flex gap-x-4">
          <span className="text-[#777E90]"> 1950.08 USDT = </span>
          <span>5,759.24 ADA</span>
          <span>32%</span>
          <span>ADA</span>
          <div className="w-4 h-4 bg-green-400 rounded-full"></div>
        </div>
        <div className="flex gap-x-4">
          <span className="text-[#777E90]"> 1950.08 USDT = </span>
          <span>5,759.24 ADA</span>
          <span>32%</span>
          <span>ADA</span>
          <div className="w-4 h-4 bg-red-400 rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default PieChartGrid;
