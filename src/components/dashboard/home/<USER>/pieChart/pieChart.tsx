import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { useEffect, useState, useRef } from "react";

declare module "highcharts" {
  interface Chart {
    centerTextElement?: Highcharts.SVGElement;
    dollarSignElement?: Highcharts.SVGElement;
  }
}

const PieChart = () => {
  const [isMobile, setIsMobile] = useState(false);
  const chartRef = useRef(null);

  // تشخیص حالت موبایل
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // بررسی اولیه
    checkIfMobile();

    // اضافه کردن event listener برای تغییر سایز صفحه
    window.addEventListener("resize", checkIfMobile);

    // پاکسازی event listener
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  // اضافه کردن متن مرکزی بعد از رندر شدن نمودار

  const options = {
    chart: {
      type: "pie",
      backgroundColor: "transparent",
      width: 400,
      height: 300,
      events: {
        render: function () {
          const chart = this as unknown as Highcharts.Chart;

          // این بخش برای اطمینان از نمایش متن مرکزی بعد از هر بار رندر مجدد نمودار است
          if (chart.centerTextElement) {
            chart.centerTextElement.attr({
              x: chart.plotLeft + chart.plotWidth / 2,
              y: chart.plotTop + chart.plotHeight / 2,
            });
          }
          if (chart.dollarSignElement) {
            chart.dollarSignElement.attr({
              x: chart.plotLeft + chart.plotWidth * 0.64,
              y: chart.plotTop + chart.plotHeight * 0.4,
            });
          }
        },
      },
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    title: {
      text: "نمای دارایی ها",
      style: {
        color: "#fff",
        fontSize: "26px",
        fontWeight: "bold",
      },
    },
    tooltip: {
      pointFormat: "{series.name}: <b>{point.percentage:.0f}%</b>",
    },
    legend: {
      enabled: false,
    },
    plotOptions: {
      series: {
        allowPointSelect: true,
        cursor: "pointer",
        borderRadius: 20,
        borderWidth: 0, // حذف بردر
        dataLabels: [
          {
            enabled: false,
            distance: 20,
            format: "{point.name}",
          },
          {
            enabled: false,
            distance: -15,
            format: "{point.percentage:.0f}%",
            style: {
              fontSize: "0.9em",
            },
          },
        ],
        showInLegend: true,
      },
    },
    series: [
      {
        name: "Registrations",
        colorByPoint: true,
        innerSize: "89%",
        data: [
          { name: "EV", y: 23.9 },
          { name: "Hybrids", y: 12.6 },
          { name: "Diesel", y: 37.0 },
          { name: "Petrol", y: 26.4 },
        ],
      },
    ],
  };

  // اگر موبایل است، چیزی نمایش نده
  if (isMobile) {
    return null;
  }

  return (
    <HighchartsReact highcharts={Highcharts} options={options} ref={chartRef} />
  );
};

export default PieChart;
