"use client";
import Header from "./header";
import Sidebar from "./sidebar";
import { useRejectedStatus } from "@/hooks/useRejectedStatus";
import RejectedStatusModal from "@/components/dashboard/modals/RejectedStatusModal";

const Layout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  const { isModalOpen, closeModal } = useRejectedStatus();

  return (
    <>
      <Header />
      <div className="flex gap-x-5 mt-5">
        <Sidebar />
        <main className="w-full lg:w-[calc(100%-244px)]">{children}</main>
      </div>

      {/* Rejected Status Modal */}
      <RejectedStatusModal isOpen={isModalOpen} onClose={closeModal} />
    </>
  );
};

export default Layout;
