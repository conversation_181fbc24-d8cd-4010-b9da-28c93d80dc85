"use client";
import React, { useState, useRef, useEffect } from "react";
import { uploadDocument } from "../../../../requests/dashboardRequest";
import { compressImage, validateImageFile, formatFileSize } from "../../../../utils/imageUtils";
import toast from "react-hot-toast";

interface AuthData {
  birthDate: string;
  acceptedTerms: boolean;
  nationalCardFrontImage: string | null;
  nationalCardBackImage: string | null;
  selfieImage: string | null;
  consentAccepted: boolean;
  signature: string | null;
  consentPdf: string | null;
  uploadStatus: {
    id: boolean;
    id_back: boolean;
    selfie: boolean;
    consent: boolean;
  };
  uploadProgress: {
    id: number;
    id_back: number;
    selfie: number;
    consent: number;
  };
  verificationStatus: {
    overall_status: string;
    is_fully_verified: boolean;
    completion_percentage: number;
    document_status: {
      [key: string]: {
        id: number;
        status: 'pending' | 'approved' | 'rejected';
        description: string | null;
        created_at: string;
        updated_at: string;
      };
    };
    has_pending: boolean;
    has_rejected: boolean;
  } | null;
}

interface Props {
  authData: AuthData;
  updateAuthData: (data: Partial<AuthData>) => void;
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  canProceedToNext: boolean;
}

const SelfieStep: React.FC<Props> = ({
  authData,
  updateAuthData,
  onNext,
  onBack,
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Helper functions for verification status
  const isDocumentApproved = (): boolean => {
    return authData.verificationStatus?.document_status?.['selfie']?.status === 'approved';
  };

  const isDocumentRejected = (): boolean => {
    return authData.verificationStatus?.document_status?.['selfie']?.status === 'rejected';
  };

  const isDocumentPending = (): boolean => {
    return authData.verificationStatus?.document_status?.['selfie']?.status === 'pending';
  };

  const getRejectionReason = (): string | null => {
    return authData.verificationStatus?.document_status?.['selfie']?.description || null;
  };

  // Helper function to check if document can be uploaded (new user or rejected)
  const canUploadDocument = (): boolean => {
    // اگر هیچ وضعیت احراز هویتی وجود ندارد (کاربر جدید)
    if (!authData.verificationStatus?.document_status) {
      return true;
    }

    // اگر مدرک وجود ندارد (کاربر جدید)
    if (!authData.verificationStatus.document_status['selfie']) {
      return true;
    }

    // اگر مدرک رد شده
    return authData.verificationStatus.document_status['selfie'].status === 'rejected';
  };

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent));
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // Validate file
    const validation = validateImageFile(file, 10); // 10MB max
    if (!validation.isValid) {
      toast.error(validation.error || 'فایل نامعتبر است');
      return;
    }

    try {
      // Show original file size
      console.log(`Original file size: ${formatFileSize(file.size)}`);

      // Compress image
      const compressedFile = await compressImage(file, 1200, 1200, 0.8);
      console.log(`Compressed file size: ${formatFileSize(compressedFile.size)}`);

      // Create preview URL
      const imageUrl = URL.createObjectURL(compressedFile);

      // Update UI immediately
      updateAuthData({
        selfieImage: imageUrl,
        uploadProgress: { ...authData.uploadProgress, selfie: 0 }
      });

      // Simulate progress
      let currentProgress = 0;
      const progressInterval = setInterval(() => {
        currentProgress = Math.min(currentProgress + 10, 90);
        updateAuthData({
          uploadProgress: {
            ...authData.uploadProgress,
            selfie: currentProgress
          }
        });
      }, 100);

      const result = await uploadDocument(compressedFile, 'selfie');

      clearInterval(progressInterval);

      if (result.isError) {
        toast.error(result.message || 'خطا در آپلود فایل');
        // Reset on error
        updateAuthData({
          selfieImage: null,
          uploadProgress: { ...authData.uploadProgress, selfie: 0 }
        });
      } else {
        toast.success('عکس سلفی با موفقیت آپلود شد');
        // Mark as uploaded
        updateAuthData({
          uploadStatus: {
            ...authData.uploadStatus,
            selfie: true
          },
          uploadProgress: {
            ...authData.uploadProgress,
            selfie: 100
          }
        });
      }
    } catch (error) {
      console.error("خطا در آپلود:", error);
      toast.error("خطا در آپلود فایل. لطفا دوباره تلاش کنید.");

      // Reset on error
      updateAuthData({
        selfieImage: null,
        uploadProgress: { ...authData.uploadProgress, selfie: 0 }
      });
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleCameraCapture = () => {
    if (isMobile) {
      fileInputRef.current?.click();
    } else {
      setShowModal(true);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file && file.type.startsWith("image/")) {
      handleFileUpload(file);
    }
  };

  const handleNext = () => {
    // اگر مدرک رد شده و آپلود شده، برگرد به صفحه وضعیت
    if (isDocumentRejected() && authData.uploadStatus.selfie) {
      toast.success("عکس سلفی با موفقیت ارسال شد. در حال بازگشت به صفحه وضعیت...");
      setTimeout(() => {
        window.location.href = '/dashboard/authenticate';
      }, 1500);
      return;
    }

    if (!authData.selfieImage) {
      toast.error("لطفا عکس سلفی خود را آپلود کنید");
      return;
    }
    if (!authData.uploadStatus.selfie) {
      toast.error("لطفا منتظر بمانید تا آپلود عکس سلفی کامل شود");
      return;
    }
    onNext();
  };

  const Modal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1E1E1E] p-6 rounded-lg shadow-xl max-w-md w-full">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-[#4899EB]/20 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-[#4899EB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-3">استفاده از دوربین</h3>
          <p className="text-white/80 text-center mb-6 leading-6">
            برای گرفتن عکس سلفی با دوربین، لطفا با تلفن همراه خود وارد سایت شوید.
            در غیر این صورت می‌توانید از گزینه "انتخاب فایل" استفاده کنید.
          </p>
          <button
            onClick={() => setShowModal(false)}
            className="w-full bg-[#4899EB] text-white py-3 rounded-lg hover:bg-blue-600 transition-colors font-medium"
          >
            متوجه شدم
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-[#18191D] rounded-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2">
            عکس سلفی
            {isDocumentApproved() && (
              <span className="text-green-500 text-sm">✓ تایید شده</span>
            )}
            {isDocumentRejected() && (
              <span className="text-red-500 text-sm">✗ رد شده</span>
            )}
            {isDocumentPending() && (
              <span className="text-yellow-500 text-sm">⏳ در حال بررسی</span>
            )}
          </h2>
          <p className="text-gray-400">
            لطفا عکس سلفی خود را همراه با کارت ملی ارسال کنید
          </p>
          {isDocumentRejected() && getRejectionReason() && (
            <div className="bg-red-500/10 border border-red-500/20 rounded p-3 mt-4 max-w-md mx-auto">
              <p className="text-red-300 text-sm">
                <strong>دلیل رد:</strong> {getRejectionReason()}
              </p>
              <p className="text-gray-400 text-xs mt-2">
                لطفا مدرک را با رفع مشکل مجدداً ارسال کنید
              </p>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="bg-[#2A2D35] p-4 rounded-lg mb-6 border-r-4 border-r-[#4899EB]">
          <h4 className="text-white font-semibold mb-2 text-sm">نکات مهم برای عکس سلفی:</h4>
          <ul className="text-white/80 text-sm space-y-1">
            <li>• کارت ملی را در کنار صورت خود نگه دارید</li>
            <li>• صورت و کارت ملی هر دو باید واضح و خوانا باشند</li>
            <li>• از نور مناسب استفاده کنید تا تصویر تیره نباشد</li>
            <li>• مطمئن شوید که اطلاعات روی کارت ملی قابل خواندن است</li>
            <li>• عکس باید طبیعی و بدون فیلتر باشد</li>
          </ul>
        </div>

        {/* Sample Image */}
        <div className="bg-[#2A2D35] p-4 rounded-lg mb-6">
          <h4 className="text-white font-semibold mb-3 text-sm">نمونه عکس صحیح:</h4>
          <div className="flex justify-center">
            <div className="w-48 h-32 bg-gray-600 rounded-lg flex items-center justify-center">
              <div className="text-center text-gray-400">
                <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <p className="text-xs">نمونه عکس سلفی</p>
                <p className="text-xs">با کارت ملی</p>
              </div>
            </div>
          </div>
        </div>

        {/* اگر مدرک تایید شده یا در حال بررسی، فقط نمایش داده شود */}
        {isDocumentApproved() ? (
          <div className="relative min-h-[320px] flex flex-col items-center justify-center rounded-lg border-2 border-green-500 bg-green-500/10 p-6 mb-6">
            <div className="text-center">
              <svg className="w-16 h-16 mx-auto text-green-500 mb-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <p className="text-green-400 font-semibold">عکس سلفی تایید شده است</p>
              <p className="text-gray-400 text-sm mt-2">نیازی به ارسال مجدد نیست</p>
            </div>
          </div>
        ) : isDocumentPending() ? (
          <div className="relative min-h-[320px] flex flex-col items-center justify-center rounded-lg border-2 border-yellow-500 bg-yellow-500/10 p-6 mb-6">
            <div className="text-center">
              <svg className="w-16 h-16 mx-auto text-yellow-500 mb-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-yellow-400 font-semibold">عکس سلفی در حال بررسی است</p>
              <p className="text-gray-400 text-sm mt-2">لطفا منتظر نتیجه بررسی بمانید</p>
            </div>
          </div>
        ) : (
          /* Upload Area */
          <div
            className={`relative min-h-[320px] flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 mb-6 transition-all ${
              dragActive
                ? "border-[#4899EB] bg-[#4899EB]/10"
                : authData.selfieImage
                ? "border-green-500 bg-green-500/5"
                : "border-gray-600 bg-[#2A2D35]/30"
            }`}
            onDragEnter={canUploadDocument() ? handleDrag : undefined}
            onDragLeave={canUploadDocument() ? handleDrag : undefined}
            onDragOver={canUploadDocument() ? handleDrag : undefined}
            onDrop={canUploadDocument() ? handleDrop : undefined}
          >
          {authData.selfieImage ? (
            <div className="flex flex-col items-center">
              <img
                src={authData.selfieImage}
                alt="عکس سلفی آپلود شده"
                className="max-h-[240px] object-contain rounded-lg shadow-lg mb-4"
              />

              {/* Upload Progress */}
              {authData.uploadProgress.selfie < 100 && (
                <div className="w-full max-w-xs mb-3">
                  <div className="flex justify-between text-xs text-gray-400 mb-1">
                    <span>در حال آپلود...</span>
                    <span>{authData.uploadProgress.selfie}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-[#4899EB] h-2 rounded-full transition-all duration-300"
                      style={{ width: `${authData.uploadProgress.selfie}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Upload Status */}
              {authData.uploadStatus.selfie ? (
                <p className="text-green-400 text-sm flex items-center mb-2">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  عکس سلفی با موفقیت آپلود شد
                </p>
              ) : (
                <p className="text-yellow-400 text-sm flex items-center mb-2">
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  در حال آپلود
                </p>
              )}

              <button
                onClick={() => updateAuthData({
                  selfieImage: null,
                  uploadStatus: { ...authData.uploadStatus, selfie: false },
                  uploadProgress: { ...authData.uploadProgress, selfie: 0 }
                })}
                className="mt-2 text-red-400 text-sm hover:text-red-300 transition-colors"
                disabled={authData.uploadProgress.selfie > 0 && authData.uploadProgress.selfie < 100}
              >
                حذف و آپلود مجدد
              </button>
            </div>
          ) : (
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-[#4899EB]/20 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-[#4899EB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <p className="text-white text-lg font-semibold mb-2">
                ارسال عکس سلفی
              </p>
              <p className="text-white/70 text-sm text-center max-w-xs mx-auto mb-6">
                فایل را اینجا بکشید و رها کنید یا از دکمه‌های زیر استفاده کنید
              </p>
            </div>
          )}

          {/* Hidden File Input */}
          <input
            type="file"
            ref={fileInputRef}
            onChange={(e) => canUploadDocument() && handleInputChange(e)}
            accept="image/*"
            capture={isMobile ? "user" : undefined}
            className="hidden"
            disabled={!canUploadDocument()}
          />

          {/* Upload Buttons */}
          {!authData.selfieImage && (
            <div className="flex gap-4">
              <button
                onClick={() => canUploadDocument() && handleUploadClick()}
                disabled={!canUploadDocument()}
                className={`flex items-center justify-center px-4 py-2 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105 ${
                  !canUploadDocument()
                    ? 'bg-gray-600 cursor-not-allowed opacity-50'
                    : 'bg-[#4899EB] hover:bg-blue-600'
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span className="text-sm font-medium">انتخاب فایل</span>
              </button>
              <button
                onClick={() => canUploadDocument() && handleCameraCapture()}
                disabled={!canUploadDocument()}
                className={`flex items-center justify-center px-4 py-2 border border-gray-600 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105 ${
                  !canUploadDocument()
                    ? 'bg-gray-600 cursor-not-allowed opacity-50'
                    : 'bg-[#2A2D35] hover:bg-gray-600'
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="text-sm font-medium">دوربین سلفی</span>
              </button>
            </div>
          )}
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <button
            onClick={onBack}
            className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            بازگشت
          </button>
          <button
            onClick={handleNext}
            disabled={!authData.uploadStatus.selfie}
            className="px-6 py-3 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {!authData.uploadStatus.selfie && (
              <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {!authData.uploadStatus.selfie ? 'در حال آپلود...' : 'مرحله بعد'}
          </button>
        </div>
      </div>

      {/* Modal */}
      {showModal && <Modal />}
    </div>
  );
};

export default SelfieStep;
