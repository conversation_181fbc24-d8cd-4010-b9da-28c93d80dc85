"use client";
import React, { useState, useRef, useEffect } from "react";
import jsPDF from "jspdf";
import SignatureWrapper from "../SignatureWrapper";
import { uploadDocument } from "../../../../requests/dashboardRequest";
import { dataUrlToFile } from "../../../../utils/imageUtils";
import toast from "react-hot-toast";
import { getProfile } from "../../../../requests/dashboardRequest";

interface AuthData {
  birthDate: string;
  acceptedTerms: boolean;
  nationalCardFrontImage: string | null;
  nationalCardBackImage: string | null;
  selfieImage: string | null;
  consentAccepted: boolean;
  signature: string | null;
  consentPdf: string | null;
  uploadStatus: {
    id: boolean;
    id_back: boolean;
    selfie: boolean;
    consent: boolean;
  };
  uploadProgress: {
    id: number;
    id_back: number;
    selfie: number;
    consent: number;
  };
}

interface Props {
  authData: AuthData;
  updateAuthData: (data: Partial<AuthData>) => void;
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  canProceedToNext: boolean;
}

const ConsentStep: React.FC<Props> = ({
  authData,
  updateAuthData,
  onNext,
  onBack,
}) => {
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [isLoadingUserInfo, setIsLoadingUserInfo] = useState(true);
  const signatureRef = useRef<any>(null);

  // Fetch user information
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const result = await getProfile();
        if (result.isError) {
          console.error('Failed to fetch user info');
        } else {
          setUserInfo(result.data);
        }
      } catch (error) {
        console.error('Error fetching user info:', error);
      } finally {
        setIsLoadingUserInfo(false);
      }
    };

    fetchUserInfo();
  }, []);

  const clearSignature = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
      updateAuthData({ signature: null });
    }
  };

  const saveSignature = () => {
    if (signatureRef.current && !signatureRef.current.isEmpty()) {
      const signatureData = signatureRef.current.toDataURL();
      updateAuthData({ signature: signatureData });
    }
  };

  const generatePDF = async () => {
    if (!authData.signature) {
      alert("لطفا ابتدا امضا کنید");
      return;
    }

    if (!userInfo) {
      alert("در حال بارگذاری اطلاعات کاربر، لطفا کمی صبر کنید");
      return;
    }

    setIsGeneratingPdf(true);

    try {
      const currentDate = new Date().toLocaleDateString('fa-IR');

      // Create simple HTML content for PDF
      const htmlContent = `
        <div style="
          font-family: 'Tahoma', Arial, sans-serif;
          direction: rtl;
          text-align: right;
          line-height: 1.4;
          padding: 25px 35px;
          color: #333;
          background: white;
          width: 170mm;
          margin: 0 auto;
          box-sizing: border-box;
          font-size: 12px;
        ">
          <!-- Header -->
          <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #4899EB; padding-bottom: 12px;">
            <h1 style="font-size: 18px; font-weight: bold; color: #4899EB; margin: 0 0 5px 0;">
              رضایت‌نامه احراز هویت و تایید اطلاعات
            </h1>
            <h2 style="font-size: 12px; color: #666; margin: 0; font-weight: normal;">
              پلتفرم معاملات ارزهای دیجیتال اکسچنجیم
            </h2>
          </div>

          <!-- User Information -->
          <div style="margin: 15px 5px; padding: 15px; background: #f0f8ff; border-radius: 6px; border: 1px solid #4899EB;">
            <h3 style="color: #4899EB; font-size: 14px; margin: 0 0 10px 0; text-align: center; font-weight: bold;">اطلاعات شخصی متقاضی</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 11px;">
              <div><strong>نام و نام خانوادگی:</strong> ${userInfo.firstname || ''} ${userInfo.lastname || ''}</div>
              <div><strong>کد ملی:</strong> ${userInfo.national_id || 'نامشخص'}</div>
              <div><strong>شماره موبایل:</strong> ${userInfo.phone || 'نامشخص'}</div>
              <div><strong>تاریخ تولد:</strong> ${authData.birthDate}</div>
            </div>
          </div>

          <!-- Main Content -->
          <div style="font-size: 11px; line-height: 1.6; text-align: justify; margin: 0 5px;">
            <p style="margin-bottom: 12px; text-indent: 15px; font-weight: 500;">
              اینجانب <strong>${userInfo.firstname || ''} ${userInfo.lastname || ''}</strong> دارای کد ملی <strong>${userInfo.national_id || 'نامشخص'}</strong> با پذیرش کامل قوانین و مقررات پلتفرم اکسچنجیم، رضایت صریح خود را برای موارد ذیل اعلام می‌نمایم:
            </p>

            <div style="margin: 12px 0; padding: 10px; background: #fafafa; border-radius: 5px; border-right: 3px solid #4899EB;">
              <h4 style="color: #4899EB; margin: 0 0 8px 0; font-size: 12px;">موارد رضایت:</h4>

              <p style="margin: 6px 0; text-indent: 0; padding-right: 10px; font-size: 10px;">
                <strong>الف)</strong> جمع‌آوری و پردازش اطلاعات هویتی شامل تصاویر کارت ملی، عکس سلفی، اطلاعات شخصی و تاریخ تولد
              </p>

              <p style="margin: 6px 0; text-indent: 0; padding-right: 10px; font-size: 10px;">
                <strong>ب)</strong> استفاده از اطلاعات جهت احراز هویت، رعایت قوانین مبارزه با پولشویی و انطباق با الزامات نظارتی
              </p>

              <p style="margin: 6px 0; text-indent: 0; padding-right: 10px; font-size: 10px;">
                <strong>ج)</strong> نگهداری ایمن اطلاعات با بالاترین استانداردهای امنیتی و عدم انتشار به اشخاص ثالث
              </p>

              <p style="margin: 6px 0; text-indent: 0; padding-right: 10px; font-size: 10px;">
                <strong>د)</strong> پردازش اطلاعات در چارچوب قوانین حمایت از حریم خصوصی جمهوری اسلامی ایران
              </p>
            </div>

            <div style="margin: 12px 0; padding: 10px; background: #fff8dc; border-radius: 5px; border: 1px solid #ffa500;">
              <h4 style="color: #ff8c00; margin: 0 0 8px 0; font-size: 12px;">تعهدات و اقرارات:</h4>
              <p style="margin: 0; text-indent: 10px; font-size: 10px;">
                اینجانب اقرار می‌نمایم که کلیه اطلاعات ارائه شده صحیح و دقیق بوده و مسئولیت صحت آنها بر عهده اینجانب است. همچنین دارای اهلیت قانونی کامل برای انعقاد این قرارداد می‌باشم.
              </p>
            </div>
          </div>

          <!-- Date and Legal Info -->
          <div style="margin: 12px 5px; padding: 12px; background: #f0f4f8; border-radius: 5px; border: 1px solid #4899EB;">
            <h4 style="color: #4899EB; margin: 0 0 8px 0; font-size: 12px; text-align: center;">اطلاعات تکمیلی</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px; font-size: 10px;">
              <div><strong>تاریخ تنظیم:</strong> ${currentDate}</div>
              <div><strong>وضعیت:</strong> در حال بررسی</div>
            </div>
          </div>

          <!-- Signature Section -->
          <div style="margin: 15px 5px; text-align: center; border: 2px solid #4899EB; padding: 18px; border-radius: 8px; background: #f8fbff;">
            <h3 style="margin: 0 0 15px 0; font-size: 14px; font-weight: bold; color: #4899EB;">امضای دیجیتال</h3>
            <div style="margin: 15px 0; padding: 12px; border: 1px solid #ddd; border-radius: 5px; display: inline-block;">
              <img src="${authData.signature}" alt="امضای دیجیتال ${userInfo.firstname || ''} ${userInfo.lastname || ''}" style="max-width: 200px; max-height: 100px; display: block; min-width: 180px; min-height: 80px;">
            </div>
            <p style="margin: 10px 0 0 0; font-size: 12px; color: #666;">
              <strong>${userInfo.firstname || ''} ${userInfo.lastname || ''}</strong>
            </p>
            <p style="margin: 5px 0 0 0; font-size: 10px; color: #888;">
              ${currentDate} - ${new Date().toLocaleTimeString('fa-IR')}
            </p>
          </div>

          <!-- Legal Footer -->
          <div style="margin: 12px 5px 10px 5px; padding: 10px; background: #fff3cd; border: 1px solid #ffc107; border-radius: 5px;">
            <h4 style="color: #856404; margin: 0 0 6px 0; font-size: 11px; text-align: center; font-weight: bold;">اعتبار قانونی</h4>
            <p style="margin: 0; font-size: 9px; color: #856404; text-align: center; line-height: 1.4;">
              این رضایت‌نامه از تاریخ امضا معتبر بوده و تا زمان لغو توسط کاربر دارای اعتبار قانونی کامل می‌باشد. این سند مطابق قوانین جمهوری اسلامی ایران تنظیم شده است.
            </p>
          </div>
        </div>
      `;

      // Create a temporary div to render the HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '0';
      document.body.appendChild(tempDiv);

      // Wait a moment for fonts to load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Use html2canvas to convert HTML to canvas with optimized settings
      const html2canvas = (await import('html2canvas')).default;
      const canvas = await html2canvas(tempDiv.firstElementChild as HTMLElement, {
        scale: 2.1, // Optimized scale for better fit
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 660, // A4 proportional width
        height: 950, // A4 proportional height
        logging: false,
        imageTimeout: 0,
        scrollX: 0,
        scrollY: 0
      });

      // Clean up
      document.body.removeChild(tempDiv);

      // Create PDF from canvas with proper margins
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgData = canvas.toDataURL('image/jpeg', 0.8); // Slightly better quality for text

      // Calculate dimensions to fit A4 with proper margins
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const margin = 15; // 15mm margin on all sides
      const availableWidth = pdfWidth - (margin * 2);
      const availableHeight = pdfHeight - (margin * 2);

      // Calculate image dimensions maintaining aspect ratio
      const imgAspectRatio = canvas.width / canvas.height;
      let imgWidth = availableWidth;
      let imgHeight = imgWidth / imgAspectRatio;

      // If image is too tall, scale it down
      if (imgHeight > availableHeight) {
        imgHeight = availableHeight;
        imgWidth = imgHeight * imgAspectRatio;
      }

      // Center the image on the page
      const xOffset = (pdfWidth - imgWidth) / 2;
      const yOffset = (pdfHeight - imgHeight) / 2;

      pdf.addImage(imgData, 'JPEG', xOffset, yOffset, imgWidth, imgHeight);

      // Generate PDF as base64
      const pdfData = pdf.output('datauristring');
      updateAuthData({ consentPdf: pdfData });

      // Download PDF
      pdf.save(`exchangim-consent-${currentDate.replace(/\//g, '-')}.pdf`);

      // Upload PDF to server
      try {
        // Convert PDF to File
        const pdfFile = dataUrlToFile(pdfData, `exchangim-consent-${currentDate.replace(/\//g, '-')}.pdf`);

        // Update progress
        updateAuthData({
          uploadProgress: { ...authData.uploadProgress, consent: 0 }
        });

        // Simulate progress
        let currentProgress = 0;
        const progressInterval = setInterval(() => {
          currentProgress = Math.min(currentProgress + 15, 90);
          updateAuthData({
            uploadProgress: {
              ...authData.uploadProgress,
              consent: currentProgress
            }
          });
        }, 150);

        const uploadResult = await uploadDocument(pdfFile, 'consent');

        clearInterval(progressInterval);

        if (uploadResult.isError) {
          toast.error(uploadResult.message || 'خطا در آپلود PDF');
          updateAuthData({
            uploadProgress: { ...authData.uploadProgress, consent: 0 }
          });
        } else {
          toast.success('PDF رضایت‌نامه با موفقیت آپلود شد');
          updateAuthData({
            uploadStatus: {
              ...authData.uploadStatus,
              consent: true
            },
            uploadProgress: {
              ...authData.uploadProgress,
              consent: 100
            }
          });
        }
      } catch (uploadError) {
        console.error('Error uploading PDF:', uploadError);
        toast.error('خطا در آپلود PDF. لطفا دوباره تلاش کنید.');
        updateAuthData({
          uploadProgress: { ...authData.uploadProgress, consent: 0 }
        });
      }

    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('خطا در تولید PDF. لطفا دوباره تلاش کنید.');
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handleNext = () => {
    if (!authData.consentAccepted) {
      toast.error("لطفا رضایت‌نامه را مطالعه کرده و تایید کنید");
      return;
    }
    if (!authData.signature) {
      toast.error("لطفا امضای خود را انجام دهید");
      return;
    }
    if (!authData.consentPdf) {
      toast.error("لطفا PDF رضایت‌نامه را تولید کنید");
      return;
    }
    if (!authData.uploadStatus.consent) {
      toast.error("لطفا منتظر بمانید تا آپلود PDF کامل شود");
      return;
    }
    onNext();
  };

  const ConsentModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1E1E1E] rounded-lg shadow-xl max-w-3xl w-full max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-xl font-bold text-white">رضایت‌نامه احراز هویت</h3>
        </div>
        <div className="p-6 overflow-y-auto max-h-[60vh] text-gray-300 text-sm leading-6">
          <div className="space-y-6">
            <div className="bg-[#2A2D35] p-4 rounded-lg border-r-4 border-r-[#4899EB]">
              <h4 className="text-white font-semibold mb-3">رضایت جمع‌آوری و پردازش اطلاعات شخصی</h4>
              <p className="leading-relaxed">
                من، کاربر محترم اکسچنجیم، با آگاهی کامل و اختیار تام، رضایت خود را برای جمع‌آوری، پردازش، ذخیره‌سازی و استفاده از اطلاعات شخصی‌ام توسط اکسچنجیم اعلام می‌کنم.
              </p>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-3">اطلاعات مورد جمع‌آوری</h4>
              <p className="mb-3">اطلاعات زیر از من جمع‌آوری و پردازش خواهد شد:</p>
              <ul className="list-disc list-inside space-y-1 text-gray-300">
                <li>تاریخ تولد</li>
                <li>تصاویر کارت ملی (رو و پشت)</li>
                <li>عکس سلفی همراه با کارت ملی</li>
                <li>اطلاعات هویتی موجود در کارت ملی</li>
                <li>سایر اطلاعات لازم برای احراز هویت</li>
              </ul>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-3">اهداف استفاده از اطلاعات</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-300">
                <li>تایید هویت و احراز اصالت کاربر</li>
                <li>رعایت قوانین و مقررات مبارزه با پولشویی</li>
                <li>تامین امنیت پلتفرم و جلوگیری از تقلب</li>
                <li>ارائه خدمات مطابق با قوانین کشور</li>
                <li>حفظ امنیت معاملات و دارایی‌های کاربران</li>
              </ul>
            </div>

            <div className="bg-[#2A2D35] p-4 rounded-lg">
              <h4 className="text-white font-semibold mb-3">تعهدات اکسچنجیم</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-300">
                <li>حفاظت از اطلاعات شخصی با بالاترین استانداردهای امنیتی</li>
                <li>عدم انتشار اطلاعات به اشخاص ثالث بدون رضایت کاربر</li>
                <li>استفاده از اطلاعات فقط برای اهداف مشروع و قانونی</li>
                <li>رعایت قوانین حفاظت از داده‌های شخصی</li>
                <li>امکان حذف اطلاعات در صورت درخواست کاربر</li>
              </ul>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-3">حقوق کاربر</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-300">
                <li>حق دسترسی به اطلاعات شخصی ذخیره شده</li>
                <li>حق اصلاح اطلاعات نادرست</li>
                <li>حق درخواست حذف اطلاعات</li>
                <li>حق اعتراض به پردازش اطلاعات</li>
                <li>حق انتقال اطلاعات</li>
              </ul>
            </div>

            <div className="bg-[#2A2D35] p-4 rounded-lg border-r-4 border-r-yellow-500">
              <h4 className="text-white font-semibold mb-3">تایید و اقرار</h4>
              <p className="leading-relaxed">
                با تایید این رضایت‌نامه، اقرار می‌کنم که تمام اطلاعات ارائه شده صحیح و دقیق است و مسئولیت هرگونه عدم صحت اطلاعات بر عهده من است. همچنین تایید می‌کنم که سن قانونی لازم برای انعقاد قرارداد را دارم.
              </p>
            </div>

            <div className="text-center bg-[#1C1E24] p-4 rounded-lg">
              <p className="text-yellow-400 font-medium">
                این رضایت‌نامه تا زمان لغو توسط کاربر یا بسته شدن حساب کاربری معتبر است.
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 border-t border-gray-700 flex gap-4 justify-end">
          <button
            onClick={() => setShowConsentModal(false)}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            بستن
          </button>
          <button
            onClick={() => {
              updateAuthData({ consentAccepted: true });
              setShowConsentModal(false);
            }}
            className="px-6 py-2 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            مطالعه کردم و موافقم
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-[#18191D] rounded-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2">رضایت‌نامه</h2>
          <p className="text-gray-400">
            لطفا رضایت‌نامه احراز هویت را مطالعه کرده و تایید کنید
          </p>
        </div>

        {/* Consent Summary */}
        <div className="bg-[#2A2D35] p-6 rounded-lg mb-6 border-r-4 border-r-[#4899EB]">
          <h3 className="text-white font-semibold mb-4">خلاصه رضایت‌نامه:</h3>
          <div className="space-y-3 text-gray-300 text-sm">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-[#4899EB] mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <p>رضایت برای جمع‌آوری و پردازش اطلاعات شخصی</p>
            </div>
            <div className="flex items-start">
              <svg className="w-5 h-5 text-[#4899EB] mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <p>تایید صحت اطلاعات ارائه شده</p>
            </div>
            <div className="flex items-start">
              <svg className="w-5 h-5 text-[#4899EB] mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <p>موافقت با استفاده از اطلاعات برای احراز هویت</p>
            </div>
            <div className="flex items-start">
              <svg className="w-5 h-5 text-[#4899EB] mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <p>تعهد به رعایت قوانین و مقررات پلتفرم</p>
            </div>
          </div>
        </div>

        {/* Consent Checkbox */}
        <div className="space-y-4 mb-8">
          <div className="flex items-start space-x-3 space-x-reverse">
            <input
              type="checkbox"
              id="consent"
              checked={authData.consentAccepted}
              onChange={(e) => updateAuthData({ consentAccepted: e.target.checked })}
              className="w-5 h-5 text-[#4899EB] bg-[#2A2D35] border-gray-600 rounded focus:ring-[#4899EB] focus:ring-2 mt-1"
            />
            <label htmlFor="consent" className="text-sm text-gray-300 leading-relaxed">
              رضایت‌نامه احراز هویت را مطالعه کرده و با تمام شرایط و ضوابط آن موافقم
            </label>
          </div>

          <button
            onClick={() => setShowConsentModal(true)}
            className="text-[#4899EB] text-sm hover:text-blue-400 transition-colors underline"
          >
            مطالعه کامل رضایت‌نامه
          </button>
        </div>

        {/* Digital Signature Section */}
        <div className="bg-[#2A2D35] rounded-lg p-6 mb-6">
          <h3 className="text-white font-semibold mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            امضای دیجیتال
          </h3>

          <p className="text-gray-400 text-sm mb-4">
            لطفا در کادر زیر امضای خود را انجام دهید
          </p>

          <div className="border-2 border-dashed border-gray-600 rounded-lg p-4 bg-white">
            <SignatureWrapper
              ref={signatureRef}
              canvasProps={{
                width: 500,
                height: 200,
                className: 'signature-canvas w-full h-48 border rounded',
                style: { width: '100%', height: '200px' }
              }}
              backgroundColor="white"
              penColor="black"
              onEnd={saveSignature}
            />
          </div>

          <div className="flex gap-3 mt-4">
            <button
              onClick={clearSignature}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors text-sm"
            >
              پاک کردن امضا
            </button>

            {authData.signature && (
              <div className="flex items-center text-green-400 text-sm">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                امضا ذخیره شد
              </div>
            )}
          </div>
        </div>

        {/* Generate PDF Section */}
        {authData.signature && authData.consentAccepted && (
          <div className="bg-[#1C1E24] rounded-lg p-6 mb-6 border border-[#4899EB]/30">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              تولید PDF رضایت‌نامه
            </h3>

            <p className="text-gray-400 text-sm mb-4">
              برای تکمیل فرآیند، PDF رضایت‌نامه امضا شده را تولید کنید
            </p>

            <button
              onClick={generatePDF}
              disabled={isGeneratingPdf || isLoadingUserInfo}
              className="flex items-center px-6 py-3 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
            >
              {isGeneratingPdf ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  در حال تولید PDF...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  تولید و دانلود PDF
                </>
              )}
            </button>

            {authData.consentPdf && (
              <div className="mt-4 space-y-3">
                {/* Upload Progress */}
                {authData.uploadProgress.consent < 100 && (
                  <div className="p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                    <div className="flex justify-between text-xs text-gray-400 mb-2">
                      <span>در حال آپلود PDF...</span>
                      <span>{authData.uploadProgress.consent}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-[#4899EB] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${authData.uploadProgress.consent}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Upload Status */}
                {authData.uploadStatus.consent ? (
                  <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                    <div className="flex items-center text-green-400 text-sm">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      PDF رضایت‌نامه با موفقیت آپلود شد
                    </div>
                  </div>
                ) : authData.consentPdf && authData.uploadProgress.consent === 0 ? (
                  <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                    <div className="flex items-center text-green-400 text-sm">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      PDF رضایت‌نامه با موفقیت تولید شد
                    </div>
                  </div>
                ) : authData.consentPdf && (
                  <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                    <div className="flex items-center text-yellow-400 text-sm">
                      <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      در حال آپلود PDF...
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Important Notice */}
        <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <h4 className="text-yellow-500 font-semibold text-sm mb-1">توجه مهم</h4>
              <p className="text-yellow-200 text-sm">
                با تایید این رضایت‌نامه، شما موافقت خود را با جمع‌آوری و پردازش اطلاعات شخصی‌تان برای اهداف احراز هویت اعلام می‌کنید.
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <button
            onClick={onBack}
            className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            بازگشت
          </button>
          <button
            onClick={handleNext}
            disabled={!authData.uploadStatus.consent}
            className="px-6 py-3 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {!authData.uploadStatus.consent && authData.consentPdf && (
              <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {!authData.uploadStatus.consent && authData.consentPdf ? 'در حال آپلود...' : 'مرحله بعد'}
          </button>
        </div>
      </div>

      {/* Consent Modal */}
      {showConsentModal && <ConsentModal />}
    </div>
  );
};

export default ConsentStep;
