"use client";
import React, { useState } from "react";

interface AuthData {
  birthDate: string;
  acceptedTerms: boolean;
  nationalCardFrontImage: string | null;
  nationalCardBackImage: string | null;
  selfieImage: string | null;
  consentAccepted: boolean;
  signature: string | null;
  consentPdf: string | null;
}

interface Props {
  authData: AuthData;
  updateAuthData: (data: Partial<AuthData>) => void;
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const PersonalInfoStep: React.FC<Props> = ({
  authData,
  updateAuthData,
  onNext,
  onBack,
  isFirstStep,
}) => {
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [selectedYear, setSelectedYear] = useState("");
  const [selectedMonth, setSelectedMonth] = useState("");
  const [selectedDay, setSelectedDay] = useState("");

  // Persian months
  const persianMonths = [
    { value: "01", label: "فروردین" },
    { value: "02", label: "اردیبهشت" },
    { value: "03", label: "خرداد" },
    { value: "04", label: "تیر" },
    { value: "05", label: "مرداد" },
    { value: "06", label: "شهریور" },
    { value: "07", label: "مهر" },
    { value: "08", label: "آبان" },
    { value: "09", label: "آذر" },
    { value: "10", label: "دی" },
    { value: "11", label: "بهمن" },
    { value: "12", label: "اسفند" },
  ];

  // Generate years (1300-1403)
  const currentPersianYear = 1403;
  const years = Array.from({ length: 104 }, (_, i) => currentPersianYear - i);

  // Generate days based on selected month
  const getDaysInMonth = (month: string) => {
    if (!month) return 31;
    const monthNum = parseInt(month);
    if (monthNum <= 6) return 31; // فروردین تا شهریور
    if (monthNum <= 11) return 30; // مهر تا بهمن
    return 29; // اسفند (simplified, not considering leap years)
  };

  const days = Array.from({ length: getDaysInMonth(selectedMonth) }, (_, i) => i + 1);

  // Update birth date when selectors change
  React.useEffect(() => {
    if (selectedYear && selectedMonth && selectedDay) {
      const birthDate = `${selectedYear}/${selectedMonth.padStart(2, '0')}/${selectedDay.padStart(2, '0')}`;
      updateAuthData({ birthDate });
    }
  }, [selectedYear, selectedMonth, selectedDay, updateAuthData]);

  // Parse existing birth date
  React.useEffect(() => {
    if (authData.birthDate) {
      const parts = authData.birthDate.split('/');
      if (parts.length === 3) {
        setSelectedYear(parts[0]);
        setSelectedMonth(parts[1]);
        setSelectedDay(parts[2]);
      }
    }
  }, [authData.birthDate]);

  const handleNext = () => {
    if (!authData.birthDate) {
      alert("لطفا تاریخ تولد خود را وارد کنید");
      return;
    }
    if (!authData.acceptedTerms) {
      alert("لطفا قوانین و مقررات را مطالعه کرده و تایید کنید");
      return;
    }
    onNext();
  };

  const TermsModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1E1E1E] rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-xl font-bold text-white">قوانین و مقررات اکسچنجیم</h3>
        </div>
        <div className="p-6 overflow-y-auto max-h-[60vh] text-gray-300 text-sm leading-6">
          <div className="space-y-4">
            <div className="bg-[#2A2D35] p-4 rounded-lg border-r-4 border-r-[#4899EB]">
              <h4 className="text-white font-semibold mb-2">ضروری!</h4>
              <p>
                مسئولیت کامل اطمینان از ایمن بودن رایانه‌ای که برای معاملات و مدیریت نقدینگی دارایی‌های دیجیتال استفاده می‌شود، بر عهده شما است. به دلیل مسائل مرتبط با نمایش صفحات وب و ملاحظات امنیتی، توصیه اکید می‌شود که کاربران از آخرین نسخه مرورگر گوگل کروم برای ورود به اکسچنجیم استفاده کنند.
              </p>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-2">واجد شرایط بودن و ثبت نام</h4>
              <p>
                با ثبت‌نام برای ایجاد حساب کاربری در اکسچنجیم یا استفاده از خدمات اکسچنجیم، شما تأکید و ضمانت می‌کنید که:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>به‌عنوان یک فرد، از سن قانونی برای تشکیل یک قرارداد معتبر برخوردار هستید</li>
                <li>ظرفیت قانونی کامل و مجوزهای کافی برای ورود به این توافق‌نامه را دارید</li>
                <li>قبلاً از استفاده از پلتفرم اکسچنجیم معلق یا حذف نشده‌اید</li>
                <li>حساب کاربری فعالی در اکسچنجیم ندارید</li>
              </ul>
            </div>

            <div className="bg-[#2A2D35] p-4 rounded-lg">
              <h4 className="text-white font-semibold mb-2">خطرات و ریسک‌ها</h4>
              <p>
                برای افزایش آگاهی شما از ریسک‌های مرتبط با معاملات دارایی‌های دیجیتال، اکسچنجیم شما را به موارد زیر یادآور می‌شود:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>ریسک نوسانات قیمت: قیمت دارایی‌های دیجیتال بسیار نوسان دارد</li>
                <li>ریسک نقدینگی: ممکن است نتوانید دارایی خود را به سرعت بفروشید</li>
                <li>ریسک عملیاتی: خطاهای عملیاتی ممکن است منجر به زیان شود</li>
                <li>ریسک مسدود شدن حساب: در صورت تقلب، حساب ممکن است مسدود شود</li>
              </ul>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-2">قوانین مبارزه با پولشویی</h4>
              <p>
                اکسچنجیم متعهد به رعایت قوانین مبارزه با پولشویی است. واریز و برداشت ریال فقط از طریق حساب بانکی که به نام صاحب حساب کاربری است، امکان‌پذیر می‌باشد. استفاده از حساب بانکی دیگران برای واریز یا برداشت مجاز نیست.
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 border-t border-gray-700 flex gap-4 justify-end">
          <button
            onClick={() => setShowTermsModal(false)}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            بستن
          </button>
          <button
            onClick={() => {
              updateAuthData({ acceptedTerms: true });
              setShowTermsModal(false);
            }}
            className="px-6 py-2 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            مطالعه کردم و موافقم
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-[#18191D] rounded-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2">اطلاعات شخصی</h2>
          <p className="text-gray-400">
            لطفا تاریخ تولد خود را وارد کرده و قوانین اکسچنجیم را مطالعه کنید
          </p>
        </div>

        <div className="space-y-6">
          {/* Birth Date Input */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              تاریخ تولد (شمسی)
            </label>
            <div className="grid grid-cols-3 gap-3">
              {/* Year */}
              <div>
                <label className="block text-xs text-gray-400 mb-1">سال</label>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="w-full px-3 py-3 bg-[#2A2D35] border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#4899EB] focus:border-transparent"
                >
                  <option value="">انتخاب سال</option>
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>

              {/* Month */}
              <div>
                <label className="block text-xs text-gray-400 mb-1">ماه</label>
                <select
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(e.target.value)}
                  className="w-full px-3 py-3 bg-[#2A2D35] border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#4899EB] focus:border-transparent"
                >
                  <option value="">انتخاب ماه</option>
                  {persianMonths.map((month) => (
                    <option key={month.value} value={month.value}>
                      {month.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Day */}
              <div>
                <label className="block text-xs text-gray-400 mb-1">روز</label>
                <select
                  value={selectedDay}
                  onChange={(e) => setSelectedDay(e.target.value)}
                  className="w-full px-3 py-3 bg-[#2A2D35] border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#4899EB] focus:border-transparent"
                  disabled={!selectedMonth}
                >
                  <option value="">انتخاب روز</option>
                  {days.map((day) => (
                    <option key={day} value={day.toString().padStart(2, '0')}>
                      {day}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Display selected date */}
            {authData.birthDate && (
              <div className="mt-2 p-2 bg-[#4899EB]/10 border border-[#4899EB]/30 rounded text-sm text-[#4899EB]">
                تاریخ انتخاب شده: {authData.birthDate}
              </div>
            )}
          </div>

          {/* Terms and Conditions */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3 space-x-reverse">
              <input
                type="checkbox"
                id="terms"
                checked={authData.acceptedTerms}
                onChange={(e) => updateAuthData({ acceptedTerms: e.target.checked })}
                className="w-5 h-5 text-[#4899EB] bg-[#2A2D35] border-gray-600 rounded focus:ring-[#4899EB] focus:ring-2 mt-1"
              />
              <label htmlFor="terms" className="text-sm text-gray-300 leading-relaxed">
                قوانین و مقررات اکسچنجیم را مطالعه کرده و می‌پذیرم
              </label>
            </div>

            <button
              onClick={() => setShowTermsModal(true)}
              className="text-[#4899EB] text-sm hover:text-blue-400 transition-colors underline"
            >
              مطالعه قوانین و مقررات
            </button>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <button
            onClick={onBack}
            disabled={isFirstStep}
            className={`px-6 py-3 rounded-lg transition-colors ${
              isFirstStep
                ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                : "bg-gray-700 text-white hover:bg-gray-600"
            }`}
          >
            بازگشت
          </button>
          <button
            onClick={handleNext}
            className="px-6 py-3 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            مرحله بعد
          </button>
        </div>
      </div>

      {/* Terms Modal */}
      {showTermsModal && <TermsModal />}
    </div>
  );
};

export default PersonalInfoStep;
