"use client";
import React, { useState } from "react";

interface AuthData {
  birthDate: string;
  acceptedTerms: boolean;
  nationalCardFrontImage: string | null;
  nationalCardBackImage: string | null;
  selfieImage: string | null;
  consentAccepted: boolean;
  signature: string | null;
  consentPdf: string | null;
}

interface Props {
  authData: AuthData;
  updateAuthData: (data: Partial<AuthData>) => void;
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const ReviewStep: React.FC<Props> = ({
  authData,
  onBack,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Here you would normally send the data to your API
      console.log("Submitting authentication data:", authData);

      setIsSubmitted(true);
    } catch (error) {
      console.error("Error submitting authentication:", error);
      alert("خطا در ارسال اطلاعات. لطفا دوباره تلاش کنید.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "وارد نشده";
    const date = new Date(dateString);
    return date.toLocaleDateString('fa-IR');
  };

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-[#18191D] rounded-lg p-8 text-center">
          <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-green-500/20 to-green-500/5 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>

          <h2 className="text-2xl font-bold mb-4 text-green-400">
            درخواست شما با موفقیت ارسال شد!
          </h2>

          <div className="space-y-4 text-gray-300">
            <p className="text-lg">
              اطلاعات احراز هویت شما برای بررسی ارسال شد
            </p>

            <div className="bg-[#2A2D35] p-4 rounded-lg border-r-4 border-r-green-500">
              <h3 className="text-white font-semibold mb-2">مراحل بعدی:</h3>
              <ul className="text-sm space-y-1 text-right">
                <li>• بررسی اطلاعات توسط تیم کارشناسان</li>
                <li>• تایید یا درخواست اصلاح مدارک</li>
                <li>• اطلاع‌رسانی از طریق ایمیل و پیامک</li>
                <li>• فعال‌سازی سطح طلایی حساب</li>
              </ul>
            </div>

            <div className="bg-[#1C1E24] p-4 rounded-lg">
              <p className="text-sm text-gray-400">
                زمان بررسی معمولاً بین 24 تا 72 ساعت است. از صبر و شکیبایی شما متشکریم.
              </p>
            </div>
          </div>

          <button
            onClick={() => window.location.href = '/dashboard'}
            className="mt-6 px-6 py-3 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            بازگشت به داشبورد
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-[#18191D] rounded-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2">بررسی نهایی</h2>
          <p className="text-gray-400">
            لطفا اطلاعات و مدارک ارسالی خود را بررسی کرده و سپس ارسال کنید
          </p>
        </div>

        <div className="space-y-6">
          {/* Personal Information */}
          <div className="bg-[#2A2D35] rounded-lg p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              اطلاعات شخصی
            </h3>
            <div className="grid grid-cols-1 gap-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-600">
                <span className="text-gray-400">تاریخ تولد:</span>
                <span className="text-white">{formatDate(authData.birthDate)}</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-400">قوانین و مقررات:</span>
                <span className={`text-sm px-2 py-1 rounded ${authData.acceptedTerms ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                  {authData.acceptedTerms ? 'تایید شده' : 'تایید نشده'}
                </span>
              </div>
            </div>
          </div>

          {/* Documents */}
          <div className="bg-[#2A2D35] rounded-lg p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              مدارک ارسالی
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* National Card Front */}
              <div className="border border-gray-600 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">جلوی کارت ملی</h4>
                {authData.nationalCardFrontImage ? (
                  <div className="space-y-2">
                    <img
                      src={authData.nationalCardFrontImage}
                      alt="جلوی کارت ملی"
                      className="w-full h-32 object-cover rounded"
                    />
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      آپلود شده
                    </span>
                  </div>
                ) : (
                  <span className="text-red-400 text-sm">آپلود نشده</span>
                )}
              </div>

              {/* National Card Back */}
              <div className="border border-gray-600 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">پشت کارت ملی</h4>
                {authData.nationalCardBackImage ? (
                  <div className="space-y-2">
                    <img
                      src={authData.nationalCardBackImage}
                      alt="پشت کارت ملی"
                      className="w-full h-32 object-cover rounded"
                    />
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      آپلود شده
                    </span>
                  </div>
                ) : (
                  <span className="text-red-400 text-sm">آپلود نشده</span>
                )}
              </div>

              {/* Selfie */}
              <div className="border border-gray-600 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">عکس سلفی</h4>
                {authData.selfieImage ? (
                  <div className="space-y-2">
                    <img
                      src={authData.selfieImage}
                      alt="عکس سلفی"
                      className="w-full h-32 object-cover rounded"
                    />
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      آپلود شده
                    </span>
                  </div>
                ) : (
                  <span className="text-red-400 text-sm">آپلود نشده</span>
                )}
              </div>
            </div>
          </div>

          {/* Consent and Signature */}
          <div className="bg-[#2A2D35] rounded-lg p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              رضایت‌نامه و امضا
            </h3>

            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-600">
                <span className="text-gray-400">وضعیت رضایت‌نامه:</span>
                <span className={`text-sm px-2 py-1 rounded ${authData.consentAccepted ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                  {authData.consentAccepted ? 'تایید شده' : 'تایید نشده'}
                </span>
              </div>

              <div className="flex justify-between items-center py-2 border-b border-gray-600">
                <span className="text-gray-400">امضای دیجیتال:</span>
                <span className={`text-sm px-2 py-1 rounded ${authData.signature ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                  {authData.signature ? 'انجام شده' : 'انجام نشده'}
                </span>
              </div>

              <div className="flex justify-between items-center py-2">
                <span className="text-gray-400">PDF رضایت‌نامه:</span>
                <span className={`text-sm px-2 py-1 rounded ${authData.consentPdf ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                  {authData.consentPdf ? 'تولید شده' : 'تولید نشده'}
                </span>
              </div>

              {/* Display signature preview */}
              {authData.signature && (
                <div className="mt-4">
                  <h4 className="text-white font-medium mb-2">پیش‌نمایش امضا:</h4>
                  <div className="bg-white p-2 rounded border">
                    <img
                      src={authData.signature}
                      alt="امضای دیجیتال"
                      className="max-h-20 object-contain"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Final Confirmation */}
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <h4 className="text-yellow-500 font-semibold text-sm mb-1">تایید نهایی</h4>
                <p className="text-yellow-200 text-sm">
                  با ارسال این اطلاعات، تایید می‌کنید که تمام اطلاعات صحیح و مدارک معتبر هستند.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <button
            onClick={onBack}
            disabled={isSubmitting}
            className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            بازگشت
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || !authData.birthDate || !authData.acceptedTerms || !authData.nationalCardFrontImage || !authData.nationalCardBackImage || !authData.selfieImage || !authData.consentAccepted || !authData.signature || !authData.consentPdf}
            className="px-6 py-3 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                در حال ارسال...
              </>
            ) : (
              'ارسال نهایی'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReviewStep;
