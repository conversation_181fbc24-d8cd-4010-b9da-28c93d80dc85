"use client";
import React, { forwardRef } from "react";
import dynamic from "next/dynamic";

// Dynamically import SignatureCanvas to avoid SSR issues
const SignatureCanvas = dynamic(() => import("react-signature-canvas"), {
  ssr: false,
});

interface SignatureWrapperProps {
  onEnd: () => void;
  backgroundColor?: string;
  penColor?: string;
  canvasProps?: any;
}

const SignatureWrapper = forwardRef<any, SignatureWrapperProps>(
  ({ onEnd, backgroundColor = "white", penColor = "black", canvasProps }, ref) => {
    return (
      <SignatureCanvas
        // @ts-ignore
        ref={ref}
        canvasProps={canvasProps}
        backgroundColor={backgroundColor}
        penColor={penColor}
        onEnd={onEnd}
      />
    );
  }
);

SignatureWrapper.displayName = "SignatureWrapper";

export default SignatureWrapper;
