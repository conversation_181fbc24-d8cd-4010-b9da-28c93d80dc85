"use client";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FiAlertTriangle, FiX, FiCheck } from "react-icons/fi";
import toast from "react-hot-toast";
import { updateNationalId } from "@/requests/dashboardRequest";

interface RejectedStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RejectedStatusModal: React.FC<RejectedStatusModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [nationalId, setNationalId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");

  if (!isOpen) return null;

  const validateNationalId = (id: string) => {
    // National ID must be 10 digits
    return /^\d{10}$/.test(id);
  };

  const handleSubmit = async () => {
    // Reset states
    setError("");

    // Validate national ID
    if (!validateNationalId(nationalId)) {
      setError("کد ملی باید ۱۰ رقم باشد");
      return;
    }

    try {
      setIsSubmitting(true);

      // Update national ID using the API function
      const result = await updateNationalId(nationalId);

      if (result.isError) {
        throw new Error(result.message);
      }

      // Show success message
      setIsSuccess(true);
      toast.success("کد ملی با موفقیت بروزرسانی شد");

      // Close modal after 2 seconds
      setTimeout(() => {
        onClose();
        // Reload page to refresh user data
        window.location.reload();
      }, 2000);

    } catch (err: any) {
      console.error("Error updating national ID:", err);
      setError(err.message || "خطا در بروزرسانی کد ملی. لطفا دوباره تلاش کنید.");
      toast.error("خطا در بروزرسانی کد ملی");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Backdrop with blur and gradient */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-[#18191D]/90 via-[#1C1E24]/90 to-[#18191D]/90 backdrop-filter backdrop-blur-lg"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          onClick={isSubmitting ? undefined : onClose}
        />

        {/* Modal Container */}
        <motion.div
          className="w-[90%] sm:w-[450px] max-w-md relative z-10"
          initial={{ scale: 0.9, y: 20, opacity: 0 }}
          animate={{ scale: 1, y: 0, opacity: 1 }}
          exit={{ scale: 0.9, y: 20, opacity: 0 }}
          transition={{ type: "spring", damping: 25, stiffness: 300 }}
        >
          {/* Modal Content */}
          <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-6 rounded-2xl border border-gray-800/30 shadow-[0_0_40px_rgba(0,0,0,0.3)] relative overflow-hidden">
            {/* Decorative top gradient line */}
            <div
              className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_15px_3px_rgba(235,87,87,0.5)]"
              style={{
                background:
                  "linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(235,87,87,1) 50%, rgba(211,211,211,0.1) 100%)",
              }}
            />

            {/* Close button - disabled during submission */}
            {!isSubmitting && (
              <button
                onClick={onClose}
                className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
              >
                <FiX className="w-5 h-5" />
              </button>
            )}

            <div className="flex flex-col items-center text-center">
              {/* Alert icon with animation */}
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: [0.8, 1.2, 1] }}
                transition={{ duration: 0.5 }}
                className={`w-16 h-16 rounded-full ${
                  isSuccess ? "bg-green-500/20" : "bg-red-500/20"
                } flex items-center justify-center mb-4`}
              >
                {isSuccess ? (
                  <FiCheck className="w-8 h-8 text-green-500" />
                ) : (
                  <FiAlertTriangle className="w-8 h-8 text-red-500" />
                )}
              </motion.div>

              <h3 className="text-xl font-medium text-white mb-2">
                {isSuccess ? "عملیات موفق" : "خطا در احراز هویت"}
              </h3>

              {isSuccess ? (
                <p className="text-gray-300 mb-6">
                  کد ملی شما با موفقیت بروزرسانی شد
                </p>
              ) : (
                <p className="text-gray-300 mb-6">
                  کد ملی شما با شماره موبایل هم خوانی ندارد
                  <br />
                  لطفا کد ملی صحیح خود را وارد نمایید
                </p>
              )}

              {!isSuccess && (
                <>
                  {/* National ID Input */}
                  <div className="w-full mb-6 relative group">
                    <div className="relative">
                      <input
                        type="text"
                        value={nationalId}
                        onChange={(e) => setNationalId(e.target.value.replace(/\D/g, '').slice(0, 10))}
                        name="nationalId"
                        placeholder="کد ملی"
                        disabled={isSubmitting}
                        className="w-full p-3 pl-4 border border-gray-700 rounded-xl bg-[#141416]/80 text-white h-[50px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right"
                      />
                      <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
                    </div>

                    {/* Error message */}
                    {error && (
                      <p className="text-red-500 text-sm mt-2 text-right">{error}</p>
                    )}
                  </div>

                  {/* Submit Button */}
                  <button
                    onClick={handleSubmit}
                    disabled={isSubmitting || nationalId.length !== 10}
                    className={`w-full ${
                      isSubmitting || nationalId.length !== 10
                        ? "bg-gray-600 cursor-not-allowed"
                        : "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 cursor-pointer"
                    } text-white py-3 px-6 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-blue-500/20 flex items-center justify-center`}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        در حال ارسال...
                      </>
                    ) : (
                      "ثبت کد ملی جدید"
                    )}
                  </button>
                </>
              )}

              {/* Close button for success state */}
              {isSuccess && (
                <button
                  onClick={onClose}
                  className="w-full bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white py-3 px-6 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-green-500/20"
                >
                  بستن
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default RejectedStatusModal;
