"use client";
import { addWithdrawal, getProfile } from "@/requests/dashboardRequest";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { motion, AnimatePresence } from "framer-motion";
import { FiX, FiCreditCard, FiAlertTriangle, FiClock, FiArrowLeft, FiDollarSign } from "react-icons/fi";
import TwoFactorVerificationModal from "./TwoFactorVerificationModal";

interface AddWithdrawModalProps {
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  getwithdrawalHandler: () => void;
  cards: {
    id: number;
    bank: { name: string; icon: string };
    number: string;
    sheba: string;
    status: "pending" | "approved" | "rejected";
  }[];
}

const AddWithdrawModal = ({
  setIsOpen,
  cards,
  getwithdrawalHandler,
}: AddWithdrawModalProps) => {
  const [cardId, setCardId] = useState<number | null>(null);
  const [selectedCardNumber, setSelectedCardNumber] = useState("");
  const [tomanAmount, setTomanAmount] = useState("");
  const [info, setInfo] = useState<{ tomanBalance?: string }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [showCardDropdown, setShowCardDropdown] = useState(false);
  const [is2FAModalOpen, setIs2FAModalOpen] = useState(false);

  const formatNumber = (number: string) => {
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  async function getProfileHandler() {
    setIsLoading(true);
    try {
      const result = await getProfile();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setInfo(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات کاربر");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    getProfileHandler();
  }, []);

  const handleCardSelect = (card: any) => {
    setCardId(card.id);
    setSelectedCardNumber(card.number);
    setShowCardDropdown(false);
  };

  const handleWithdrawAll = () => {
    if (info.tomanBalance) {
      // تبدیل مقدار به عدد و سپس به عدد صحیح (بدون اعشار)
      const balanceAsNumber = Number(info.tomanBalance);
      const integerBalance = Math.floor(balanceAsNumber).toString();
      setTomanAmount(integerBalance);
    }
  };

  const [isSubmitting, setIsSubmitting] = useState(false);

  // We don't need to store 2FA data separately since we already have cardId and tomanAmount

  // Function to handle withdrawal request
  const adduWithdrawal = async (code?: string) => {
    if (!cardId) return;

    setIsSubmitting(true);
    try {
      const result = await addWithdrawal(tomanAmount, cardId, code);
      if (result.isError) {
        if (result.requires2FA) {
          // Show 2FA modal
          setIs2FAModalOpen(true);
        } else {
          toast.error(result.message);
        }
      } else {
        toast.success("عملیات موفقیت‌آمیز بود");
        setIsOpen(false);
        getwithdrawalHandler();
      }
    } catch (error) {
      toast.error("خطا در ثبت درخواست برداشت");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to handle 2FA verification
  const handle2FAVerification = async (code: string) => {
    if (!cardId) {
      toast.error("لطفا کارت بانکی را انتخاب کنید");
      return;
    }

    try {
      setIsSubmitting(true);
      const result = await addWithdrawal(tomanAmount, cardId, code);
      if (result.isError) {
        toast.error(result.message || "خطا در ثبت درخواست برداشت");
      } else {
        toast.success("عملیات موفقیت‌آمیز بود");
        setIs2FAModalOpen(false);
        setIsOpen(false);
        getwithdrawalHandler();
      }
    } catch (error) {
      toast.error("خطا در ثبت درخواست برداشت");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key="withdraw-modal"
        className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Backdrop with blur and gradient */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-[#18191D]/90 via-[#1C1E24]/90 to-[#18191D]/90 backdrop-filter backdrop-blur-lg"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          onClick={() => setIsOpen(false)}
        />

        {/* Modal Container */}
        <motion.div
          className="w-[90%] sm:w-[85%] md:w-[80%] lg:w-[75%] max-w-[1200px] max-h-[90vh] overflow-y-auto relative z-10"
          initial={{ scale: 0.9, y: 20, opacity: 0 }}
          animate={{ scale: 1, y: 0, opacity: 1 }}
          exit={{ scale: 0.9, y: 20, opacity: 0 }}
          transition={{ type: "spring", damping: 25, stiffness: 300 }}
        >
          {/* Modal Content */}
          <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-6 md:p-8 rounded-2xl border border-gray-800/30 shadow-[0_0_40px_rgba(0,0,0,0.3)] relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_15px_3px_rgba(72,153,235,0.5)]"
              style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
            />
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:20px_20px] opacity-20"></div>

            {/* Header */}
            <div className="flex flex-col sm:flex-row items-center sm:justify-between gap-3 mb-8 relative z-10">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-gray-400 text-transparent bg-clip-text mb-2">برداشت</h1>
                <motion.div
                  className="h-1 w-16 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: 64 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                />
                <p className="text-sm text-gray-400 mt-2">
                  درخواست و تراکنش‌های برداشت وجه
                </p>
              </motion.div>

              <motion.button
                whileHover={{ scale: 1.1, rotate: 90 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setIsOpen(false)}
                className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-3 rounded-full cursor-pointer self-end sm:self-auto border border-gray-800/30 shadow-lg"
                initial={{ opacity: 0, rotate: 45 }}
                animate={{ opacity: 1, rotate: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <FiX className="w-5 h-5 text-gray-300" />
              </motion.button>
            </div>

            {/* Wallet Balance Card */}
            <motion.div
              className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-5 rounded-xl mb-8 border border-gray-800/30 shadow-lg relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>
              <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
                style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
              />

              <div className="flex items-center justify-between relative z-10">
                <div>
                  <p className="text-gray-400 mb-1">موجودی تومانی</p>
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 border-2 border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
                      <p className="text-gray-300">در حال بارگذاری...</p>
                    </div>
                  ) : (
                    <p className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 text-transparent bg-clip-text">
                      {sliceNumber(Math.floor(Number(info.tomanBalance || 0)).toString())} <span className="text-sm font-normal text-gray-400">تومان</span>
                    </p>
                  )}
                </div>
                <div className="bg-blue-500/20 p-3 rounded-full">
                  <FiDollarSign className="w-6 h-6 text-blue-400" />
                </div>
              </div>
            </motion.div>

            {/* Input Fields */}
            <motion.div
              className="bg-gradient-to-br from-[#1C1E24]/50 to-[#23262F]/50 p-6 rounded-2xl border border-gray-800/30 shadow-lg mb-8 relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="mb-6 flex gap-6 flex-col md:flex-row">
                {/* مبلغ برداشت */}
                <div className="flex-1">
                  <label className="block mb-3 text-sm font-medium text-gray-300">
                    مقدار برداشت به تومان
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <FiDollarSign className="text-gray-400" />
                    </div>
                    <input
                      type="text"
                      inputMode="numeric"
                      pattern="\d*"
                      className="border border-gray-700 rounded-xl p-3 pr-10 w-full text-right bg-[#23262F] text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-inner"
                      placeholder="مبلغ به تومان"
                      value={formatNumber(tomanAmount)}
                      onChange={(e) => {
                        const onlyDigits = e.target.value.replace(/\D/g, "");
                        setTomanAmount(onlyDigits);
                      }}
                    />
                  </div>
                  <div className="flex justify-between items-center mt-3">
                    <p className="text-sm text-gray-400">
                      کل موجودی شما:
                      {isLoading
                        ? " در حال بارگذاری..."
                        : ` ${sliceNumber(Math.floor(Number(info.tomanBalance || 0)).toString())} تومان`}
                    </p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleWithdrawAll}
                      className="text-sm text-blue-400 hover:text-blue-300 cursor-pointer flex items-center gap-1 transition-colors duration-300"
                    >
                      <span>برداشت کل موجودی</span>
                    </motion.button>
                  </div>
                </div>

                {/* انتخاب کارت */}
                <div className="flex-1 relative">
                  <label className="block mb-3 text-sm font-medium text-gray-300">
                    شماره کارت
                  </label>
                  <div
                    className="border border-gray-700 rounded-xl p-3 w-full text-right bg-[#23262F] text-white cursor-pointer flex items-center justify-between hover:border-gray-600 transition-colors duration-300 shadow-inner"
                    onClick={() => setShowCardDropdown(!showCardDropdown)}
                  >
                    <div className="flex items-center gap-2">
                      <FiCreditCard className="text-gray-400" />
                      <span>{selectedCardNumber || "برای انتخاب کلیک کنید"}</span>
                    </div>
                    <motion.div
                      animate={{ rotate: showCardDropdown ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <svg
                        className="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path d="M19 9l-7 7-7-7" />
                      </svg>
                    </motion.div>
                  </div>

                  <AnimatePresence>
                    {showCardDropdown && (
                      <motion.ul
                        className="absolute w-full mt-2 bg-gradient-to-br from-[#23262F] to-[#1C1E24] border border-gray-700 rounded-xl shadow-2xl z-20 max-h-60 overflow-y-auto text-sm text-white divide-y divide-gray-800/50"
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                      >
                        {cards?.map((card) => (
                          <motion.li
                            key={card.id}
                            onClick={() => handleCardSelect(card)}
                            className="p-3 hover:bg-gradient-to-r hover:from-blue-600/20 hover:to-blue-500/10 cursor-pointer transition-colors duration-300 flex items-center gap-3"
                            whileHover={{ x: 5 }}
                          >
                            <Image
                              className="rounded-md"
                              src={`https://api.exchangim.com/storage/${card?.bank?.icon}`}
                              height={35}
                              width={35}
                              alt="card"
                            />
                            <div className="flex flex-col">
                              <p className="font-medium">{card.bank.name}</p>
                              <p className="text-gray-400">{card.number}</p>
                            </div>
                          </motion.li>
                        ))}
                      </motion.ul>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              {/* Notices */}
              <div className="grid md:grid-cols-2 gap-5">
                <motion.div
                  className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl border border-yellow-800/30 shadow-lg relative overflow-hidden"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                >
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(234,179,8,0.5)]"
                    style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(234,179,8,1) 50%, rgba(211,211,211,0.1) 100%)' }}
                  />

                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500/20 p-2 rounded-lg mt-1">
                      <FiAlertTriangle className="w-5 h-5 text-yellow-500" />
                    </div>
                    <div>
                      <p className="text-yellow-400 font-medium mb-2">
                        لطفا در صورت استفاده از فیلتر شکن آن را خاموش کنید
                      </p>
                      <p className="text-sm text-gray-400">
                        به دستور فتا برای برداشت تومان IP شما باید کشور ایران باشد.
                      </p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl border border-blue-800/30 shadow-lg relative overflow-hidden"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                >
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(59,130,246,0.5)]"
                    style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(59,130,246,1) 50%, rgba(211,211,211,0.1) 100%)' }}
                  />

                  <div className="flex items-start gap-3">
                    <div className="bg-blue-500/20 p-2 rounded-lg mt-1">
                      <FiClock className="w-5 h-5 text-blue-500" />
                    </div>
                    <div>
                      <p className="text-blue-400 font-medium mb-2">
                        زمان بندی سیکل‌های پایا
                      </p>
                      <p className="text-sm text-gray-400">
                        روزهای عادی: برداشت‌های انجام‌شده تا ساعت 14:30 در همان روز
                        پردازش می‌شوند.
                      </p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Submit Button */}
            <motion.div
              className="text-center mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <motion.button
                whileHover={cardId !== null && tomanAmount && !isSubmitting ? { scale: 1.03, boxShadow: "0 0 20px rgba(59, 130, 246, 0.5)" } : {}}
                whileTap={cardId !== null && tomanAmount && !isSubmitting ? { scale: 0.97 } : {}}
                onClick={cardId !== null && tomanAmount && !isSubmitting ? () => adduWithdrawal() : undefined}
                disabled={cardId === null || !tomanAmount || isSubmitting}
                className={`px-8 py-4 rounded-xl relative overflow-hidden ${
                  cardId !== null && tomanAmount && !isSubmitting
                    ? "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white shadow-lg transition-all duration-300 cursor-pointer"
                    : "bg-gradient-to-r from-gray-700 to-gray-600 text-gray-300 cursor-not-allowed opacity-70"
                }`}
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 border-2 border-t-white border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
                    <span>در حال پردازش...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">درخواست برداشت</span>
                    <FiArrowLeft className="w-5 h-5" />
                  </div>
                )}

                {/* Animated glow effect */}
                {cardId !== null && tomanAmount && !isSubmitting && (
                  <motion.div
                    className="absolute top-0 left-0 right-0 h-full w-20 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    animate={{ x: ['-100%', '200%'] }}
                    transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1 }}
                  />
                )}
              </motion.button>

              {/* Animated underline */}
              {cardId !== null && tomanAmount && !isSubmitting && (
                <motion.div
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                  className="w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/50 to-blue-600/0 mt-1 mx-auto"
                  style={{ maxWidth: '200px' }}
                />
              )}
            </motion.div>
          </div>
        </motion.div>
      </motion.div>

      {/* 2FA Verification Modal */}
      {is2FAModalOpen && (
        <TwoFactorVerificationModal
          key="2fa-modal"
          onClose={() => setIs2FAModalOpen(false)}
          onVerify={handle2FAVerification}
          isSubmitting={isSubmitting}
        />
      )}
    </AnimatePresence>
  );
};

export default AddWithdrawModal;
