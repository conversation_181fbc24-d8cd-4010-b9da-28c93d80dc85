"use client";
import React, { useState } from "react";

import { FiX, FiShield } from "react-icons/fi";
import OTPInput from "react-otp-input";
import toast from "react-hot-toast";

interface TwoFactorVerificationModalProps {
  onClose: () => void;
  onVerify: (code: string) => Promise<void>;
  isSubmitting?: boolean;
}

const TwoFactorVerificationModal: React.FC<TwoFactorVerificationModalProps> = ({
  onClose,
  onVerify,
  isSubmitting: externalIsSubmitting,
}) => {
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [internalIsSubmitting, setInternalIsSubmitting] = useState<boolean>(false);

  // Use external isSubmitting state if provided, otherwise use internal state
  const isSubmitting = externalIsSubmitting !== undefined ? externalIsSubmitting : internalIsSubmitting;

  const handleVerify = async () => {
    if (verificationCode.length !== 6) {
      toast.error("لطفا کد تایید را وارد کنید");
      return;
    }

    setInternalIsSubmitting(true);
    try {
      await onVerify(verificationCode);
    } catch (error) {
      console.error("Error in 2FA verification:", error);
    } finally {
      setInternalIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="bg-[#1E1E1E] w-[90%] max-w-md rounded-xl shadow-2xl z-[101] relative">
        <div className="p-6">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-white p-1 rounded-full hover:bg-gray-700/50 transition-colors"
          >
            <FiX size={20} />
          </button>

          {/* Header */}
          <div className="text-center mb-6 mt-2">
            <div className="flex justify-center mb-4">
              <div className="bg-blue-500/20 p-4 rounded-full">
                <FiShield className="w-8 h-8 text-blue-400" />
              </div>
            </div>
            <h2 className="text-xl font-bold text-white mb-2">
              احراز هویت دو مرحله‌ای
            </h2>
            <p className="text-gray-400 text-sm">
              لطفا کد تایید را از برنامه Google Authenticator وارد کنید
            </p>
          </div>

          {/* Verification Code Input */}
          <div className="mb-6">
            <p className="text-white text-sm mb-3 text-center">کد تایید را وارد کنید:</p>
            <div className="flex justify-center" dir="ltr">
              <OTPInput
                value={verificationCode}
                onChange={setVerificationCode}
                numInputs={6}
                shouldAutoFocus={true}
                renderSeparator={<span className="w-2"></span>}
                renderInput={(props) => (
                  <input
                    {...props}
                    className="!w-[40px] !h-[45px] !text-white !font-bold !bg-[#141416]/80 !border !border-gray-700 !rounded-lg !mx-1 focus:!border-blue-500 focus:!shadow-[0px_0px_15px_rgba(72,153,235,0.3)] !transition-all !duration-300 !outline-none"
                  />
                )}
                containerStyle="flex justify-center items-center gap-1"
              />
            </div>
          </div>

          {/* Submit Button */}
          <button
            onClick={handleVerify}
            disabled={isSubmitting || verificationCode.length !== 6}
            className={`w-full ${
              !isSubmitting && verificationCode.length === 6
                ? "bg-[#4899EB] hover:bg-blue-500 cursor-pointer"
                : "bg-gray-600 cursor-not-allowed"
            } text-white py-3 px-6 rounded-lg font-medium transition-all duration-300`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                در حال پردازش...
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : (
              "تایید و ادامه"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TwoFactorVerificationModal;
