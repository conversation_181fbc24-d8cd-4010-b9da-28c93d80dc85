"use client";

import React, { useState } from "react";
import BTN from "@/components/form/BTN";
import { sendCard } from "@/requests/dashboardRequest";
import Image from "next/image";
import toast from "react-hot-toast";

interface AddCardModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddCardModal = ({ isOpen, onClose, onSuccess }: AddCardModalProps) => {
  const [number, setNumber] = useState<number | undefined>(undefined);

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    setNumber(value ? Number(value) : undefined);
  };

  const sendCardHandler = async () => {
    if (number) {
      const result = await sendCard(number);
      if (result.isError) {
        toast.error("ارسال ناموفق بود");
      } else {
        toast.success("کارت با موفقیت ارسال شد");
        setNumber(undefined);
        onSuccess();
        onClose();
      }
    } else {
      toast.error("شماره کارت الزامی است");
    }
  };

  if (!isOpen) return null;

  return (
    <>

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center inset-0 backdrop-blur-sm bg-black/30">
        <div className="bg-[#18191D] p-5 rounded-xl w-[90%] max-w-md">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-medium">افزودن حساب بانکی جدید</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <h2 className="font-light text-sm text-right mt-2">
            از طریق پر کردن فرم زیر حساب بانکی جهت واریز و برداشت اضافه کنید
          </h2>

          <div className="mt-4 flex gap-2.5 items-center text-right bg-[#23262F] p-3 rounded-md text-[#777E90] border-2 border-[#353945]">
            <Image
              className="w-6 h-6 rounded-full bg-[#F6BA47] flex-shrink-0"
              src="/images/taajob.png"
              height={1000}
              width={1000}
              alt="warning"
            />
            <p className="text-[#FCEAC8] text-xs">
              کاربر گرامی پس از افزودن حساب بانکی جدید، منتظر بررسی آن توسط
              کارشناسان ما باشید.
            </p>
          </div>

          <form action={sendCardHandler} className="mt-6">
            <fieldset className="relative">
              <label className="absolute bottom-10 right-3 font-light text-xs">
                افزودن حساب جدید
              </label>
              <input
                value={number ?? ""}
                onChange={handleNumberChange}
                className="text-right w-full bg-[#23262F] p-3 rounded-md text-[#777E90] border-2 border-[#353945] outline-none text-sm"
                type="tel"
                maxLength={16}
                placeholder="شماره کارت حساب بانکی خود را اضافه نمایید"
              />
            </fieldset>
            <div className="flex justify-between mt-6">
              <button
                type="button"
                onClick={onClose}
                className="bg-[#353945] px-6 py-2 rounded-lg cursor-pointer text-sm"
              >
                انصراف
              </button>
              <BTN
                label="افزودن"
                className="bg-[#1C1E24] px-6 py-2 rounded-lg cursor-pointer text-sm"
                type="submit"
              />
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default AddCardModal;
