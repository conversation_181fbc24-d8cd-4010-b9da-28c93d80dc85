"use client";
import { FC } from "react";
import { useFormStatus } from "react-dom";
import FormLoader from "../loader/formLoader";

interface IBTN {
  label: string;
  type?: "button" | "submit" | "reset";
  className: string;
}

const BTN: FC<IBTN> = ({ label, type, className }) => {
  const { pending } = useFormStatus();

  return (
    <button disabled={pending} className={className} type={type ?? "button"}>
      {pending ? <FormLoader /> : label}
    </button>
  );
};

export default BTN;
