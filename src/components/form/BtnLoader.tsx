"use client";
import { FC } from "react";
import FormLoader from "../loader/formLoader";

interface IBTN {
  label: string;
  type?: "button" | "submit" | "reset";
  className: string;
  pending: boolean;
  onClick: any;
}

const BtnLoader: FC<IBTN> = ({ label, type, className, pending, onClick }) => {
  return (
    <button
      onClick={onClick}
      disabled={pending}
      className={className}
      type={type ?? "button"}
    >
      {pending ? <FormLoader /> : label}
    </button>
  );
};

export default BtnLoader;
