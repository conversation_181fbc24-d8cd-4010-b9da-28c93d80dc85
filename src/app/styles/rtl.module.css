/* RTL specific styles */
.rtlSection {
  direction: rtl;
  text-align: right;
}

.rtlSection * {
  direction: rtl;
}

.rtlCard {
  direction: rtl;
  text-align: right;
  align-items: flex-start;
}

/* Override default flex direction for RTL layout */
@media (min-width: 768px) {
  .rtlSection > div {
    flex-direction: row-reverse;
  }
}

/* Ensure proper alignment of grid items */
.rtlSection .grid {
  direction: rtl;
}

/* Fix for images and icons in RTL context */
.rtlCard img {
  margin-left: auto;
  margin-right: auto;
}
