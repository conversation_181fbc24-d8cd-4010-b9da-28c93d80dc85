"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { getUserCurrency, addCryptoWithdrawal } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";

interface CurrencyItem {
  id: number;
  wallet_id: number;
  coin_type: string;
  coin_icon: string;
  balance: string;
  balance_usd: string;
  withdrawal_fees: string;
  minimum_withdrawal: string;
  maximum_withdrawal: string;
  coin_network: {
    id: number;
    network_id: number;
    network: {
      id: number;
      name: string;
    } | null;
  }[];
}

const Page: React.FC = () => {
  const searchParams = useSearchParams();
  const walletId = searchParams.get('id');

  const [userCurrency, setUserCurrency] = useState<CurrencyItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form states
  const [selectedWallet, setSelectedWallet] = useState<CurrencyItem | null>(null);
  const [walletAddress, setWalletAddress] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [showWalletDropdown, setShowWalletDropdown] = useState(false);

  // Get user currencies
  useEffect(() => {
    getCurrencyHandler();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showWalletDropdown && !target.closest('.wallet-dropdown-container')) {
        setShowWalletDropdown(false);
      }
    };

    if (showWalletDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showWalletDropdown]);

  const getCurrencyHandler = async () => {
    try {
      setIsLoading(true);
      const result = await getUserCurrency();
      if (result.isError) {
        toast.error("خطا در دریافت اطلاعات کیف پول‌ها");
      } else {
        // Filter wallets with balance > 0
        const walletsWithBalance = result.data.filter((wallet: CurrencyItem) =>
          parseFloat(wallet.balance) > 0
        );
        setUserCurrency(walletsWithBalance);

        // Auto-select wallet if ID is provided in URL
        if (walletId && walletsWithBalance.length > 0) {
          const targetWallet = walletsWithBalance.find((wallet: CurrencyItem) =>
            wallet.id.toString() === walletId
          );
          if (targetWallet) {
            setSelectedWallet(targetWallet);
            toast.success(`کیف پول ${targetWallet.coin_type} انتخاب شد`);
          } else {
            toast.error("کیف پول مورد نظر یافت نشد یا موجودی ندارد");
          }
        }
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات کیف پول‌ها");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle wallet selection
  const handleWalletSelect = (wallet: CurrencyItem, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    setSelectedWallet(wallet);
    setShowWalletDropdown(false);
    // Reset amount when wallet changes
    setAmount("");
  };

  // Calculate total amount needed (amount + fees)
  const calculateTotalAmount = (receiveAmount: number, fees: number) => {
    return receiveAmount + fees;
  };

  // Calculate maximum receivable amount (balance - fees)
  const calculateMaxReceivable = (balance: number, fees: number) => {
    return Math.max(0, balance - fees);
  };

  // Handle withdrawal submission
  const handleWithdrawal = async () => {
    if (!selectedWallet || !walletAddress.trim() || !amount.trim()) {
      toast.error("لطفا تمام فیلدها را تکمیل کنید");
      return;
    }

    const receiveAmount = parseFloat(amount);
    
    // Validate amount is a valid number
    if (isNaN(receiveAmount)) {
      toast.error("لطفا مبلغ معتبری وارد کنید");
      return;
    }

    const fees = parseFloat(selectedWallet.withdrawal_fees);
    const totalAmount = calculateTotalAmount(receiveAmount, fees);
    const balanceNum = parseFloat(selectedWallet.balance);
    const minWithdrawal = parseFloat(selectedWallet.minimum_withdrawal);
    const maxWithdrawal = parseFloat(selectedWallet.maximum_withdrawal);

    if (receiveAmount <= 0) {
      toast.error("مبلغ دریافتی باید بیشتر از صفر باشد");
      return;
    }

    if (receiveAmount < minWithdrawal) {
      toast.error(`حداقل مبلغ دریافتی ${minWithdrawal.toFixed(8)} ${selectedWallet.coin_type} است`);
      return;
    }

    if (maxWithdrawal < 99999999 && receiveAmount > maxWithdrawal) {
      toast.error(`حداکثر مبلغ دریافتی ${maxWithdrawal.toFixed(8)} ${selectedWallet.coin_type} است`);
      return;
    }

    if (totalAmount > balanceNum) {
      toast.error(`موجودی کافی نیست. برای دریافت ${receiveAmount.toFixed(8)} ${selectedWallet.coin_type} نیاز به ${totalAmount.toFixed(8)} ${selectedWallet.coin_type} موجودی دارید`);
      return;
    }

    // Get the first network for the selected wallet
    const firstNetwork = selectedWallet.coin_network[0];
    if (!firstNetwork || !firstNetwork.network) {
      toast.error("شبکه‌ای برای این کیف پول یافت نشد");
      return;
    }

    // Validate wallet address format (basic validation)
    if (walletAddress.length < 10) {
      toast.error("آدرس کیف پول وارد شده کوتاه است");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await addCryptoWithdrawal(
        walletAddress,
        selectedWallet.wallet_id,
        firstNetwork.network_id,
        totalAmount.toString()
      );
      if (result.isError) {
        toast.error(result.message || "خطا در ثبت درخواست برداشت");
      } else {
        toast.success(result.message || "درخواست برداشت با موفقیت ثبت شد");
        // Reset form
        setSelectedWallet(null);
        setWalletAddress("");
        setAmount("");
        setShowWalletDropdown(false);
        // Refresh wallet data
        getCurrencyHandler();
      }
    } catch (error) {
      toast.error("خطا در ثبت درخواست برداشت");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-transparent md:bg-[#18191D] p-6 rounded-2xl">
      {/* Withdraw Steps Section */}
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 sm:rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden group">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

        {/* Animated background elements */}
        <div className="absolute -top-10 -right-10 w-20 h-20 bg-red-500/10 rounded-full blur-xl group-hover:bg-red-500/20 transition-all duration-700"></div>
        <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-orange-500/10 rounded-full blur-xl group-hover:bg-orange-500/20 transition-all duration-700"></div>

        <div className="flex items-center justify-between mb-4 relative z-10">
          <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 p-2 rounded-lg hidden md:flex">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            مراحل برداشت رمز ارز
          </h2>
        </div>

        <div className="flex flex-col gap-3 lg:flex-row mt-4 relative z-10">
          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-red-500 to-red-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-red-500 to-red-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">1</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">کیف پول مورد نظر را انتخاب کنید</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">انتخاب کیف پول با موجودی مناسب</p>
            </div>
          </div>

          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-orange-500 to-orange-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-orange-500 to-orange-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">2</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">آدرس کیف پول مقصد را وارد کنید</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">آدرس کیف پول دریافت کننده</p>
            </div>
          </div>

          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-yellow-500 to-yellow-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-yellow-500 to-yellow-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">3</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">مبلغ دریافتی را تعیین کنید</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">مقدار ارز مورد نظر برای برداشت</p>
            </div>
          </div>

          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-green-500 to-green-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-green-500 to-green-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">4</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">تراکنش را تایید کنید</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">تایید نهایی درخواست برداشت</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 flex flex-col md:flex-row gap-5 sm:gap-6 rounded-xl mt-6 shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

        {/* Tips Section */}
        <div className="w-full md:w-[35%] relative z-10">
          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 hover:shadow-md">
            <div className="flex items-center mb-3">
              <div className="bg-gradient-to-r from-red-500 to-orange-500 p-2 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h2 className="mr-3 text-lg sm:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                نکات مهم
              </h2>
            </div>

            <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 mb-3 hover:border-red-500/30 transition-all duration-300">
              <p className="text-right text-sm text-gray-300">
                کاربر گرامی لطفا آدرس کیف پول و شبکه مورد نظر خود را قبل از برداشت
                با دقت بررسی کنید تا منجر به از دست رفتن سرمایه شما نشود.
              </p>
            </div>

            <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 mb-3 hover:border-red-500/30 transition-all duration-300">
              <p className="text-right text-sm text-gray-300">
                برداشت رمز ارز غیرقابل بازگشت است. لطفا از صحت آدرس کیف پول و شبکه
                انتخابی اطمینان حاصل کنید.
              </p>
            </div>

            <div className="bg-[#2A1810]/70 p-3 rounded-lg border border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
              <p className="text-right text-sm text-orange-300">
                <span className="font-bold text-orange-400">توجه:</span> مبلغ وارد شده، مبلغ دریافتی شما است. کارمزد برداشت به این مبلغ اضافه
                شده و کل مبلغ از کیف پول شما کسر می‌شود.
              </p>
              <div className="bg-[#1A1D21]/70 p-2 rounded-lg mt-2 text-xs text-gray-300">
                <strong className="text-orange-400">مثال:</strong> اگر 5 ترون دریافت کنید و کارمزد 2 ترون باشد،
                در مجموع 7 ترون از کیف پول شما کسر می‌شود.
              </div>
            </div>
          </div>
        </div>

        {/* Form Section */}
        <div className="w-full md:w-[65%] mt-4 md:mt-0 relative z-10">
          <div className="bg-[#23262F]/70 p-5 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 hover:shadow-md">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
                <span className="mr-3 text-gray-400">در حال بارگذاری کیف پول‌ها...</span>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Step 1: Wallet Selection */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-bold text-white">انتخاب کیف پول</h3>
                    {walletId && selectedWallet && (
                      <span className="text-xs text-green-400 bg-green-500/20 px-2 py-1 rounded-lg border border-green-500/30">
                        از لینک انتخاب شده
                      </span>
                    )}
                  </div>
                  <div className="relative wallet-dropdown-container">
                    <div
                      onClick={() => {
                        if (userCurrency.length > 0) {
                          setShowWalletDropdown(!showWalletDropdown);
                        }
                      }}
                      className={`cursor-pointer flex items-center justify-between p-3 bg-[#1A1D21]/70 rounded-lg border border-[#353945]/20 hover:border-red-500/30 transition-all duration-300 group ${userCurrency.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <div className="flex items-center">
                        {selectedWallet ? (
                          <>
                            <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
                              <Image
                                src={`https://api.exchangim.com/storage/${selectedWallet.coin_icon}`}
                                width={32}
                                height={32}
                                alt={selectedWallet.coin_type}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex flex-col">
                              <span className="text-white font-medium">{selectedWallet.coin_type}</span>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-[#B1B5C3]">
                                  موجودی: {parseFloat(selectedWallet.balance).toFixed(8)} {selectedWallet.coin_type}
                                </span>
                                <span className="text-xs text-[#4899EB]">
                                  (${selectedWallet.balance_usd})
                                </span>
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="bg-[#353945]/50 p-1 rounded-full mr-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </div>
                            <span className="text-gray-500">
                              {userCurrency.length === 0 ? "کیف پولی با موجودی یافت نشد" : "کیف پول مورد نظر را انتخاب کنید"}
                            </span>
                          </>
                        )}
                      </div>
                      <div className="bg-[#353945]/50 p-1 rounded-full group-hover:bg-red-500/20 transition-all">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 group-hover:text-red-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>

                    {/* Wallet Dropdown */}
                    {showWalletDropdown && userCurrency.length > 0 && (
                      <div className="absolute top-full left-0 right-0 mt-2 bg-[#1A1D21] border border-[#353945]/50 rounded-lg shadow-xl z-50 max-h-60 overflow-y-auto">
                        {userCurrency.map((wallet) => (
                          <div
                            key={wallet.id}
                            onClick={(e) => handleWalletSelect(wallet, e)}
                            className={`flex items-center p-3 cursor-pointer transition-colors border-b border-[#353945]/30 last:border-b-0 ${
                              selectedWallet?.id === wallet.id
                                ? 'bg-red-500/20 hover:bg-red-500/30 border-red-500/30'
                                : 'hover:bg-[#23262F]'
                            }`}
                          >
                            <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
                              <Image
                                src={`https://api.exchangim.com/storage/${wallet.coin_icon}`}
                                width={32}
                                height={32}
                                alt={wallet.coin_type}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex flex-col flex-1">
                              <div className="flex justify-between items-center">
                                <div className="flex items-center">
                                  <span className="text-white font-medium">{wallet.coin_type}</span>
                                  {selectedWallet?.id === wallet.id && (
                                    <span className="mr-2 text-red-400 text-xs">✓ انتخاب شده</span>
                                  )}
                                </div>
                                <span className="text-xs text-[#4899EB]">
                                  ${wallet.balance_usd}
                                </span>
                              </div>
                              <div className="flex justify-between items-center mt-1">
                                <span className="text-xs text-[#B1B5C3]">
                                  موجودی: {parseFloat(wallet.balance).toFixed(8)} {wallet.coin_type}
                                </span>
                                <span className="text-xs text-[#777E90]">
                                  شبکه: {wallet.coin_network[0]?.network?.name || 'نامشخص'}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Step 2: Wallet Address */}
                <div>
                  <h3 className={`text-lg font-bold mb-3 ${selectedWallet ? 'text-white' : 'text-gray-500'}`}>
                    آدرس کیف پول مقصد
                  </h3>
                  <input
                    type="text"
                    value={walletAddress}
                    onChange={(e) => setWalletAddress(e.target.value)}
                    placeholder="آدرس کیف پول دریافت کننده را وارد کنید"
                    className={`w-full p-3 bg-[#1A1D21]/70 border border-[#353945]/20 rounded-lg text-white placeholder-[#777E90] ${selectedWallet ? 'hover:border-orange-500/30 focus:border-orange-500/50' : 'opacity-70'} focus:outline-none transition-all duration-300`}
                    disabled={!selectedWallet}
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    لطفا آدرس کیف پول را با دقت وارد کنید. برداشت به آدرس اشتباه غیرقابل بازگشت است.
                  </p>
                  {selectedWallet && (
                    <div className="mt-3 p-2 bg-[#1A1D21]/50 rounded-lg border border-orange-500/20">
                      <span className="text-xs text-orange-400">
                        شبکه: {selectedWallet.coin_network[0]?.network?.name || 'نامشخص'}
                      </span>
                    </div>
                  )}
                </div>

                {/* Step 3: Amount */}
                <div>
                  <h3 className={`text-lg font-bold mb-3 ${selectedWallet && walletAddress ? 'text-white' : 'text-gray-500'}`}>
                    مبلغ دریافتی
                  </h3>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="مبلغ دریافتی را وارد کنید"
                    className={`w-full p-3 bg-[#1A1D21]/70 border border-[#353945]/20 rounded-lg text-white placeholder-[#777E90] ${selectedWallet && walletAddress ? 'hover:border-yellow-500/30 focus:border-yellow-500/50' : 'opacity-70'} focus:outline-none transition-all duration-300`}
                    step="0.00000001"
                    min="0"
                    disabled={!selectedWallet || !walletAddress}
                  />

                  {selectedWallet && (
                    <div className="mt-4 space-y-3">
                      {/* Balance Info */}
                      <div className="bg-[#1A1D21]/50 p-3 rounded-lg space-y-2">
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-[#B1B5C3]">موجودی کیف پول:</span>
                          <span className="text-white">
                            {parseFloat(selectedWallet.balance).toFixed(8)} {selectedWallet.coin_type}
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-[#B1B5C3]">حداکثر قابل دریافت:</span>
                          <span className="text-[#4899EB]">
                            {calculateMaxReceivable(parseFloat(selectedWallet.balance), parseFloat(selectedWallet.withdrawal_fees)).toFixed(8)} {selectedWallet.coin_type}
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-[#B1B5C3]">کارمزد برداشت:</span>
                          <span className="text-orange-400">
                            {parseFloat(selectedWallet.withdrawal_fees).toFixed(8)} {selectedWallet.coin_type}
                          </span>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            const maxReceivable = calculateMaxReceivable(parseFloat(selectedWallet.balance), parseFloat(selectedWallet.withdrawal_fees));
                            if (maxReceivable > 0) {
                              setAmount(maxReceivable.toFixed(8));
                            }
                          }}
                          className="text-xs text-[#4899EB] hover:text-[#5BA7F7] transition-colors bg-[#1A1D21] px-3 py-1 rounded border border-[#353945]/50 hover:border-[#4899EB]/50"
                          disabled={calculateMaxReceivable(parseFloat(selectedWallet.balance), parseFloat(selectedWallet.withdrawal_fees)) <= 0}
                        >
                          حداکثر دریافت
                        </button>
                        <button
                          onClick={() => {
                            const maxReceivable = calculateMaxReceivable(parseFloat(selectedWallet.balance), parseFloat(selectedWallet.withdrawal_fees));
                            if (maxReceivable > 0) {
                              setAmount((maxReceivable / 2).toFixed(8));
                            }
                          }}
                          className="text-xs text-[#4899EB] hover:text-[#5BA7F7] transition-colors bg-[#1A1D21] px-3 py-1 rounded border border-[#353945]/50 hover:border-[#4899EB]/50"
                          disabled={calculateMaxReceivable(parseFloat(selectedWallet.balance), parseFloat(selectedWallet.withdrawal_fees)) <= 0}
                        >
                          50%
                        </button>
                        <button
                          onClick={() => {
                            const maxReceivable = calculateMaxReceivable(parseFloat(selectedWallet.balance), parseFloat(selectedWallet.withdrawal_fees));
                            if (maxReceivable > 0) {
                              setAmount((maxReceivable * 0.25).toFixed(8));
                            }
                          }}
                          className="text-xs text-[#4899EB] hover:text-[#5BA7F7] transition-colors bg-[#1A1D21] px-3 py-1 rounded border border-[#353945]/50 hover:border-[#4899EB]/50"
                          disabled={calculateMaxReceivable(parseFloat(selectedWallet.balance), parseFloat(selectedWallet.withdrawal_fees)) <= 0}
                        >
                          25%
                        </button>
                      </div>

                      {/* Amount Calculation */}
                      {amount && parseFloat(amount) > 0 && (
                        <div className="bg-[#1A1D21]/50 p-3 rounded-lg border border-[#4899EB]/20">
                          <div className="space-y-2 text-sm">
                            <div className="text-center mb-2">
                              <span className="text-[#4899EB] font-medium">محاسبه برداشت</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-[#B1B5C3]">مبلغ دریافتی:</span>
                              <span className="text-green-400 font-medium">
                                {parseFloat(amount).toFixed(8)} {selectedWallet.coin_type}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-[#B1B5C3]">کارمزد برداشت:</span>
                              <span className="text-orange-400">
                                +{parseFloat(selectedWallet.withdrawal_fees).toFixed(8)} {selectedWallet.coin_type}
                              </span>
                            </div>
                            <div className="border-t border-[#353945]/50 pt-2">
                              <div className="flex justify-between items-center font-medium">
                                <span className="text-[#B1B5C3]">کل کسر از کیف پول:</span>
                                <span className="text-red-400">
                                  {calculateTotalAmount(parseFloat(amount), parseFloat(selectedWallet.withdrawal_fees)).toFixed(8)} {selectedWallet.coin_type}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Submit Button */}
                {selectedWallet && walletAddress && amount && (
                  <div className="flex justify-center mt-6">
                    <button
                      onClick={handleWithdrawal}
                      disabled={isSubmitting}
                      className={`${
                        isSubmitting ? "bg-green-700/70" : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600"
                      } px-6 py-3 rounded-lg text-white font-bold shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 flex items-center justify-center min-w-[200px]`}
                    >
                      {isSubmitting ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                          <span>در حال ثبت...</span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span>تایید و ارسال درخواست برداشت</span>
                        </div>
                      )}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
