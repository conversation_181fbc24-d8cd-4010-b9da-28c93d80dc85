"use client";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { getUserCurrency } from "@/requests/dashboardRequest";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  toman_sell_price: number;
  name: string;
  id: number;
}

const SwapSuccessPage = () => {
  const searchParams = useSearchParams();
  const [fromCurrency, setFromCurrency] = useState<CurrencyItem | null>(null);
  const [toCurrency, setToCurrency] = useState<CurrencyItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Parse transaction data from URL parameters
  let transactionData = null;
  try {
    const transactionParam = searchParams.get("transaction");
    if (transactionParam) {
      transactionData = JSON.parse(decodeURIComponent(transactionParam));
    }
  } catch (error) {
    console.error("Error parsing transaction data:", error);
  }
  const fromBalance = searchParams.get("from_balance") || "0";
  const toBalance = searchParams.get("to_balance") || "0";
  const fromCurrencyId = searchParams.get("from_currency_id") || "0";
  const toCurrencyId = searchParams.get("to_currency_id") || "0";

  useEffect(() => {
    // Fetch currency details only once when component mounts
    const getCurrencyDetails = async () => {
      try {
        setIsLoading(true);
        const result = await getUserCurrency();
        if (!result.isError) {
          const fromCurrencyDetails = result.data.find(
            (item: CurrencyItem) => item.id === Number(fromCurrencyId)
          );
          const toCurrencyDetails = result.data.find(
            (item: CurrencyItem) => item.id === Number(toCurrencyId)
          );

          setFromCurrency(fromCurrencyDetails || null);
          setToCurrency(toCurrencyDetails || null);
        }
      } catch (error) {
        console.error("Error fetching currency details:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getCurrencyDetails();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Format date and time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return {
      date: `${year}/${month}/${day}`,
      time: `${hours}:${minutes}:${seconds}`,
    };
  };

  const fromDateTime = transactionData?.from_transaction?.created_at
    ? formatDateTime(transactionData.from_transaction.created_at)
    : { date: "", time: "" };

  // If transaction data is missing, show a fallback UI
  if (!transactionData) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-[1200px] mx-auto bg-[#18191D] px-4 py-9 sm:p-5 md:p-6 rounded-2xl relative overflow-hidden text-center"
        style={{
          backgroundImage:
            "radial-gradient(circle at 10% 20%, rgba(35, 38, 47, 0.8) 0%, rgba(24, 25, 29, 0.9) 90%)",
          boxShadow: "0 10px 30px -10px rgba(0, 0, 0, 0.5)",
        }}
      >
        <div className="py-12">
          <div className="bg-yellow-500/20 p-4 rounded-full mb-4 mx-auto w-fit">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-yellow-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            اطلاعات تراکنش در دسترس نیست
          </h1>
          <p className="text-gray-400 mb-8">
            متأسفانه اطلاعات تراکنش تبدیل ارز در دسترس نیست.
          </p>
          <Link href="/dashboard/swap">
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="px-6 py-3 rounded-xl bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-blue-600/20 font-medium text-base relative overflow-hidden"
            >
              بازگشت به صفحه تبدیل ارز
            </motion.button>
          </Link>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-[1200px] mx-auto bg-[#18191D] px-4 py-9 sm:p-5 md:p-6 rounded-2xl relative overflow-hidden"
      style={{
        backgroundImage:
          "radial-gradient(circle at 10% 20%, rgba(35, 38, 47, 0.8) 0%, rgba(24, 25, 29, 0.9) 90%)",
        boxShadow: "0 10px 30px -10px rgba(0, 0, 0, 0.5)",
      }}
    >
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-600/20 via-blue-400/40 to-blue-600/20" />

      {/* Background glow effects */}
      <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-blue-500/5 blur-3xl" />
      <div className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-cyan-500/5 blur-3xl" />

      {/* Success Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex flex-col items-center justify-center mb-8 relative z-10"
      >
        <div className="bg-blue-500/20 p-4 rounded-full mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-blue-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
          تبدیل ارز با موفقیت انجام شد
        </h1>
        <p className="text-sm text-gray-400">
          جزئیات تراکنش تبدیل شما در زیر نمایش داده شده است
        </p>
      </motion.div>

      {/* Transaction Details */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="grid md:grid-cols-2 gap-6 relative z-10"
      >
        {/* From Transaction Summary */}
        <motion.div
          whileHover={{ boxShadow: "0 8px 30px rgba(0, 0, 0, 0.12)" }}
          className="bg-gradient-to-br from-[#23262F]/80 to-[#18191D] p-5 sm:p-6 rounded-2xl border border-gray-800/50 backdrop-blur-sm relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/20 to-blue-600/0"></div>
          <h2 className="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            جزئیات تراکنش خروجی
          </h2>

          <div className="space-y-4">
            {/* Transaction ID */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">شناسه تراکنش:</span>
              <span className="font-medium text-white">{transactionData?.from_transaction?.id}</span>
            </div>

            {/* Transaction Type */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">نوع تراکنش:</span>
              <span className="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-lg text-sm">
                خروج از تبدیل
              </span>
            </div>

            {/* Transaction Status */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">وضعیت:</span>
              <span className="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-lg text-sm">
                {transactionData?.from_transaction?.status === "done" ? "تکمیل شده" : transactionData?.from_transaction?.status}
              </span>
            </div>

            {/* Transaction Date & Time */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">تاریخ و زمان:</span>
              <div className="text-left">
                <span className="text-white block">{fromDateTime.time}</span>
                <span className="text-gray-400 text-sm block">{fromDateTime.date}</span>
              </div>
            </div>

            {/* Transaction Description */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">توضیحات:</span>
              <span className="text-white">{transactionData?.from_transaction?.description}</span>
            </div>
          </div>
        </motion.div>

        {/* To Transaction Summary */}
        <motion.div
          whileHover={{ boxShadow: "0 8px 30px rgba(0, 0, 0, 0.12)" }}
          className="bg-gradient-to-br from-[#23262F]/80 to-[#18191D] p-5 sm:p-6 rounded-2xl border border-gray-800/50 backdrop-blur-sm relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-600/0 via-cyan-400/20 to-cyan-600/0"></div>
          <h2 className="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            جزئیات تراکنش ورودی
          </h2>

          <div className="space-y-4">
            {/* Transaction ID */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">شناسه تراکنش:</span>
              <span className="font-medium text-white">{transactionData?.to_transaction?.id}</span>
            </div>

            {/* Transaction Type */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">نوع تراکنش:</span>
              <span className="bg-cyan-500/20 text-cyan-400 px-3 py-1 rounded-lg text-sm">
                ورود از تبدیل
              </span>
            </div>

            {/* Transaction Status */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">وضعیت:</span>
              <span className="bg-cyan-500/20 text-cyan-400 px-3 py-1 rounded-lg text-sm">
                {transactionData?.to_transaction?.status === "done" ? "تکمیل شده" : transactionData?.to_transaction?.status}
              </span>
            </div>

            {/* Transaction Date & Time */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">تاریخ و زمان:</span>
              <div className="text-left">
                <span className="text-white block">{fromDateTime.time}</span>
                <span className="text-gray-400 text-sm block">{fromDateTime.date}</span>
              </div>
            </div>

            {/* Transaction Description */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">توضیحات:</span>
              <span className="text-white">{transactionData?.to_transaction?.description}</span>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Amount Details */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-6 bg-gradient-to-br from-[#23262F]/80 to-[#18191D] p-5 sm:p-6 rounded-2xl border border-gray-800/50 backdrop-blur-sm relative overflow-hidden"
      >
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-600/0 via-purple-400/20 to-purple-600/0"></div>
        <h2 className="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
          جزئیات تبدیل
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* From Amount */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">مقدار ارز مبدا:</span>
            <div className="flex items-center">
              {!isLoading && fromCurrency && (
                <Image
                  src={`https://api.exchangim.com/storage/${fromCurrency.coin_icon}`}
                  height={20}
                  width={20}
                  alt={fromCurrency.coin_type}
                  className="ml-2 rounded-full"
                />
              )}
              <span className="font-medium text-white">
                {Number(transactionData?.from_amount).toFixed(4)} {fromCurrency?.coin_type}
              </span>
            </div>
          </div>

          {/* To Amount */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">مقدار ارز مقصد:</span>
            <div className="flex items-center">
              {!isLoading && toCurrency && (
                <Image
                  src={`https://api.exchangim.com/storage/${toCurrency.coin_icon}`}
                  height={20}
                  width={20}
                  alt={toCurrency.coin_type}
                  className="ml-2 rounded-full"
                />
              )}
              <span className="font-medium text-white">
                {Number(transactionData?.to_amount).toFixed(4)} {toCurrency?.coin_type}
              </span>
            </div>
          </div>

          {/* USD Value */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">ارزش دلاری:</span>
            <span className="font-medium text-white">${Number(transactionData?.usd_value).toFixed(2)}</span>
          </div>

          {/* Fee Amount */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">کارمزد تبدیل:</span>
            <span className="font-medium text-white">${Number(transactionData?.fee_amount).toFixed(4)}</span>
          </div>

          {/* From Balance Before */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">موجودی قبلی {fromCurrency?.coin_type}:</span>
            <div className="flex items-center">
              {!isLoading && fromCurrency && (
                <Image
                  src={`https://api.exchangim.com/storage/${fromCurrency.coin_icon}`}
                  height={20}
                  width={20}
                  alt={fromCurrency.coin_type}
                  className="ml-2 rounded-full"
                />
              )}
              <span className="font-medium text-white">
                {Number(transactionData?.from_transaction?.balance_before).toFixed(4)} {fromCurrency?.coin_type}
              </span>
            </div>
          </div>

          {/* From Balance After */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">موجودی فعلی {fromCurrency?.coin_type}:</span>
            <div className="flex items-center">
              {!isLoading && fromCurrency && (
                <Image
                  src={`https://api.exchangim.com/storage/${fromCurrency.coin_icon}`}
                  height={20}
                  width={20}
                  alt={fromCurrency.coin_type}
                  className="ml-2 rounded-full"
                />
              )}
              <span className="font-medium text-white">
                {Number(transactionData?.from_transaction?.balance_after).toFixed(4)} {fromCurrency?.coin_type}
              </span>
            </div>
          </div>

          {/* To Balance Before */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">موجودی قبلی {toCurrency?.coin_type}:</span>
            <div className="flex items-center">
              {!isLoading && toCurrency && (
                <Image
                  src={`https://api.exchangim.com/storage/${toCurrency.coin_icon}`}
                  height={20}
                  width={20}
                  alt={toCurrency.coin_type}
                  className="ml-2 rounded-full"
                />
              )}
              <span className="font-medium text-white">
                {Number(transactionData?.to_transaction?.balance_before).toFixed(4)} {toCurrency?.coin_type}
              </span>
            </div>
          </div>

          {/* To Balance After */}
          <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
            <span className="text-gray-400">موجودی فعلی {toCurrency?.coin_type}:</span>
            <div className="flex items-center">
              {!isLoading && toCurrency && (
                <Image
                  src={`https://api.exchangim.com/storage/${toCurrency.coin_icon}`}
                  height={20}
                  width={20}
                  alt={toCurrency.coin_type}
                  className="ml-2 rounded-full"
                />
              )}
              <span className="font-medium text-white">
                {Number(transactionData?.to_transaction?.balance_after).toFixed(4)} {toCurrency?.coin_type}
              </span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Current Balances */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6 relative z-10"
      >
        {/* From Currency Balance */}
        <motion.div
          whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.2)" }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
          className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-blue-500/20 shadow-md relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20"></div>
          <div className="absolute -top-10 -right-10 w-24 h-24 bg-blue-500/10 rounded-full blur-2xl"></div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">موجودی {fromCurrency?.name}</p>
              <div className="flex items-center">
                {!isLoading && fromCurrency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${fromCurrency.coin_icon}`}
                    height={24}
                    width={24}
                    alt={fromCurrency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <p className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                  {Number(fromBalance).toFixed(4)} {fromCurrency?.coin_type}
                </p>
              </div>
            </div>
            <div className="bg-blue-500/20 p-2 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
        </motion.div>

        {/* To Currency Balance */}
        <motion.div
          whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.2)" }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
          className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-cyan-500/20 shadow-md relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-600/20 via-cyan-400/40 to-cyan-600/20"></div>
          <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-cyan-500/10 rounded-full blur-2xl"></div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">موجودی {toCurrency?.name}</p>
              <div className="flex items-center">
                {!isLoading && toCurrency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${toCurrency.coin_icon}`}
                    height={24}
                    width={24}
                    alt={toCurrency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <p className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                  {Number(toBalance).toFixed(4)} {toCurrency?.coin_type}
                </p>
              </div>
            </div>
            <div className="bg-cyan-500/20 p-2 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-cyan-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-8"
      >
        <Link href="/dashboard">
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="px-6 py-3 rounded-xl bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-blue-600/20 font-medium text-base relative overflow-hidden w-full sm:w-auto"
          >
            بازگشت به داشبورد
          </motion.button>
        </Link>

        <Link href="/dashboard/swap">
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="px-6 py-3 rounded-xl bg-gradient-to-r from-cyan-600 to-cyan-500 hover:from-cyan-500 hover:to-cyan-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-cyan-600/20 font-medium text-base relative overflow-hidden w-full sm:w-auto"
          >
            تبدیل مجدد
          </motion.button>
        </Link>
      </motion.div>
    </motion.div>
  );
};

export default SwapSuccessPage;
