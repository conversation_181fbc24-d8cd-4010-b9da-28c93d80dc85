"use client";
import { getUserCurrency, swapCurrency } from "@/requests/dashboardRequest";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import Image from "next/image";
import React, { useState, useEffect, useRef } from "react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  toman_sell_price: number;
  toman_buy_price: number;
  balance_toman: number;
  balance: string;
  balance_usd: string;
  name: string;
  id: number;
  coin_network?: {
    id: number;
    network_id: number;
    currency_id: number;
    network: {
      id: number;
      name: string;
    } | null;
  }[];
}

// Animated background component with particles
const ParticlesBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let animationFrameId: number;

    // Set canvas dimensions
    const handleResize = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    // Particle properties
    const particlesArray: Particle[] = [];
    const numberOfParticles = 100;
    const colors = ['rgba(59, 130, 246, 0.5)', 'rgba(16, 185, 129, 0.5)', 'rgba(99, 102, 241, 0.5)'];

    class Particle {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      color: string;

      constructor() {
        this.x = Math.random() * (canvas?.width || window.innerWidth);
        this.y = Math.random() * (canvas?.height || window.innerHeight);
        this.size = Math.random() * 3 + 1;
        this.speedX = (Math.random() - 0.5) * 0.5;
        this.speedY = (Math.random() - 0.5) * 0.5;
        this.color = colors[Math.floor(Math.random() * colors.length)];
      }

      update() {
        this.x += this.speedX;
        this.y += this.speedY;

        // Bounce off edges
        if (this.x > (canvas?.width || window.innerWidth) || this.x < 0) {
          this.speedX = -this.speedX;
        }
        if (this.y > (canvas?.height || window.innerHeight) || this.y < 0) {
          this.speedY = -this.speedY;
        }
      }

      draw() {
        if (ctx) {
          ctx.fillStyle = this.color;
          ctx.beginPath();
          ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
          ctx.fill();
        }
      }
    }

    // Create particles
    const init = () => {
      for (let i = 0; i < numberOfParticles; i++) {
        particlesArray.push(new Particle());
      }
    };

    init();

    // Animation loop
    const animate = () => {
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw connections between particles
        for (let a = 0; a < particlesArray.length; a++) {
          for (let b = a; b < particlesArray.length; b++) {
            const dx = particlesArray[a].x - particlesArray[b].x;
            const dy = particlesArray[a].y - particlesArray[b].y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
              ctx.strokeStyle = `rgba(72, 153, 235, ${0.1 - distance/1000})`;
              ctx.lineWidth = 0.5;
              ctx.beginPath();
              ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
              ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
              ctx.stroke();
            }
          }
        }

        // Update and draw particles
        for (let i = 0; i < particlesArray.length; i++) {
          particlesArray[i].update();
          particlesArray[i].draw();
        }
      }

      animationFrameId = requestAnimationFrame(animate);
    };

    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  return (
    <motion.canvas
      ref={canvasRef}
      className="absolute inset-0 z-0 opacity-30"
      initial={{ opacity: 0 }}
      animate={{ opacity: 0.3 }}
      transition={{ duration: 1.5 }}
    />
  );
};

const Swap = () => {
  const [amount, setAmount] = useState("");
  const [estimatedAmount, setEstimatedAmount] = useState("");
  const [fromCurrency, setFromCurrency] = useState<number | null>(null);
  const [toCurrency, setToCurrency] = useState<number | null>(null);
  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userCurrency, setUserCurrency] = useState<CurrencyItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showConfetti, setShowConfetti] = useState(false);
  const router = useRouter();

  const fromCurrencyDetails = userCurrency.find((item) => item.id === fromCurrency);
  const toCurrencyDetails = userCurrency.find((item) => item.id === toCurrency);

  useEffect(() => {
    fetchUserCurrency();
  }, []);

  const fetchUserCurrency = async () => {
    try {
      setIsLoading(true);
      const result = await getUserCurrency();
      if (result.isError) {
        toast.error("خطا در دریافت اطلاعات ارزها");
      } else {
        setUserCurrency(result.data);
        // Set default currencies if available
        if (result.data.length >= 2) {
          setFromCurrency(result.data[0].id);
          setToCurrency(result.data[1].id);
        }
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات ارزها");
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate estimated amount based on exchange rates
  useEffect(() => {
    if (fromCurrencyDetails && toCurrencyDetails && amount) {
      // This is a simplified calculation - in a real app, you would use actual exchange rates
      // For now, we'll use the toman_sell_price as a reference for relative value
      if (fromCurrencyDetails.toman_sell_price && toCurrencyDetails.toman_sell_price) {
        const fromValue = parseFloat(amount) * fromCurrencyDetails.toman_sell_price;
        const toAmount = fromValue / toCurrencyDetails.toman_sell_price;
        setEstimatedAmount(toAmount.toFixed(8));
      }
    } else {
      setEstimatedAmount("");
    }
  }, [amount, fromCurrencyDetails, toCurrencyDetails]);

  const handleFromCurrencySelect = (currency: CurrencyItem) => {
    // Don't allow selecting the same currency for both from and to
    if (currency.id === toCurrency) {
      // If user selects the same currency, swap them
      setFromCurrency(toCurrency);
      setToCurrency(fromCurrency);
    } else {
      setFromCurrency(currency.id);
    }
    setShowFromDropdown(false);
  };

  const handleToCurrencySelect = (currency: CurrencyItem) => {
    // Don't allow selecting the same currency for both from and to
    if (currency.id === fromCurrency) {
      // If user selects the same currency, swap them
      setToCurrency(fromCurrency);
      setFromCurrency(toCurrency);
    } else {
      setToCurrency(currency.id);
    }
    setShowToDropdown(false);
  };

  const handleSwapCurrencies = () => {
    const temp = fromCurrency;
    setFromCurrency(toCurrency);
    setToCurrency(temp);
    // Reset amount when swapping to avoid confusion
    setAmount("");
    setEstimatedAmount("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!fromCurrency || !toCurrency || !amount) {
      toast.error("لطفا تمام فیلدها را پر کنید");
      return;
    }

    if (fromCurrency === toCurrency) {
      toast.error("ارز مبدا و مقصد نمی‌توانند یکسان باشند");
      return;
    }

    try {
      setLoading(true);
      const result = await swapCurrency(fromCurrency, toCurrency, amount);

      if (result.isError) {
        toast.error(result.message || "خطا در تبدیل ارز");
      } else {
        toast.success(result.message || "تبدیل ارز با موفقیت انجام شد");
        setShowConfetti(true); // Show confetti animation on success

        // If we have transaction data, redirect to success page
        if (result.data) {
          const {
            from_transaction,
            to_transaction,
            from_wallet_balance,
            to_wallet_balance,
            from_amount,
            to_amount,
            fee_amount,
            usd_value
          } = result.data;

          // Transaction data is available, proceed to success page

          // Create URL with transaction data
          try {
            // Create a simplified version of the transaction data to avoid encoding issues
            const simplifiedData = {
              from_transaction: {
                id: from_transaction.id,
                type: from_transaction.type,
                amount: from_transaction.amount,
                price: from_transaction.price,
                status: from_transaction.status,
                description: from_transaction.description,
                balance_before: from_transaction.balance_before,
                balance_after: from_transaction.balance_after,
                created_at: from_transaction.created_at
              },
              to_transaction: {
                id: to_transaction.id,
                type: to_transaction.type,
                amount: to_transaction.amount,
                price: to_transaction.price,
                status: to_transaction.status,
                description: to_transaction.description,
                balance_before: to_transaction.balance_before,
                balance_after: to_transaction.balance_after,
                created_at: to_transaction.created_at
              },
              from_amount,
              to_amount,
              fee_amount,
              usd_value
            };

            // Safely encode the transaction data
            const transactionParam = encodeURIComponent(JSON.stringify(simplifiedData));
            const successUrl = `/dashboard/swap/success?transaction=${transactionParam}&from_balance=${from_wallet_balance}&to_balance=${to_wallet_balance}&from_currency_id=${fromCurrency}&to_currency_id=${toCurrency}`;

            // Add a small delay to show the confetti animation before redirecting
            setTimeout(() => {
              // Redirect to success page
              router.push(successUrl);
            }, 1000);
          } catch (error) {
            console.error("Error encoding transaction data:", error);
            toast.error("خطا در انتقال به صفحه موفقیت");
            setShowConfetti(false);
          }
        }
      }
    } catch (error) {
      console.error(error);
      toast.error("خطا در تبدیل ارز");
    } finally {
      setLoading(false);
    }
  };

  // Confetti effect component
  const Confetti = () => {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 pointer-events-none"
      >
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute"
            initial={{
              top: "0%",
              left: `${Math.random() * 100}%`,
              width: `${Math.random() * 10 + 5}px`,
              height: `${Math.random() * 10 + 5}px`,
              backgroundColor: [
                "#3B82F6", // blue
                "#10B981", // green
                "#6366F1", // indigo
                "#F59E0B", // amber
                "#EC4899", // pink
              ][Math.floor(Math.random() * 5)],
              borderRadius: Math.random() > 0.5 ? "50%" : "0%",
              opacity: 0,
            }}
            animate={{
              top: "100%",
              opacity: [0, 1, 0],
              rotate: Math.random() * 360,
              scale: [0, 1, 0.5],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              ease: "easeOut",
              delay: Math.random() * 0.5,
            }}
          />
        ))}
      </motion.div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-[1200px] mx-auto bg-gradient-to-b from-[#18191D] to-[#1A1D21] px-4 py-9 sm:p-5 md:p-6 rounded-2xl shadow-xl border border-gray-800/50 relative overflow-hidden"
      style={{
        backgroundImage: "radial-gradient(circle at 10% 20%, rgba(35, 38, 47, 0.8) 0%, rgba(24, 25, 29, 0.9) 90%)",
        boxShadow: "0 10px 30px -10px rgba(0, 0, 0, 0.5)",
      }}
    >
      {/* Show confetti animation on successful swap */}
      <AnimatePresence>
        {showConfetti && <Confetti />}
      </AnimatePresence>
      {/* Animated particles background */}
      <ParticlesBackground />

      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-600/20 via-blue-400/40 to-blue-600/20" />

      <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full -mr-32 -mt-32 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-500/5 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-blue-500/5 blur-3xl" />
      <div className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-cyan-500/5 blur-3xl" />

      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex flex-col sm:flex-row items-center sm:justify-between gap-3 mb-8 relative z-10"
      >
        <div className="w-full text-right sm:text-right">
          <h1 className="text-3xl sm:text-4xl font-bold mb-2 bg-gradient-to-r from-white via-blue-100 to-gray-300 bg-clip-text text-transparent">تبدیل آسان ارز</h1>
          <p className="text-sm text-gray-400">تبدیل سریع و آسان ارزهای دیجیتال به یکدیگر با بهترین نرخ</p>
        </div>
      </motion.div>

      {isLoading ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex flex-col justify-center items-center h-64"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-blue-500/30 blur-xl rounded-full w-20 h-20 mx-auto"></div>
            <motion.div
              animate={{
                rotate: 360,
                scale: [1, 1.1, 1],
                boxShadow: ["0 0 10px rgba(59, 130, 246, 0.3)", "0 0 20px rgba(59, 130, 246, 0.5)", "0 0 10px rgba(59, 130, 246, 0.3)"]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }}
              className="relative z-10"
            >
              <svg className="animate-spin h-20 w-20 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </motion.div>
          </div>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-4 text-gray-400 font-medium"
          >
            در حال بارگذاری اطلاعات ارزها...
          </motion.p>

          {/* Animated particles */}
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-blue-500/30"
              style={{
                width: Math.random() * 10 + 5,
                height: Math.random() * 10 + 5,
              }}
              initial={{
                x: 0,
                y: 0,
                opacity: 0
              }}
              animate={{
                x: Math.random() * 200 - 100,
                y: Math.random() * 200 - 100,
                opacity: [0, 0.8, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: Math.random() * 2 + 1,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut",
              }}
            />
          ))}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-b from-[#23262F] to-[#1E2026] p-6 rounded-xl shadow-lg border border-gray-800/50 relative overflow-hidden"
          style={{
            boxShadow: "0 10px 30px -5px rgba(0, 0, 0, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.1)"
          }}
        >
          <div className="absolute top-0 right-0 w-48 h-48 bg-blue-500/5 rounded-full -mr-24 -mt-24 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-blue-500/5 rounded-full -ml-24 -mb-24 blur-3xl"></div>
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>
          <form onSubmit={handleSubmit} className="relative z-10">
            {/* From Currency Section */}
            <div className="mb-6">
              <label className="block text-gray-400 text-sm mb-2">ارز مبدا</label>
              <div className="relative">
                <div
                  className="bg-gradient-to-r from-[#1C1E24] to-[#23262F] p-4 rounded-xl flex justify-between items-center cursor-pointer border border-[#353945] hover:border-blue-500 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10"
                  onClick={() => setShowFromDropdown(!showFromDropdown)}
                >
                  {fromCurrencyDetails ? (
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full overflow-hidden mr-3 border-2 border-blue-500/30 p-0.5 shadow-lg shadow-blue-500/20">
                        <Image
                          src={`https://api.exchangim.com/storage/${fromCurrencyDetails.coin_icon}`}
                          width={40}
                          height={40}
                          alt={fromCurrencyDetails.name}
                          className="w-full h-full object-cover rounded-full"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-white">{fromCurrencyDetails.name}</p>
                        <div className="flex items-center">
                          <p className="text-xs text-blue-400 font-medium">{fromCurrencyDetails.coin_type}</p>
                          <span className="mx-1 text-gray-500">•</span>
                          <p className="text-xs text-gray-400">${fromCurrencyDetails.coin_price}</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-400">انتخاب ارز</p>
                  )}
                  <div className="bg-[#2A2D36] p-2 rounded-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-5 w-5 text-blue-400 transition-transform ${
                        showFromDropdown ? "transform rotate-180" : ""
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>

                {/* Dropdown */}
                <AnimatePresence>
                  {showFromDropdown && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute z-10 mt-2 w-full bg-[#1C1E24] rounded-xl shadow-lg border border-[#353945] max-h-60 overflow-y-auto"
                    >
                      {userCurrency.map((currency) => (
                        <div
                          key={currency.id}
                          className={`p-3 flex items-center justify-between hover:bg-[#353945] cursor-pointer transition-all duration-200 ${
                            currency.id === fromCurrency ? "bg-gradient-to-r from-blue-900/30 to-[#353945] border-r-2 border-blue-500" : ""
                          }`}
                          onClick={() => handleFromCurrencySelect(currency)}
                        >
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full overflow-hidden mr-2 border border-gray-700">
                              <Image
                                src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                                width={32}
                                height={32}
                                alt={currency.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div>
                              <p className="font-medium">{currency.name}</p>
                              <div className="flex items-center">
                                <p className="text-xs text-blue-400">{currency.coin_type}</p>
                                <span className="mx-1 text-gray-500 text-xs">•</span>
                                <p className="text-xs text-gray-400">${currency.coin_price}</p>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{sliceNumber(currency.balance)}</p>
                            <p className="text-xs text-gray-400">{currency.coin_type}</p>
                          </div>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Amount Input */}
              <div className="mt-4">
                <label className="block text-gray-400 text-sm mb-2">مقدار</label>
                <div className="relative">
                  <input
                    type="text"
                    value={amount}
                    onChange={(e) => {
                      // Only allow numbers and a single decimal point
                      const value = e.target.value;
                      if (/^[0-9]*\.?[0-9]*$/.test(value)) {
                        setAmount(value);
                      }
                    }}
                    className="w-full bg-[#1C1E24] p-4 rounded-xl border border-[#353945] focus:border-blue-500 outline-none transition-colors"
                    placeholder="مقدار را وارد کنید"
                  />
                  {fromCurrencyDetails && (
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center">
                      <span className="text-gray-400 mr-2">{fromCurrencyDetails.coin_type}</span>
                    </div>
                  )}
                </div>
                {fromCurrencyDetails && (
                  <div className="flex flex-col space-y-1 mt-1">
                    <p className="text-sm text-gray-300">
                      موجودی: <span className="text-blue-400 font-medium">{sliceNumber(fromCurrencyDetails.balance)} {fromCurrencyDetails.coin_type}</span>
                    </p>
                    <p className="text-xs text-gray-400">
                      معادل: {sliceNumber(Number(fromCurrencyDetails.balance_toman).toLocaleString())} تومان
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Swap Button */}
            <div className="flex justify-center my-6 relative">
              {/* Outer glow */}
              <div className="absolute inset-0 bg-blue-500/20 blur-xl rounded-full w-16 h-16 mx-auto"></div>

              {/* Animated rings */}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  className="absolute rounded-full border-2 border-blue-500/30 w-16 h-16"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.3, 0.5, 0.3],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <motion.div
                  className="absolute rounded-full border-2 border-cyan-400/20 w-14 h-14"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.2, 0.4, 0.2],
                    rotate: [0, -180, -360]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.2
                  }}
                />
              </div>

              <motion.button
                type="button"
                onClick={handleSwapCurrencies}
                whileHover={{ scale: 1.1, rotate: 180, boxShadow: "0 0 20px rgba(59, 130, 246, 0.5)" }}
                whileTap={{ scale: 0.9 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
                className="bg-gradient-to-r from-blue-600 to-blue-500 p-4 rounded-full border border-blue-400 hover:shadow-lg hover:shadow-blue-500/30 transition-all z-10 relative"
                style={{
                  boxShadow: "0 0 15px rgba(59, 130, 246, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.2)"
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                  />
                </svg>
              </motion.button>
            </div>

            {/* To Currency Section */}
            <div className="mb-6">
              <label className="block text-gray-400 text-sm mb-2">ارز مقصد</label>
              <div className="relative">
                <div
                  className="bg-gradient-to-r from-[#1C1E24] to-[#23262F] p-4 rounded-xl flex justify-between items-center cursor-pointer border border-[#353945] hover:border-blue-500 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10"
                  onClick={() => setShowToDropdown(!showToDropdown)}
                >
                  {toCurrencyDetails ? (
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full overflow-hidden mr-3 border-2 border-blue-500/30 p-0.5 shadow-lg shadow-blue-500/20">
                        <Image
                          src={`https://api.exchangim.com/storage/${toCurrencyDetails.coin_icon}`}
                          width={40}
                          height={40}
                          alt={toCurrencyDetails.name}
                          className="w-full h-full object-cover rounded-full"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-white">{toCurrencyDetails.name}</p>
                        <div className="flex items-center">
                          <p className="text-xs text-blue-400 font-medium">{toCurrencyDetails.coin_type}</p>
                          <span className="mx-1 text-gray-500">•</span>
                          <p className="text-xs text-gray-400">${toCurrencyDetails.coin_price}</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-400">انتخاب ارز</p>
                  )}
                  <div className="bg-[#2A2D36] p-2 rounded-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-5 w-5 text-blue-400 transition-transform ${
                        showToDropdown ? "transform rotate-180" : ""
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>

                {/* Dropdown */}
                <AnimatePresence>
                  {showToDropdown && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute z-10 mt-2 w-full bg-[#1C1E24] rounded-xl shadow-lg border border-[#353945] max-h-60 overflow-y-auto"
                    >
                      {userCurrency.map((currency) => (
                        <div
                          key={currency.id}
                          className={`p-3 flex items-center justify-between hover:bg-[#353945] cursor-pointer transition-all duration-200 ${
                            currency.id === toCurrency ? "bg-gradient-to-r from-blue-900/30 to-[#353945] border-r-2 border-blue-500" : ""
                          }`}
                          onClick={() => handleToCurrencySelect(currency)}
                        >
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full overflow-hidden mr-2 border border-gray-700">
                              <Image
                                src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                                width={32}
                                height={32}
                                alt={currency.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div>
                              <p className="font-medium">{currency.name}</p>
                              <div className="flex items-center">
                                <p className="text-xs text-blue-400">{currency.coin_type}</p>
                                <span className="mx-1 text-gray-500 text-xs">•</span>
                                <p className="text-xs text-gray-400">${currency.coin_price}</p>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{sliceNumber(currency.balance)}</p>
                            <p className="text-xs text-gray-400">{currency.coin_type}</p>
                          </div>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Estimated Amount */}
              <div className="mt-4">
                <label className="block text-gray-400 text-sm mb-2">مقدار تخمینی دریافتی</label>
                <div className="relative">
                  <input
                    type="text"
                    value={estimatedAmount}
                    readOnly
                    className="w-full bg-[#1C1E24] p-4 rounded-xl border border-[#353945] outline-none"
                    placeholder="مقدار تخمینی"
                  />
                  {toCurrencyDetails && (
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center">
                      <span className="text-gray-400 mr-2">{toCurrencyDetails.coin_type}</span>
                    </div>
                  )}
                </div>
                {toCurrencyDetails && (
                  <div className="flex flex-col space-y-1 mt-1">
                    <p className="text-sm text-gray-300">
                      موجودی: <span className="text-blue-400 font-medium">{sliceNumber(toCurrencyDetails.balance)} {toCurrencyDetails.coin_type}</span>
                    </p>
                    <p className="text-xs text-gray-400">
                      معادل: {sliceNumber(Number(toCurrencyDetails.balance_toman).toLocaleString())} تومان
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Fee Information */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-r from-[#1C1E24] to-[#23262F] p-5 rounded-xl mb-6 border border-[#353945] shadow-lg relative overflow-hidden"
              style={{
                boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.05)"
              }}
            >
              <div className="absolute top-0 right-0 w-24 h-24 bg-blue-500/5 rounded-full -mr-8 -mt-8 blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-500/10 rounded-full -ml-8 -mb-8 blur-2xl"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>

              <motion.h3
                initial={{ x: -10, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-sm font-medium mb-3 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                اطلاعات کارمزد و تبدیل
              </motion.h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 }}
                  whileHover={{ y: -2, boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)" }}
                  className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 backdrop-blur-sm"
                >
                  <p className="text-xs text-gray-400 flex justify-between items-center">
                    کارمزد تبدیل: <span className="text-white font-medium">0.5%</span>
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ y: -2, boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)" }}
                  className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 backdrop-blur-sm"
                >
                  <p className="text-xs text-gray-400 flex justify-between items-center">
                    زمان تقریبی تبدیل: <span className="text-green-400 font-medium">آنی</span>
                  </p>
                </motion.div>

                {fromCurrencyDetails && toCurrencyDetails && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 }}
                    whileHover={{ y: -2, boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)" }}
                    className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 md:col-span-2 backdrop-blur-sm"
                  >
                    <p className="text-xs text-gray-400 flex justify-between items-center">
                      نرخ تبدیل:
                      <span className="text-white font-medium">
                        1 {fromCurrencyDetails.coin_type} ≈ {sliceNumber((fromCurrencyDetails.toman_sell_price / toCurrencyDetails.toman_sell_price).toFixed(6))} {toCurrencyDetails.coin_type}
                      </span>
                    </p>
                  </motion.div>
                )}
              </div>
            </motion.div>

            {/* Submit Button */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              {/* Animated glow effect */}
              <div className="absolute inset-0 bg-blue-500/20 blur-xl rounded-xl mx-auto opacity-70"></div>
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-blue-400/30 to-blue-600/20 rounded-xl"
                animate={{
                  boxShadow: ["0 0 10px rgba(59, 130, 246, 0.3)", "0 0 20px rgba(59, 130, 246, 0.5)", "0 0 10px rgba(59, 130, 246, 0.3)"]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              <motion.button
                type="submit"
                disabled={loading || !fromCurrency || !toCurrency || !amount}
                whileHover={{
                  scale: 1.02,
                  boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.5)"
                }}
                whileTap={{ scale: 0.98 }}
                className={`w-full py-4 rounded-xl font-medium flex items-center justify-center relative z-10 ${
                  loading || !fromCurrency || !toCurrency || !amount
                    ? "bg-gray-600 cursor-not-allowed"
                    : "bg-gradient-to-r from-blue-600 to-blue-500 hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300"
                }`}
                style={{
                  boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.2)"
                }}
              >
                {loading ? (
                  <div className="flex items-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <svg className="h-5 w-5 text-white mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </motion.div>
                    در حال پردازش...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ x: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                      </svg>
                    </motion.div>
                    تبدیل ارز
                  </div>
                )}
              </motion.button>
            </motion.div>
          </form>
        </motion.div>
      )}
    </motion.div>
  );
};

export default Swap;
