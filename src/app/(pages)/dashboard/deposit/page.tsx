"use client";
import React, { useEffect, useState } from "react";
import CryptoModal from "@/components/dashboard/deposit/cryptoModal";
import NetworkModal from "@/components/dashboard/deposit/networkModal";
import Image from "next/image";
import { getUserCurrency, sendDeposit } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import { QRCodeCanvas } from "qrcode.react";
import { useSearchParams } from "next/navigation";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  name: string;
  id: number;
}

const Page = () => {
  const searchParams = useSearchParams();
  const [isCryptoModal, setIsCryptoModal] = useState(true);
  const [isNetworkModal, setIsNetworkModal] = useState(false);
  const [currencyId, setCurrencyId] = useState(0);
  const [networkId, setNetworkId] = useState(0);
  const [currency, setCurrency] = useState("");
  const [network, setNetwork] = useState("");
  const [walletAddress, setWalletAddress] = useState("");
  const [userCurrency, setUserCurrency] = useState<CurrencyItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const id = searchParams.get("id");
const [focusedInput, setFocusedInput] = useState<string | null>(null);


  const sendDepositHandler = async () => {
    try {
      setIsSubmitting(true);
      const result = await sendDeposit(currencyId, networkId);
      if (result.isError) {
        toast.error(result.message);
      } else {
        toast.success(result.message);
        setWalletAddress(result.data);
      }
    } catch (error) {
      toast.error("خطا در ارسال درخواست");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    getCurrencyHandler();
  }, []);

  const getCurrencyHandler = async () => {
    try {
      setIsLoading(true);
      const result = await getUserCurrency();
      setUserCurrency(result.data);
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات ارزها");
    } finally {
      setIsLoading(false);
    }
  };

  const selectedCurrency = userCurrency.find((item) => item.id === Number(id));

  useEffect(() => {
    if (selectedCurrency) {
      setCurrencyId(selectedCurrency.id);
      setCurrency(selectedCurrency?.coin_type);
    }
    id && setIsCryptoModal(false);
  }, [selectedCurrency]);

  // Loading spinner component
  const LoadingSpinner = () => (
    <div className="flex justify-center items-center py-10">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  );

  return (
    <div className="bg-transparent md:bg-[#18191D] p-6 rounded-2xl">
      {/* Deposit Steps Section */}
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 sm:rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden group">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

        {/* Animated background elements */}
        <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl group-hover:bg-blue-500/20 transition-all duration-700"></div>
        <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl group-hover:bg-green-500/20 transition-all duration-700"></div>

        <div className="flex items-center justify-between mb-4 relative z-10">
          <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-2 rounded-lg hidden md:flex">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            مراحل واریز رمز ارز
          </h2>
        </div>

        <div className="flex flex-col gap-3 lg:flex-row mt-4 relative z-10">
          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-500 to-blue-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-blue-500 to-blue-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">1</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">آدرس کیف پول را کپی کنید</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">آدرس کیف پول اختصاصی خود را کپی کنید</p>
            </div>
          </div>

          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-purple-500 to-purple-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-purple-500 to-purple-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">2</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">آدرس را به مبدا واریز بدهید</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">آدرس را در کیف پول مبدا وارد کنید</p>
            </div>
          </div>

          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-orange-500 to-orange-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-orange-500 to-orange-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">3</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">انتقال را تایید کنید</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">تراکنش را در کیف پول خود تایید کنید</p>
            </div>
          </div>

          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md flex items-center relative overflow-hidden group/step">
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-green-500 to-green-600"></div>
            <div className="flex justify-center items-center bg-gradient-to-br from-green-500 to-green-600 h-8 w-8 sm:h-10 sm:w-10 rounded-lg shadow-md group-hover/step:scale-110 transition-transform duration-300">
              <p className="text-white font-bold text-sm sm:text-base">4</p>
            </div>
            <div className="mr-3 sm:mr-4">
              <p className="text-sm sm:text-base font-medium">واریز با موفقیت انجام شد!</p>
              <p className="text-xs text-gray-400 mt-1 hidden md:block">پس از تایید شبکه، واریز انجام می‌شود</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tips and Instructions Section */}
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 flex flex-col md:flex-row gap-5 sm:gap-6 rounded-xl mt-6 shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

        {/* Animated background elements */}
        <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-500/5 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-20 w-32 h-32 bg-blue-500/5 rounded-full blur-xl"></div>

        <div className="w-full md:w-[35%] relative z-10">
          <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 hover:shadow-md">
            <div className="flex items-center mb-3">
              <div className="bg-gradient-to-r from-yellow-500 to-amber-500 p-2 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h2 className="mr-3 text-lg sm:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                نکات مهم
              </h2>
            </div>

            <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 mb-3 hover:border-yellow-500/30 transition-all duration-300">
              <p className="text-right text-sm text-gray-300">
                کاربر گرامی لطفا آدرس کیف پول و شبکه مورد نظر خود را قبل از واریز
                با دقت بررسی کنید تا منجر به از دست رفتن سرمایه شما نشود.
              </p>
            </div>

            <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 hover:border-yellow-500/30 transition-all duration-300">
              <p className="text-right text-sm text-gray-300">
                جهت امنیت بیشتر کیف پول های اختصاصی به صورت دوره ای تغییر خواهند
                یافت. قبل از واریز آدرس آن را بررسی بفرمایید.
              </p>
            </div>
          </div>

          <div className="mt-5">
            <div className="bg-[#23262F]/70 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 hover:shadow-md">
              <div className="flex items-center mb-3">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-500 p-2 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h2 className="mr-3 text-lg sm:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  آموزش
                </h2>
              </div>

              <div className="space-y-3">
                <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 hover:border-blue-500/30 transition-all duration-300 flex items-center justify-between cursor-pointer group">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <div className="flex items-center">
                    <p className="text-right text-sm text-gray-300">
                      چگونه به اکسچنجیم واریز کنم؟
                    </p>
                    <div className="bg-blue-500/20 p-1 rounded-full mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 hover:border-blue-500/30 transition-all duration-300 flex items-center justify-between cursor-pointer group">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <div className="flex items-center">
                    <p className="text-right text-sm text-gray-300">
                      شبکه چیست؟
                    </p>
                    <div className="bg-blue-500/20 p-1 rounded-full mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 hover:border-blue-500/30 transition-all duration-300 flex items-center justify-between cursor-pointer group">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <div className="flex items-center">
                    <p className="text-right text-sm text-gray-300">
                      چه شبکه ای باید انتخاب کنم؟
                    </p>
                    <div className="bg-blue-500/20 p-1 rounded-full mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20 hover:border-blue-500/30 transition-all duration-300 flex items-center justify-between cursor-pointer group">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <div className="flex items-center">
                    <p className="text-right text-sm text-gray-300">
                      کارمزد واریز چقدر است؟
                    </p>
                    <div className="bg-blue-500/20 p-1 rounded-full mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Crypto and Network Selection Section */}
        <div className="w-full md:w-[65%] mt-4 md:mt-0 relative z-10">
          {isLoading ? (
            <div className="bg-[#23262F]/70 p-6 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="bg-[#23262F]/70 p-5 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 hover:shadow-md">
              <div className="relative">
                {/* Progress bar */}
                <div className="absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-blue-500 via-purple-500 to-green-500 rounded-full"></div>

                {/* Steps */}
                <ol className="relative border-s border-[#353945]/50 ms-6">
                  <li className="mb-8">
                    <div className="absolute flex items-center justify-center w-8 h-8 -start-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-md">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>

                    <div className="mb-4">
                      <h3 className="flex items-center text-base sm:text-lg font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        رمز ارز مورد نظر خود را انتخاب نمایید
                      </h3>
                      <p className="text-xs text-gray-400 mt-1">انتخاب ارز دیجیتال برای واریز</p>
                    </div>

                    <div
                      onClick={() => setIsCryptoModal(true)}
                      className="cursor-pointer flex items-center justify-between p-3 bg-[#1A1D21]/70 rounded-lg border border-[#353945]/20 hover:border-blue-500/30 transition-all duration-300 group"
                    >
                      <div className="flex items-center">
                        {currency && (
                          <div className="bg-[#353945]/50 p-1 rounded-full ml-2">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                        )}
                        <p className="text-white text-sm">{currency || "انتخاب ارز"}</p>
                      </div>
                      <div className="bg-[#353945]/50 p-1 rounded-full group-hover:bg-blue-500/20 transition-all">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 group-hover:text-blue-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </li>

                  <li className="mb-8">
                    <div className="absolute flex items-center justify-center w-8 h-8 -start-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg shadow-md">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                      </svg>
                    </div>

                    <div className="mb-4">
                      <h3 className={`flex items-center text-base sm:text-lg font-bold ${currency ? 'bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent' : 'text-gray-500'}`}>
                        شبکه را انتخاب کنید (لطفا دقت نمایید)
                      </h3>
                      <p className="text-xs text-gray-400 mt-1">انتخاب شبکه انتقال ارز دیجیتال</p>
                    </div>

                    <div
                      onClick={() => currency && setIsNetworkModal(true)}
                      className={`cursor-pointer flex items-center justify-between p-3 bg-[#1A1D21]/70 rounded-lg border border-[#353945]/20 ${currency ? 'hover:border-purple-500/30' : 'opacity-70'} transition-all duration-300 group`}
                    >
                      <div className="flex items-center">
                        {network && (
                          <div className="bg-[#353945]/50 p-1 rounded-full ml-2">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                          </div>
                        )}
                        <p className="text-white text-sm">
                          {network ? network : (
                            <span className="text-gray-500">
                              {currency ? "شبکه ای که از طریق آن قرار است واریز شود را انتخاب نمایید" : "ابتدا ارز را انتخاب کنید"}
                            </span>
                          )}
                        </p>
                      </div>
                      <div className={`bg-[#353945]/50 p-1 rounded-full ${currency ? 'group-hover:bg-purple-500/20' : ''} transition-all`}>
                        <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 text-gray-400 ${currency ? 'group-hover:text-purple-400' : ''} transition-colors`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </li>

                  <li>
                    <div className="absolute flex items-center justify-center w-8 h-8 -start-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg shadow-md">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </div>

                    <div className="mb-4">
                      <h3 className={`flex items-center text-base sm:text-lg font-bold ${walletAddress ? 'bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent' : 'text-gray-500'}`}>
                        آدرس کیف پول اختصاصی خود را کپی کنید
                      </h3>
                      <p className="text-xs text-gray-400 mt-1">کپی آدرس کیف پول برای واریز</p>
                    </div>

                    {walletAddress ? (
                      <div className="bg-[#1A1D21]/70 p-4 rounded-lg border border-[#353945]/20 hover:border-green-500/30 transition-all duration-300">
                        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                          <div className="bg-white p-2 rounded-lg">
                            <QRCodeCanvas value={walletAddress} size={120} />
                          </div>

                          <div className="flex-1">
                            <div className="bg-[#23262F] p-3 rounded-lg flex items-center justify-between mb-3">
                              <p className="text-xs md:text-sm text-gray-300 truncate max-w-[200px]">{walletAddress}</p>
                              <button className="bg-[#353945] p-1.5 rounded-lg hover:bg-[#4A4F5E] transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                              </button>
                            </div>

                            <div className="bg-[#103923]/50 text-[#2FA766] p-2 rounded-lg text-xs text-center">
                              آدرس با موفقیت ایجاد شد. لطفا فقط {currency} را از طریق شبکه {network} به این آدرس واریز کنید.
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-[#1A1D21]/70 p-4 rounded-lg border border-[#353945]/20 opacity-70">
                        <div className="flex items-center justify-center">
                          <p className="text-gray-500 text-sm">
                            {currency && network ? "برای دریافت آدرس کیف پول، دکمه ثبت را بزنید" : "ابتدا ارز و شبکه را انتخاب کنید"}
                          </p>
                        </div>
                      </div>
                    )}
                  </li>
                </ol>
              </div>

              {currencyId && networkId && !walletAddress ? (
                <div className="flex justify-center mt-6">
                  <button
                    onClick={sendDepositHandler}
                    disabled={isSubmitting}
                    className={`${
                      isSubmitting ? "bg-green-700/70" : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600"
                    } px-6 py-2 rounded-lg text-white font-bold shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 flex items-center justify-center min-w-[120px]`}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                        <span>در حال ثبت...</span>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span>دریافت آدرس</span>
                      </div>
                    )}
                  </button>
                </div>
              ) : null}
            </div>
          )}
        </div>
      </div>

      {/* Recent Deposits Section */}
      <div className="hidden md:block landscape:block bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 rounded-xl mt-6 shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

        {/* Animated background elements */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500/5 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-500/5 rounded-full blur-xl"></div>

        <div className="flex flex-col md:flex-row justify-between items-center gap-4 py-4 relative z-10">
          <div className="flex items-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 p-2 rounded-lg mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
            </div>
            <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              واریز‌های اخیر شما
            </h1>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <select className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] text-white text-xs md:text-sm py-2 px-3 pr-8 rounded-lg appearance-none border border-[#353945]/50 focus:outline-none focus:border-[#353945]/80 cursor-pointer">
                <option>همه ارزها</option>
                <option>بیت‌کوین</option>
                <option>اتریوم</option>
                <option>تتر</option>
              </select>
              <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>

            <button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 px-4 py-2 rounded-lg text-white font-bold shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              تاریخچه واریز
            </button>
          </div>
        </div>

        <div className="overflow-x-auto mt-4 relative z-10">
          <table className="w-full text-center min-w-[800px]">
            <thead>
              <tr className="bg-[#23262F]/80 rounded-lg text-xs sm:text-sm text-gray-300">
                <th scope="col" className="px-4 py-3 rounded-r-lg">
                  تاریخ / زمان
                </th>
                <th scope="col" className="px-4 py-3">
                  نوع ارز
                </th>
                <th scope="col" className="px-4 py-3">
                  میزان
                </th>
                <th scope="col" className="px-4 py-3">
                  تاریخچه بلاکچین
                </th>
                <th scope="col" className="px-4 py-3 rounded-l-lg">
                  عملیات
                </th>
              </tr>
            </thead>
            <tbody>
              {[1, 2, 3, 4, 5].map((item, index) => (
                <tr key={item} className={`${index % 2 === 0 ? 'bg-[#1A1D21]/50' : 'bg-[#23262F]/30'} hover:bg-[#23262F]/70 transition-colors`}>
                  <td className="py-4 text-sm">
                    <div className="flex flex-col items-center">
                      <span className="font-medium">20:54:29</span>
                      <span className="text-xs text-gray-400">1402/02/15</span>
                    </div>
                  </td>
                  <td className="py-4">
                    <div className="flex items-center justify-center">
                      <div className="bg-[#353945]/50 p-1.5 rounded-full mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-base font-medium">BTC</p>
                        <p className="text-xs text-gray-400">Bitcoin</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4">
                    <div className="flex flex-col items-center">
                      <span className="font-medium">1.2</span>
                      <span className="text-xs text-gray-400">$42,567</span>
                    </div>
                  </td>
                  <td className="py-4">
                    <div className="flex items-center justify-center">
                      <span className="text-sm bg-[#23262F] py-1.5 px-3 rounded-lg mr-2 truncate max-w-[150px]">
                        16asfzv6...hbdu12rex
                      </span>
                      <button className="bg-[#353945] p-1.5 rounded-lg hover:bg-[#4A4F5E] transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                  <td className="py-4">
                    <div className="flex items-center justify-center gap-2">
                      <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-3 py-1.5 rounded-lg text-xs flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80">
                        جزییات
                      </button>
                      <button className="bg-[#103923]/50 text-[#2FA766] p-1.5 rounded-lg hover:bg-[#103923]/70 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex justify-between items-center mt-5 relative z-10">
          <div className="text-sm text-gray-400">
            نمایش 1 تا 5 از 12 مورد
          </div>
          <div className="flex items-center gap-2">
            <button className="bg-[#23262F] p-2 rounded-lg hover:bg-[#2A2D38] transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button className="bg-blue-600 p-2 rounded-lg hover:bg-blue-500 transition-colors">
              <span className="text-white font-medium">1</span>
            </button>
            <button className="bg-[#23262F] p-2 rounded-lg hover:bg-[#2A2D38] transition-colors">
              <span className="text-gray-400">2</span>
            </button>
            <button className="bg-[#23262F] p-2 rounded-lg hover:bg-[#2A2D38] transition-colors">
              <span className="text-gray-400">3</span>
            </button>
            <button className="bg-[#23262F] p-2 rounded-lg hover:bg-[#2A2D38] transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile View for Recent Deposits */}
      <div className="md:hidden landscape:hidden mt-6">
        <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden mb-4">
          <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

          <div className="flex items-center justify-between relative z-10">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 p-1.5 rounded-lg mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <h2 className="text-lg font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                واریز‌های اخیر
              </h2>
            </div>

            <button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 px-3 py-1.5 rounded-lg text-white text-xs font-bold shadow-md hover:shadow-lg transition-all duration-300">
              تاریخچه
            </button>
          </div>
        </div>

        <div className="space-y-3">
          {[1, 2, 3, 4, 5].map((item) => (
            <div key={item} className="bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24]/90 p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 hover:shadow-md group">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center">
                  <div className="bg-[#353945]/50 p-1.5 rounded-full mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-medium">BTC</p>
                    <p className="text-xs text-gray-400">Bitcoin</p>
                  </div>
                </div>
                <div className="bg-[#103923]/50 text-[#2FA766] px-2 py-1 rounded-lg text-xs">
                  تکمیل شده
                </div>
              </div>

              <div className="space-y-2.5">
                <div className="flex justify-between items-center bg-[#1A1D21]/70 p-2 rounded-lg">
                  <span className="text-gray-400 text-xs">تاریخ / زمان:</span>
                  <div className="text-right">
                    <p className="text-xs font-medium">20:54:29</p>
                    <p className="text-[10px] text-gray-500">1402/02/15</p>
                  </div>
                </div>

                <div className="flex justify-between items-center bg-[#1A1D21]/70 p-2 rounded-lg">
                  <span className="text-gray-400 text-xs">میزان:</span>
                  <div className="text-right">
                    <p className="text-xs font-medium">1.2 BTC</p>
                    <p className="text-[10px] text-gray-500">$42,567</p>
                  </div>
                </div>

                <div className="bg-[#1A1D21]/70 p-2 rounded-lg">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-gray-400 text-xs">تراکنش:</span>
                    <button className="bg-[#353945] p-1 rounded-lg hover:bg-[#4A4F5E] transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </button>
                  </div>
                  <p className="text-xs text-gray-300 bg-[#23262F] py-1 px-2 rounded text-center truncate">
                    16asfzv6...hbdu12rex
                  </p>
                </div>
              </div>

              <div className="flex justify-end mt-3">
                <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-3 py-1.5 rounded-lg text-xs flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  جزییات
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-center mt-4">
          <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-4 py-2 rounded-lg text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
            نمایش بیشتر
          </button>
        </div>
      </div>

      {isCryptoModal && (
        <CryptoModal
          userCurrency={userCurrency}
          setIsCryptoModal={setIsCryptoModal}
          setCurrencyId={setCurrencyId}
          setCurrency={setCurrency}
        />
      )}
      {isNetworkModal && (
        <NetworkModal
          setIsNetworkModal={setIsNetworkModal}
          currencyId={currencyId}
          setNetworkId={setNetworkId}
          setNetwork={setNetwork}
        />
      )}
    </div>
  );
};

export default Page;