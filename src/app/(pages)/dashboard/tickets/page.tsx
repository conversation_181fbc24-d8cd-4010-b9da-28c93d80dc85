"use client";
import DataLoader from "@/components/loader/dataLoader";
import { getTickets } from "@/requests/dashboardRequest";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import jalaali from "jalaali-js";
import AddTicket from "@/components/dashboard/tickets/addTciketModal";
import { motion } from "framer-motion";
import { FiPlus, FiSearch, FiEye, FiX, FiClock, FiTag, FiHash } from "react-icons/fi";

interface ITicket {
  status: string;
  subject: string;
  id: string;
  updated_at: string;
}

const Tickets: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [data, setData] = useState<ITicket[]>([]);

  useEffect(() => {
    getTicketsHandler();
  }, []);

  async function getTicketsHandler() {
    const result = await getTickets();
    if (result.isError) {
      toast.error("خطایی رخ داد");
    } else {
      setData(result.data);
      setIsLoading(false);
    }
  }

  // Function to convert English numbers to Persian numbers
  const toPersianNumber = (num: string): string => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num
      .toString()
      .replace(/[0-9]/g, (match) => persianDigits[parseInt(match)]);
  };

  const convertToJalali = (dateString: string): string => {
    const date = new Date(dateString);
    const jalaliDate = jalaali.toJalaali(date);
    // Convert to Persian numbers
    return toPersianNumber(
      `${jalaliDate.jy}/${jalaliDate.jm}/${jalaliDate.jd}`
    );
  };

  const renderMobileTicketItem = (item: ITicket, index: number) => (
    <motion.div
      key={index}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="bg-gradient-to-br from-[#1C1E24] to-[#23262F] p-5 rounded-xl w-[85vw] sm:w-[80vw] max-w-[600px] shadow-lg border border-gray-800/30 relative overflow-hidden"
      style={{
        boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)"
      }}
    >
      {/* Decorative element */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/20 via-blue-500 to-blue-500/20"></div>

      <div className="space-y-4 relative z-10">
        <div className="flex justify-between items-center border-b border-gray-800/50 pb-3">
          <div className="flex items-center gap-2">
            <FiTag className="text-blue-400" />
            <span className="text-[#B1B5C3] text-sm">شناسه تیکت:</span>
          </div>
          <span className="font-medium text-white bg-[#2A2D36] px-3 py-1 rounded-lg">{toPersianNumber(item.id)}</span>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FiHash className="text-blue-400" />
            <span className="text-[#B1B5C3] text-sm">موضوع:</span>
          </div>
          <span className="text-white font-medium">{item.subject}</span>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FiClock className="text-blue-400" />
            <span className="text-[#B1B5C3] text-sm">زمان آخرین تغییر:</span>
          </div>
          <span className="text-white">{convertToJalali(item.updated_at)}</span>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <span className="text-[#B1B5C3] text-sm">وضعیت:</span>
          </div>
          <div className="flex items-center gap-x-3">
            <span className="bg-gradient-to-r from-[#7B5D24] to-[#8B6D34] text-[#C5EFD8] px-3 py-1.5 rounded-lg font-medium flex items-center gap-2">
              <span className="inline-block w-2 h-2 rounded-full bg-green-400 animate-pulse"></span>
              {item.status === "user_response" && "در حال بررسی"}
            </span>
          </div>
        </div>

        <div className="flex justify-between items-center pt-3 border-t border-gray-800/50 mt-3">
          <span className="text-[#B1B5C3] text-sm">عملیات:</span>
          <div className="flex justify-center gap-3">
            <Link
              href={`/dashboard/tickets/${item.id}`}
              className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-300 shadow-md hover:shadow-lg"
            >
              <FiEye className="text-white" />
              مشاهده
            </Link>
            <button className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-300 shadow-md hover:shadow-lg">
              <FiX className="text-white" />
              بستن
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-b from-[#18191D] to-[#1C1E24] p-3 md:p-6 rounded-2xl shadow-lg border border-gray-800/30 relative overflow-hidden"
    >
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full filter blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/5 rounded-full filter blur-3xl"></div>

      <div className="relative z-10">
        <motion.div
          initial={{ y: -20 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="flex flex-col items-center md:flex-row md:justify-between md:items-center gap-4 mb-6"
        >
          <div className="text-center md:text-right w-full md:w-auto">
            <p className="text-xl md:text-2xl font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">لیست تیکت ها</p>
            <p className="font-light text-sm md:text-base text-gray-400">
              تمامی تیکت ها را از این لیست مشاهده کنید.
            </p>
          </div>
          <div className="w-full sm:w-[80vw] md:w-auto max-w-[600px] flex items-center gap-x-1.5 bg-[#23262F]/80 backdrop-blur-sm rounded-lg px-4 py-2.5 border border-gray-800/30 shadow-md">
            <input
              className="text-left font-light w-full md:w-auto bg-transparent outline-none text-white"
              type="text"
              placeholder="جست و جو"
            />
            <FiSearch className="w-5 h-5 text-gray-400" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="flex justify-center md:justify-end my-5"
        >
          <button
            onClick={() => setIsOpen(true)}
            className="cursor-pointer bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 rounded-lg w-[85vw] sm:w-[80vw] md:w-auto max-w-[600px] px-6 py-2.5 text-white font-medium inline-flex items-center justify-center gap-2 transition-all duration-300 shadow-lg hover:shadow-blue-500/20 transform hover:-translate-y-1"
          >
            <FiPlus className="text-white" />
            تیکت جدید
          </button>
        </motion.div>
      </div>
      {isLoading ? (
        <div className="flex justify-center">
          <DataLoader />
        </div>
      ) : (
        <>
          {/* Desktop View */}
          <div className="hidden md:block landscape:block overflow-x-auto">
            <table className="w-full text-center min-w-[800px]">
              <thead className="text-xs text-[#B1B5C3] uppercase bg-[#23262F]/80 backdrop-blur-sm rounded-t-lg">
                <tr>
                  <th scope="col" className="px-6 py-4 rounded-tr-lg">
                    شناسه تیکت
                  </th>
                  <th scope="col" className="px-6 py-4">
                    موضوع
                  </th>
                  <th scope="col" className="px-6 py-4">
                    زمان آخرین تغییر
                  </th>
                  <th scope="col" className="px-6 py-4">
                    وضعیت
                  </th>
                  <th scope="col" className="px-6 py-4 rounded-tl-lg">
                    عملیات
                  </th>
                </tr>
              </thead>
              <tbody className="text-[#FCFCFD]">
                {data.map((item, index) => (
                  <motion.tr
                    key={item.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    className="odd:bg-[#18191D]/80 even:bg-[#1C1E24]/80 backdrop-blur-sm hover:bg-[#23262F]/50 transition-colors duration-200"
                  >
                    <td className="py-4 px-6">
                      <span className="bg-[#2A2D36] px-3 py-1 rounded-lg font-medium">{toPersianNumber(item.id)}</span>
                    </td>
                    <td className="py-4 px-6 font-medium">{item.subject}</td>
                    <td className="py-4 px-6">{convertToJalali(item.updated_at)}</td>
                    <td className="py-4 px-6">
                      <span className="bg-gradient-to-r from-[#7B5D24] to-[#8B6D34] text-[#C5EFD8] px-3 py-1.5 rounded-lg font-medium inline-flex items-center gap-2">
                        <span className="inline-block w-2 h-2 rounded-full bg-green-400 animate-pulse"></span>
                        {item.status === "user_response" && "در حال بررسی"}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex justify-center gap-3 text-sm">
                        <Link
                          href={`/dashboard/tickets/${item.id}`}
                          className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-300 shadow-md hover:shadow-lg"
                        >
                          <FiEye className="text-white" />
                          مشاهده
                        </Link>
                        <button className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-300 shadow-md hover:shadow-lg">
                          <FiX className="text-white" />
                          بستن
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
          {/* Mobile View */}
          <div className="md:hidden landscape:hidden space-y-4 flex flex-col items-center">
            {data.map(renderMobileTicketItem)}
          </div>
        </>
      )}
      {isOpen && (
        <AddTicket
          setIsOpen={setIsOpen}
          getTicketsHandler={getTicketsHandler}
        />
      )}
    </motion.div>
  );
};

export default Tickets;
