"use client";

import {
  getProfile,
  getTicketDetail,
  sendTicketReply,
} from "@/requests/dashboardRequest";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { motion } from "framer-motion";
import {
  FiArrowLeft,
  FiSend,
  FiPaperclip,
  FiX,
  FiMoreHorizontal,
  FiMessageCircle,
  FiClock,
  FiCheckCircle
} from "react-icons/fi";

interface Ticket {
  id: string;
  created_at: string;
  message: string;
  file: string;
}

interface IUserProfile {
  firstname?: string;
  lastname?: string;
}

const TicketDetailPage: React.FC = () => {
  const params = useParams<{ id: string }>();
  const [data, setData] = useState<Ticket[]>([]);
  const [message, setMessage] = useState("");
  const [subject, setSubject] = useState("");
  const [info, setInfo] = useState<IUserProfile>({});
  const [loading, setLoading] = useState(true); // Add loading state

  useEffect(() => {
    getProfileHandler();
  }, []);

  async function getProfileHandler() {
    try {
      const result = await getProfile();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setInfo(result.data);
      }
    } catch {
      toast.error("خطایی در دریافت اطلاعات کاربر رخ داد");
    }
  }

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "short" });
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}, ${year} ${day} ${month}`;
  }

  useEffect(() => {
    if (params.id) {
      getDetailHandler();
    }
  }, [params.id]);

  const getDetailHandler = async () => {
    if (!params?.id) return;
    setLoading(true); // Set loading to true before fetching data
    try {
      const result = await getTicketDetail(params.id);
      setSubject(result.data.subject);
      setData(result.data.tickets);
    } catch {
      toast.error("خطایی در دریافت جزئیات تیکت رخ داد");
    } finally {
      setLoading(false); // Set loading to false after fetching data
    }
  };

  const sendReplyHandler = async (event: React.FormEvent) => {
    event.preventDefault();
    try {
      const result = await sendTicketReply(params.id, message);
      if (result.status === "success") {
        toast.success("پاسخ تیکت با موفقیت ارسال شد.");
        setMessage(""); // Clear the message input after sending
        getDetailHandler();
      }
    } catch {
      toast.error("خطایی در ارسال پاسخ رخ داد");
    }
  };

  // Loading component
  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-[#18191D] to-[#1C1E24] relative overflow-hidden"
      >
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/5 rounded-full filter blur-3xl"></div>

        <div className="relative z-10">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 360],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className="w-20 h-20 relative"
          >
            <div className="absolute inset-0 rounded-full border-4 border-blue-500/20"></div>
            <div className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent animate-spin"></div>
          </motion.div>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-6 text-white font-medium text-lg"
          >
            در حال بارگذاری...
          </motion.p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-[#18191D] to-[#1C1E24] p-5 relative overflow-hidden"
    >
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full filter blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/5 rounded-full filter blur-3xl"></div>

      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="w-full max-w-4xl bg-gradient-to-br from-[#1C1E24]/90 to-[#23262F]/90 p-6 rounded-2xl mt-5 h-[80vh] backdrop-blur-sm border border-gray-800/30 shadow-lg relative z-10"
      >
        <motion.div
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="flex items-center justify-between gap-x-3 border-b border-gray-800/50 pb-4"
        >
          <div className="flex items-center gap-x-4">
            <h2 className="text-2xl font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">{subject}</h2>
            <Link
              href="/dashboard/tickets"
              className="bg-[#23262F] hover:bg-[#2A2D36] rounded-lg p-2.5 transition-all duration-300 border border-gray-800/50 shadow-md flex items-center justify-center"
            >
              <FiArrowLeft className="w-4 h-4 text-gray-300" />
            </Link>
          </div>
          <div>
            <Link
              href="/dashboard/tickets"
              className="border border-gray-700 hover:border-gray-600 px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-300 hover:bg-[#2A2D36]/50"
            >
              <FiX className="text-red-400" />
              <span>بستن تیکت</span>
            </Link>
          </div>
        </motion.div>
        <div className="overflow-auto max-h-[60vh] no-scrollbar mt-5">
          <div className="space-y-10 border-b border-gray-800/50 pb-10">
            {data
              .filter((item) => item.file !== null)
              .map((item, index) => {
                const formattedDate = formatDate(item.created_at);
                return (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="bg-gradient-to-br from-[#1C1E24]/70 to-[#23262F]/70 p-5 rounded-xl border border-gray-800/30 shadow-md"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-x-3">
                        <div className="relative">
                          <Image
                            className="w-12 h-12 rounded-full border-2 border-blue-500/30 object-cover"
                            src="/images/avatar.png"
                            height={1000}
                            width={1000}
                            alt="avatar"
                          />
                          <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-[#1C1E24]"></div>
                        </div>
                        <div>
                          <p className="font-medium text-white">
                            {info.firstname} {info.lastname}
                          </p>
                          <div className="flex items-center gap-1 text-gray-400 text-xs">
                            <FiClock className="w-3 h-3" />
                            <span>{formattedDate}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <button className="text-gray-400 hover:text-white transition-colors duration-200">
                          <FiMoreHorizontal className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-4 bg-[#2A2D36]/50 p-4 rounded-lg border border-gray-800/30">
                      <p className="text-[#FCFCFD] leading-relaxed">{item.message}</p>
                    </div>
                    <div className="mt-2 flex justify-end">
                      <div className="flex items-center gap-1 text-xs text-blue-400">
                        <FiCheckCircle className="w-3 h-3" />
                        <span>خوانده شده</span>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
          </div>
          <div className="mt-5 space-y-10 pb-10">
            {data
              .filter((item) => item.file === null)
              .map((item, index) => {
                const formattedDate = formatDate(item.created_at);
                return (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="bg-gradient-to-br from-[#23262F]/70 to-[#2A2D36]/70 p-5 rounded-xl border border-gray-800/30 shadow-md"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-x-3">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            <FiMessageCircle className="w-6 h-6" />
                          </div>
                          <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-[#1C1E24]"></div>
                        </div>
                        <div>
                          <p className="font-medium text-white">پشتیبانی اکسچنجیم</p>
                          <div className="flex items-center gap-1 text-gray-400 text-xs">
                            <FiClock className="w-3 h-3" />
                            <span>{formattedDate}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <button className="text-gray-400 hover:text-white transition-colors duration-200">
                          <FiMoreHorizontal className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-4 bg-[#2A2D36]/50 p-4 rounded-lg border border-gray-800/30">
                      <p className="text-[#FCFCFD] leading-relaxed">{item.message}</p>
                    </div>
                  </motion.div>
                );
              })}
          </div>
        </div>
        <form
          onSubmit={sendReplyHandler}
          className="flex justify-between gap-x-2.5 mt-5"
        >
          <div className="flex items-center gap-x-1.5 bg-[#23262F] rounded-lg px-4.5 py-3 w-[85%] border border-gray-800/30 shadow-md focus-within:border-blue-500/50 transition-all duration-300">
            <FiPaperclip className="w-5 h-5 text-gray-400" />
            <input
              autoFocus
              className="font-light outline-none w-full bg-transparent text-white"
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="اینجا پاسخ دهید"
            />
          </div>
          <button
            type="submit"
            className="cursor-pointer flex justify-center items-center gap-x-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 md:w-[15%] w-24 rounded-lg p-3 text-white font-medium transition-all duration-300 shadow-lg hover:shadow-blue-500/20 transform hover:-translate-y-1"
          >
            <span>ارسال</span>
            <FiSend className="w-5 h-5" />
          </button>
        </form>
      </motion.div>
    </motion.div>
  );
};

export default TicketDetailPage;