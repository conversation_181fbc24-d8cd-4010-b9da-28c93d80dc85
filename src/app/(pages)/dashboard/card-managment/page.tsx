"use client";
import { getCards } from "@/requests/dashboardRequest";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import AddCardModal from "@/components/dashboard/card-managment/AddCardModal";
import { motion, AnimatePresence } from "framer-motion";
import { FiPlus, FiCreditCard, FiCheckCircle, FiXCircle, FiClock, FiAlertTriangle, FiRefreshCw } from "react-icons/fi";

type Bank = {
  id: number;
  name: string;
  icon: string;
};

type Card = {
  id: number;
  user_id: number;
  number: string;
  sheba: string;
  iban: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  bank: Bank;
};

const Page = () => {
  const [data, setData] = useState<Card[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    getCardsHandler();
  }, []);

  async function getCardsHandler() {
    setIsLoading(true);
    try {
      const result = await getCards();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setData(result.data);
      }
    } catch {
      toast.error("خطایی در دریافت اطلاعات رخ داد");
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }

  const handleRefresh = () => {
    setRefreshing(true);
    getCardsHandler();
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Format card number with spaces for better readability
  const formatCardNumber = (number: string) => {
    return number.replace(/(\d{4})(?=\d)/g, '$1 ');
  };

  // Format date to Persian format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  // Get status color and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'approved':
        return {
          icon: <FiCheckCircle className="w-5 h-5" />,
          color: 'from-green-600 to-green-500',
          text: 'تایید شده',
          bgColor: 'bg-green-500/10',
          textColor: 'text-green-500'
        };
      case 'rejected':
        return {
          icon: <FiXCircle className="w-5 h-5" />,
          color: 'from-red-600 to-red-500',
          text: 'رد شده',
          bgColor: 'bg-red-500/10',
          textColor: 'text-red-500'
        };
      default:
        return {
          icon: <FiClock className="w-5 h-5" />,
          color: 'from-amber-600 to-amber-500',
          text: 'در حال بررسی',
          bgColor: 'bg-amber-500/10',
          textColor: 'text-amber-500'
        };
    }
  };

  // Add custom style for no-scrollbar
  useEffect(() => {
    // Add the style to the document head
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      .no-scrollbar::-webkit-scrollbar {
        display: none;
      }
      .no-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    `;
    document.head.appendChild(styleElement);

    // Clean up on component unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div className="space-y-6 mx-auto w-[90vw] sm:w-[90vw] md:w-[91vw] md:max-w-[1100px] max-w-[760px] lg:w-[97vw] lg:max-w-[1550px] landscape:w-[93vw] landscape:max-w-[1200px] min-h-screen px-2 sm:px-4 md:px-6">
      {/* Header Section with Add Card */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-6 rounded-xl border border-gray-800/30 shadow-lg relative overflow-hidden"
      >
        {/* Decorative top gradient line */}
        <div
          className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
          style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
        />
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

        <div className="relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-center md:text-right">
              <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-gray-400 text-transparent bg-clip-text">
                افزودن حساب بانکی جدید
              </h2>
              <motion.div
                className="h-1 w-24 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full mt-2 mx-auto md:mx-0"
                initial={{ width: 0 }}
                animate={{ width: 96 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              />
              <p className="font-light text-sm sm:text-base mt-2 text-gray-400">
                از طریق دکمه زیر حساب بانکی جهت واریز و برداشت اضافه کنید
              </p>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={openModal}
              className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 px-6 py-3 rounded-xl text-white shadow-lg hover:shadow-blue-500/20 transition-all duration-300 flex items-center gap-2"
            >
              <FiPlus className="w-5 h-5" />
              <span className="font-medium">افزودن کارت جدید</span>
            </motion.button>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-6 bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl border border-yellow-800/30 shadow-lg relative overflow-hidden"
          >
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(234,179,8,0.5)]"
              style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(234,179,8,1) 50%, rgba(211,211,211,0.1) 100%)' }}
            />

            <div className="flex items-start gap-3">
              <div className="bg-yellow-500/20 p-2 rounded-lg mt-1 flex-shrink-0">
                <FiAlertTriangle className="w-5 h-5 text-yellow-500" />
              </div>
              <p className="text-yellow-100 text-sm">
                کاربر گرامی پس از افزودن حساب بانکی جدید، منتظر بررسی آن توسط کارشناسان ما باشید.
              </p>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Cards List Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-6 rounded-xl border border-gray-800/30 shadow-lg relative"
      >
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
          style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
        />

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-400 text-transparent bg-clip-text">
            کارت‌های بانکی
          </h1>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefresh}
            disabled={refreshing}
            className="bg-[#23262F] hover:bg-[#2A2D36] p-2.5 rounded-lg text-white transition-colors duration-300 flex items-center justify-center"
          >
            <FiRefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin text-blue-400' : 'text-gray-300'}`} />
          </motion.button>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((item) => (
              <div
                key={item}
                className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl w-full h-[200px] flex flex-col justify-center items-center border border-gray-800/30 shadow-lg relative overflow-hidden animate-pulse"
              >
                <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>
                <div className="w-14 h-14 border-4 border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mb-4 relative z-10"></div>
                <p className="text-gray-400 text-sm relative z-10">در حال بارگذاری...</p>
              </div>
            ))}
          </div>
        ) : (
          <>
            {data.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {data.map((card, index) => {
                  const statusInfo = getStatusInfo(card.status);

                  return (
                    <motion.div
                      key={card.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] rounded-xl border border-gray-800/30 shadow-lg relative overflow-hidden"
                      whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                    >
                      {/* Card top gradient line */}
                      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
                        style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
                      />

                      {/* Bank card design */}
                      <div className="p-5">
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex items-center gap-2">
                            <Image
                              src={`https://api.exchangim.com/storage/${card.bank.icon}`}
                              width={40}
                              height={40}
                              alt={card.bank.name}
                              className="rounded-lg"
                            />
                            <div>
                              <h3 className="font-medium text-white">{card.bank.name}</h3>
                              <p className="text-xs text-gray-400">{formatDate(card.created_at)}</p>
                            </div>
                          </div>

                          <div className={`flex items-center gap-x-1.5 bg-gradient-to-r ${statusInfo.color} px-3 py-1.5 rounded-lg text-white text-sm`}>
                            {statusInfo.icon}
                            <span>{statusInfo.text}</span>
                          </div>
                        </div>

                        <div className="space-y-3 mt-4">
                          <div className="bg-[#1C1E24]/70 p-3 rounded-lg border border-gray-800/30">
                            <p className="text-xs text-gray-400 mb-1">شماره کارت</p>
                            <p className="text-lg font-mono tracking-wider">{formatCardNumber(card.number)}</p>
                          </div>

                          <div className="bg-[#1C1E24]/70 p-3 rounded-lg border border-gray-800/30">
                            <p className="text-xs text-gray-400 mb-1">شماره شبا</p>
                            <p className="text-sm font-mono tracking-wider overflow-x-auto no-scrollbar">{card.sheba}</p>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex flex-col items-center justify-center py-12 text-center"
              >
                <div className="bg-[#23262F] p-6 rounded-full mb-4">
                  <FiCreditCard className="w-10 h-10 text-gray-400" />
                </div>
                <h3 className="text-xl font-medium text-white mb-2">هیچ کارت بانکی ثبت نشده است</h3>
                <p className="text-gray-400 max-w-md mb-6">برای افزودن کارت بانکی جدید، روی دکمه بالا کلیک کنید.</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={openModal}
                  className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 px-5 py-2.5 rounded-lg text-white inline-flex items-center justify-center gap-2 transition-all duration-300 shadow-lg hover:shadow-blue-500/20"
                >
                  <FiPlus className="text-white" />
                  افزودن کارت جدید
                </motion.button>
              </motion.div>
            )}
          </>
        )}
      </motion.div>

      <AnimatePresence>
        {isModalOpen && (
          <AddCardModal
            isOpen={isModalOpen}
            onClose={closeModal}
            onSuccess={getCardsHandler}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Page;
