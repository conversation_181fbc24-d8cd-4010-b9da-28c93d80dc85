"use client";

import Live<PERSON>hart from "@/components/dashboard/home/<USER>/liveChart";
import LiveChartInfo from "@/components/dashboard/home/<USER>/liveChart/liveChartInfo";
import PieChartInfo from "@/components/dashboard/home/<USER>/pieChart";
import SpeechChart from "@/components/dashboard/home/<USER>/speechChart";
import Image from "next/image";
import React from "react";

const Page = () => {
  return (
    <div className="space-y-6 p-4 md:p-0">
      {/* First row - Modified for mobile */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="bg-[#18191D] p-4 rounded-2xl">
          <SpeechChart />
        </div>
        <div className="bg-[#18191D] p-4 rounded-2xl flex items-center">
          <PieChartInfo />
        </div>
      </div>

      {/* Second row */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="bg-[#18191D] p-4 md:p-6 rounded-2xl flex flex-col items-center md:items-start w-full md:w-1/2">
          <LiveChartInfo />
        </div>
        <div className="bg-[#18191D] w-full md:w-1/2 p-4 md:p-6 rounded-2xl flex items-center bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px]">
          <LiveChart />
        </div>
      </div>

      {/* Table section - Modified for mobile */}
      <div className="bg-[#18191D] p-4 md:p-6 rounded-2xl">
        <div className="flex flex-col md:flex-row justify-between items-center py-4 space-y-4 md:space-y-0">
          <h1 className="text-xl md:text-2xl font-medium text-center w-full md:text-start md:w-auto">
            لیست دارایی ها
          </h1>

          <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
            <div className="flex items-center gap-x-2">
              <label htmlFor="" className="text-sm md:text-base">
                عدم نمایش کیف پول بدون موجودی
              </label>
              <input className="accent-[#353945]" checked type="checkbox" />
            </div>

            <div className="bg-[#23262F] flex items-center gap-x-2 p-2 rounded-lg w-full md:w-auto">
              <Image
                className="w-5 h-5"
                src="/images/search.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
              <input
                type="text"
                className="w-full outline-none bg-transparent"
                placeholder="جست و جو"
              />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full text-center min-w-[800px]">
            <thead className="text-xs text-[#B1B5C3] uppercase bg-[#23262F]">
              <tr>
                <th scope="col" className="px-4 md:px-6 py-3">
                  ریال
                </th>
                <th scope="col" className="px-4 md:px-6 py-3">
                  محموع موجودی
                </th>
                <th scope="col" className="px-4 md:px-6 py-3">
                  موجود
                </th>
                <th scope="col" className="px-4 md:px-6 py-3">
                  فریز شده
                </th>
                <th scope="col" className="px-4 md:px-6 py-3">
                  معادل تتری
                </th>
                <th scope="col" className="px-4 md:px-6 py-3">
                  عملیات
                </th>
              </tr>
            </thead>
            <tbody className="text-[#FCFCFD]">
              {[1, 2, 3, 4, 5].map((_, index) => (
                <tr key={index} className="odd:bg-[#18191D] even:bg-[#1C1E24]">
                  <td className="py-4 text-sm md:text-base">
                    <Image
                      className="w-10 h-10 mr-10"
                      src="/images/btc-logo.png"
                      height={1000}
                      width={1000}
                      alt="avatar"
                    />
                  </td>
                  <td className="py-4 text-sm md:text-base">25.000.000</td>
                  <td className="py-4">
                    <div>
                      <p className="text-base md:text-lg font-medium">
                        5,759.24
                      </p>
                      <p
                        className="text-xs md:text-sm text-[#B1B5C3] font-light"
                        dir="ltr"
                      >
                        = 1950.08 USDT
                      </p>
                    </div>
                  </td>
                  <td className="py-4 text-sm md:text-base">0.00</td>
                  <td className="py-4">
                    <div className="flex justify-center items-center gap-x-1.5">
                      <span className="text-sm md:text-base">1950.08</span>
                    </div>
                  </td>
                  <th
                    scope="row"
                    className="py-4 font-medium whitespace-nowrap"
                  >
                    <button className="border border-[#353945] rounded-lg px-2 md:px-8 py-1 md:py-2 text-sm md:text-base cursor-pointer mx-2">
                      جزییات
                    </button>
                    <button className="border border-[#353945] rounded-lg px-2 md:px-8 py-1 md:py-2 text-sm md:text-base cursor-pointer ml-3 bg-[#23262F]">
                      واریز
                    </button>
                  </th>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Page;
