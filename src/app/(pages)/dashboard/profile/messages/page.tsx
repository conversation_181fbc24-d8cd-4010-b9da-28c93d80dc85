"use client";

import React, { useEffect, useState } from "react";
import { getProfile, readNotif } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import jalaali from "jalaali-js";
import { motion, AnimatePresence } from "framer-motion";
import { FaBell, FaCheckCircle, FaInbox, FaSearch } from "react-icons/fa";
import Image from "next/image";
import Link from "next/link";

interface Message {
  id: number;
  message: string;
  created_at: string;
  read: number;
}

interface IUserProfile {
  firstname?: string;
  lastname?: string;
  alerts?: Message[];
}

const Page: React.FC = () => {
  const [info, setInfo] = useState<IUserProfile>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    getProfileHandler();
  }, []);

  async function getProfileHandler() {
    setLoading(true);
    try {
      const result = await getProfile();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setInfo(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات");
    } finally {
      setLoading(false);
    }
  }

  const readNotifHandler = async (id: number) => {
    try {
      const result = await readNotif(id);
      if (result.status === "success") {
        toast.success("پیام با موفقیت خوانده شد");
        getProfileHandler();
      } else {
        toast.error("خطا در خواندن پیام");
      }
    } catch (error) {
      toast.error("خطا در خواندن پیام");
    }
  };

  const convertToJalali = (date: string): string => {
    const d = new Date(date);
    const jDate = jalaali.toJalaali(d);
    const hours = d.getHours().toString().padStart(2, "0");
    const minutes = d.getMinutes().toString().padStart(2, "0");
    return `${jDate.jy}/${jDate.jm}/${jDate.jd} - ${hours}:${minutes}`;
  };

  const toPersianNumber = (num: string): string => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num
      .toString()
      .replace(/[0-9]/g, (match) => persianDigits[parseInt(match)]);
  };

  const filteredMessages = info.alerts?.filter(message =>
    message.message.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const unreadCount = info.alerts?.filter(message => message.read === 0).length || 0;

  return (
    <div className="p-4 md:p-6">
      <div className="bg-[#18191D] rounded-2xl p-4 md:p-6 shadow-lg">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-blue-600 to-blue-500 p-2 rounded-lg shadow-[0_0_15px_rgba(59,130,246,0.3)]">
              <FaInbox className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl md:text-2xl font-bold text-white">پیام‌های من</h1>
            {unreadCount > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="bg-gradient-to-r from-blue-500 to-blue-400 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1"
              >
                <span>{toPersianNumber(unreadCount.toString())}</span>
                <span>پیام خوانده نشده</span>
              </motion.div>
            )}
          </div>

          {/* Search Box */}
          <div className="relative w-full md:w-auto">
            <input
              type="text"
              placeholder="جستجو در پیام‌ها..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full md:w-64 bg-[#23262F] border border-gray-700 rounded-lg py-2 px-4 pr-10 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {/* Messages List */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="bg-[#1C1E24] rounded-xl border border-gray-800">
            {filteredMessages.length > 0 ? (
              <div className="divide-y divide-gray-800">
                <AnimatePresence>
                  {filteredMessages.map((message, index) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05, duration: 0.3 }}
                      className={`relative p-4 md:p-5 transition-all duration-300 ${
                        message.read === 0
                          ? "hover:bg-gradient-to-r hover:from-blue-600/10 hover:to-blue-500/5"
                          : "hover:bg-[#23262F]/30"
                      }`}
                    >
                      {/* Unread indicator line */}
                      {message.read === 0 && (
                        <motion.div
                          className="absolute right-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-blue-600"
                          layoutId={`indicator-${message.id}`}
                          initial={{ height: 0 }}
                          animate={{ height: '100%' }}
                          transition={{ duration: 0.3, delay: 0.1 }}
                        />
                      )}

                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-xs text-gray-400 bg-[#23262F]/80 px-2 py-1 rounded-full">
                              {convertToJalali(message.created_at)}
                            </span>
                            {message.read === 1 && (
                              <span className="text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded-full flex items-center gap-1">
                                <FaCheckCircle className="w-3 h-3" />
                                <span>خوانده شده</span>
                              </span>
                            )}
                          </div>
                          <p className={`text-sm md:text-base ${message.read === 0 ? 'text-white' : 'text-gray-400'} transition-colors duration-300`}>
                            {message.message}
                          </p>
                        </div>

                        {message.read === 0 && (
                          <motion.button
                            whileHover={{ scale: 1.05, boxShadow: "0 0 15px rgba(59, 130, 246, 0.5)" }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => readNotifHandler(message.id)}
                            type="button"
                            className="text-white bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 rounded-lg text-xs px-3 py-1.5 cursor-pointer shadow-lg transition-all duration-300 flex items-center gap-1 mr-2"
                          >
                            <span>خواندن</span>
                            <motion.div
                              animate={{ x: [0, 5, 0] }}
                              transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
                              className="w-2 h-2 bg-white rounded-full opacity-70"
                            />
                          </motion.button>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="flex flex-col items-center justify-center py-16 text-center"
              >
                <div className="bg-[#23262F] p-4 rounded-full mb-4">
                  <FaBell className="w-8 h-8 text-gray-500" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">پیامی وجود ندارد</h3>
                <p className="text-gray-400 text-sm max-w-md px-4">
                  {searchTerm ?
                    "هیچ پیامی با عبارت جستجو شده یافت نشد." :
                    "پیام‌های مهم و اعلان‌های سیستم در اینجا نمایش داده می‌شوند."}
                </p>
              </motion.div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Page;
