"use client";
import React, { useState } from "react";
import { useRejectedStatus } from "@/hooks/useRejectedStatus";
import RejectedStatusModal from "@/components/dashboard/modals/RejectedStatusModal";
import { motion } from "framer-motion";
import { updateNationalId } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";

const TestRejectedStatusPage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { isRejected } = useRejectedStatus();
  const [nationalId, setNationalId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmit = async () => {
    if (nationalId.length !== 10) {
      toast.error("کد ملی باید ۱۰ رقم باشد");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await updateNationalId(nationalId);
      if (result.isError) {
        toast.error(result.message);
      } else {
        toast.success("کد ملی با موفقیت بروزرسانی شد");
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
    } catch (error) {
      toast.error("خطا در بروزرسانی کد ملی");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 space-y-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-6 rounded-xl border border-gray-800/30 shadow-lg"
      >
        <h1 className="text-2xl font-bold mb-4">تست وضعیت رد شده</h1>
        <p className="mb-6">
          این صفحه برای تست نمایش مودال وضعیت رد شده است. وضعیت فعلی کاربر:{" "}
          <span className={`font-bold ${isRejected ? "text-red-500" : "text-green-500"}`}>
            {isRejected ? "رد شده" : "تایید شده"}
          </span>
        </p>

        <div className="flex flex-col space-y-4">
          <button
            onClick={openModal}
            className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white py-2 px-4 rounded-lg transition-all duration-300"
          >
            نمایش مودال
          </button>

          <div className="mt-8 p-4 bg-[#23262F] rounded-xl">
            <h2 className="text-xl font-bold mb-4">تست مستقیم بروزرسانی کد ملی</h2>
            <div className="flex flex-col space-y-4">
              <input
                type="text"
                value={nationalId}
                onChange={(e) => setNationalId(e.target.value.replace(/\D/g, '').slice(0, 10))}
                placeholder="کد ملی جدید"
                className="w-full p-3 pl-4 border border-gray-700 rounded-xl bg-[#141416]/80 text-white h-[50px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right"
              />
              <button
                onClick={handleSubmit}
                disabled={isSubmitting || nationalId.length !== 10}
                className={`w-full ${
                  isSubmitting || nationalId.length !== 10
                    ? "bg-gray-600 cursor-not-allowed"
                    : "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 cursor-pointer"
                } text-white py-3 px-6 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-blue-500/20 flex items-center justify-center`}
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    در حال ارسال...
                  </>
                ) : (
                  "بروزرسانی کد ملی"
                )}
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Manual Modal Test */}
      <RejectedStatusModal isOpen={isModalOpen} onClose={closeModal} />
    </div>
  );
};

export default TestRejectedStatusPage;
