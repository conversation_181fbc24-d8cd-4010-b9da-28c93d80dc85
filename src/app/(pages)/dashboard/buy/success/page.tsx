"use client";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { getUserCurrency } from "@/requests/dashboardRequest";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  toman_sell_price: number;
  name: string;
  id: number;
}

const BuySuccessPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currency, setCurrency] = useState<CurrencyItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Parse transaction data from URL parameters
  const transaction = searchParams.get("transaction")
    ? JSON.parse(decodeURIComponent(searchParams.get("transaction") || "{}"))
    : null;
  const walletBalance = searchParams.get("wallet_balance") || "0";
  const tomanBalance = searchParams.get("toman_balance") || "0";
  const cryptoAmount = searchParams.get("crypto_amount") || "0";
  const usdAmount = searchParams.get("usd_amount") || "0";
  const currencyId = searchParams.get("currency_id") || "0";

  useEffect(() => {
    // If no transaction data is available, redirect to buy page
    if (!transaction) {
      router.replace("/dashboard/buy");
      return;
    }

    // Fetch currency details only once when component mounts
    const getCurrencyDetails = async () => {
      try {
        setIsLoading(true);
        const result = await getUserCurrency();
        if (!result.isError) {
          const currencyDetails = result.data.find(
            (item: CurrencyItem) => item.id === Number(currencyId)
          );
          setCurrency(currencyDetails || null);
        }
      } catch (error) {
        console.error("Error fetching currency details:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getCurrencyDetails();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Format date and time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return {
      date: `${year}/${month}/${day}`,
      time: `${hours}:${minutes}:${seconds}`,
    };
  };

  const dateTime = transaction?.created_at
    ? formatDateTime(transaction.created_at)
    : { date: "", time: "" };

    const toPersianNumber = (num: string): string => {
      const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
      return num
        .toString()
        .replace(/[0-9]/g, (match) => persianDigits[parseInt(match)]);
    };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-[1200px] mx-auto bg-[#18191D] px-4 py-9 sm:p-5 md:p-6 rounded-2xl relative overflow-hidden"
      style={{
        backgroundImage:
          "radial-gradient(circle at 10% 20%, rgba(35, 38, 47, 0.8) 0%, rgba(24, 25, 29, 0.9) 90%)",
        boxShadow: "0 10px 30px -10px rgba(0, 0, 0, 0.5)",
      }}
    >
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-600/20 via-green-400/40 to-green-600/20" />
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-600/20 via-green-400/40 to-green-600/20" />
      <div className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-green-600/20 via-green-400/40 to-green-600/20" />
      <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-green-600/20 via-green-400/40 to-green-600/20" />

      {/* Background glow effects */}
      <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-green-500/5 blur-3xl" />
      <div className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-blue-500/5 blur-3xl" />

      {/* Success Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex flex-col items-center justify-center mb-8 relative z-10"
      >
        <div className="bg-green-500/20 p-4 rounded-full mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-green-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
          خرید با موفقیت انجام شد
        </h1>
        <p className="text-sm text-gray-400">
          جزئیات تراکنش خرید شما در زیر نمایش داده شده است
        </p>
      </motion.div>

      {/* Transaction Details */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="grid md:grid-cols-2 gap-6 relative z-10"
      >
        {/* Transaction Summary */}
        <motion.div
          whileHover={{ boxShadow: "0 8px 30px rgba(0, 0, 0, 0.12)" }}
          className="bg-gradient-to-br from-[#23262F]/80 to-[#18191D] p-5 sm:p-6 rounded-2xl border border-gray-800/50 backdrop-blur-sm relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-600/0 via-green-400/20 to-green-600/0"></div>
          <h2 className="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            جزئیات تراکنش
          </h2>

          <div className="space-y-4">
            {/* Transaction ID */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">شناسه تراکنش:</span>
              <span className="font-medium text-white">{transaction?.id}</span>
            </div>

            {/* Transaction Type */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">نوع تراکنش:</span>
              <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-lg text-sm">
                خرید
              </span>
            </div>

            {/* Transaction Status */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">وضعیت:</span>
              <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-lg text-sm">
                {transaction?.status === "done" ? "تکمیل شده" : transaction?.status}
              </span>
            </div>

            {/* Transaction Date & Time */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">تاریخ و زمان:</span>
              <div className="text-left">
                <span className="text-white block">{dateTime.time}</span>
                <span className="text-gray-400 text-sm block">{dateTime.date}</span>
              </div>
            </div>

            {/* Transaction Description */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">توضیحات:</span>
              <span className="text-white">{transaction?.description}</span>
            </div>
          </div>
        </motion.div>

        {/* Amount Details */}
        <motion.div
          whileHover={{ boxShadow: "0 8px 30px rgba(0, 0, 0, 0.12)" }}
          className="bg-gradient-to-br from-[#23262F]/80 to-[#18191D] p-5 sm:p-6 rounded-2xl border border-gray-800/50 backdrop-blur-sm relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/20 to-blue-600/0"></div>
          <h2 className="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            جزئیات مبلغ
          </h2>

          <div className="space-y-4">
            {/* Crypto Amount */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">مقدار ارز:</span>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={20}
                    width={20}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <span className="font-medium text-white">
                  {Number(cryptoAmount).toFixed(4)} {currency?.coin_type}
                </span>
              </div>
            </div>

            {/* Toman Amount */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">مبلغ تومان:</span>
              <span className="font-medium text-white">
                {sliceNumber(transaction?.price || "0")} تومان
              </span>
            </div>

            {/* USD Amount */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">معادل دلار:</span>
              <span className="font-medium text-white">${Number(usdAmount).toFixed(2)}</span>
            </div>

            {/* Balance Before */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">موجودی قبلی:</span>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={20}
                    width={20}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <span className="font-medium text-white">
                  {Number(transaction?.balance_before).toFixed(4)} {currency?.coin_type}
                </span>
              </div>
            </div>

            {/* Balance After */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">موجودی فعلی:</span>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={20}
                    width={20}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <span className="font-medium text-white">
                  {Number(transaction?.balance_after).toFixed(4)} {currency?.coin_type}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Current Balances */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6 relative z-10"
      >
        {/* Crypto Balance */}
        <motion.div
          whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.2)" }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
          className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-blue-500/20 shadow-md relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20"></div>
          <div className="absolute -top-10 -right-10 w-24 h-24 bg-blue-500/10 rounded-full blur-2xl"></div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">موجودی ارز</p>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={24}
                    width={24}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <p className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                  {Number(walletBalance).toFixed(4)} {currency?.coin_type}
                </p>
              </div>
            </div>
            <div className="bg-blue-500/20 p-2 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
        </motion.div>

        {/* Toman Balance */}
        <motion.div
          whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.2)" }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
          className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-green-500/20 shadow-md relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-600/20 via-green-400/40 to-green-600/20"></div>
          <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-green-500/10 rounded-full blur-2xl"></div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">موجودی تومانی</p>
              <p className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                {toPersianNumber(sliceNumber(tomanBalance))} تومان
              </p>
            </div>
            <div className="bg-green-500/20 p-2 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-green-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z"
                />
              </svg>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-8"
      >
        <Link href="/dashboard">
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="px-6 py-3 rounded-xl bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-blue-600/20 font-medium text-base relative overflow-hidden w-full sm:w-auto"
          >
            بازگشت به داشبورد
          </motion.button>
        </Link>

        <Link href="/dashboard/buy">
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="px-6 py-3 rounded-xl bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-green-600/20 font-medium text-base relative overflow-hidden w-full sm:w-auto"
          >
            خرید مجدد
          </motion.button>
        </Link>
      </motion.div>
    </motion.div>
  );
};

export default BuySuccessPage;
