"use client";
import {
  buyTrade,
  getProfile,
  getUserCurrency,
  getDailyLimits,
} from "@/requests/dashboardRequest";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import Image from "next/image";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { useRouter, useSearchParams } from "next/navigation";
import BtnLoader from "@/components/form/BtnLoader";
import { motion, AnimatePresence } from "framer-motion";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  toman_sell_price: number;
  balance_toman: number;
  name: string;
  id: number;
}

const Buy = () => {
  const [tomanAmount, setTomanAmount] = useState("");
  const [trxAmount, setTrxAmount] = useState("");
  const [showCardDropdown, setShowCardDropdown] = useState(false);
  const [active, setActive] = useState(true);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("id");
  const [selected, setSelected] = useState(0);
  const [info, setInfo] = useState<{
    tomanBalance?: string;
    trxBalance?: string;
  }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [limitsLoading, setLimitsLoading] = useState(true);
  const [userCurrency, setUserCurrency] = useState<CurrencyItem[]>([]);
  const [limitsData, setLimitsData] = useState<{
    buy: {
      limit: number;
      used: number;
      remaining: number;
      can_buy: boolean;
      next_buy_time: string | null;
    };
    sell: {
      limit: number;
      used: number;
      remaining: number;
      can_sell: boolean;
    };
  } | null>(null);
  const filtered = userCurrency.find((item) => item.id === selected);

  // قیمت هر واحد ارز به تومان در filtered.toman_sell_price ذخیره شده است

  const formatNumber = (number: string) => {
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  async function getProfileHandler() {
    setIsLoading(true);
    try {
      const result = await getProfile();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setInfo(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات کاربر");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    getProfileHandler();
    getCurrencyHandler();
    fetchDailyLimits();
  }, []);

  const fetchDailyLimits = async () => {
    try {
      setLimitsLoading(true);
      const result = await getDailyLimits();
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت محدودیت‌های روزانه");
      } else {
        setLimitsData(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت محدودیت‌های روزانه");
    } finally {
      setLimitsLoading(false);
    }
  };

  useEffect(() => {
    if (id && active) {
      setSelected(Number(id));
    }
  }, [filtered, active]);

  const getCurrencyHandler = async () => {
    const result = await getUserCurrency();
    setUserCurrency(result.data);
  };

  // تبدیل مقدار ارز به تومان
  const handleTrxChange = (value: string) => {
    setTrxAmount(value);
    // اگر مقدار ارز وارد شده و ارزی انتخاب شده باشد
    if (value && filtered) {
      // اطمینان از وجود قیمت ارز
      const rate = Number(filtered.toman_sell_price);
      if (rate > 0) {
        // محاسبه مقدار تومان با ضرب کردن مقدار ارز در قیمت هر واحد
        const tomanValue = (Number(value) * rate).toFixed(0);
        setTomanAmount(tomanValue);
      } else {
        setTomanAmount("");
      }
    } else {
      setTomanAmount("");
    }
  };

  // تبدیل مقدار تومان به ارز
  const handleTomanChange = (value: string) => {
    setTomanAmount(value);
    // اگر مقدار تومان وارد شده و ارزی انتخاب شده باشد
    if (value && filtered) {
      // اطمینان از وجود قیمت ارز
      const rate = Number(filtered.toman_sell_price);
      if (rate > 0) {
        // محاسبه مقدار ارز با تقسیم مقدار تومان بر قیمت هر واحد
        const trxValue = (Number(value) / rate).toFixed(6);
        setTrxAmount(trxValue);
      } else {
        setTrxAmount("");
      }
    } else {
      setTrxAmount("");
    }
  };

  // بررسی کافی بودن موجودی
  const isBalanceSufficient = () => {
    if (!info.tomanBalance || !tomanAmount) return true;
    return Number(info.tomanBalance) >= Number(tomanAmount);
  };

  // محاسبه مبلغ مورد نیاز برای شارژ
  const getRequiredChargeAmount = () => {
    if (isBalanceSufficient() || !tomanAmount) return 0;
    return Number(tomanAmount) - Number(info.tomanBalance || 0);
  };

  const handleCardSelect = (card: any) => {
    setSelected(card.id);
    setShowCardDropdown(false);
  };

  const buyHandler = async () => {
    setLoading(true);
    if (filtered) {
      try {
        const result = await buyTrade(tomanAmount, filtered?.id);
        if (result.isError) {
          toast.error(result.message);
        } else {
          toast.success(result.message);

          // If we have transaction data, redirect to success page
          if (result.data) {
            const { transaction, wallet_balance, toman_balance, crypto_amount, usd_amount } = result.data;

            // Create URL with transaction data
            const transactionParam = encodeURIComponent(JSON.stringify(transaction));
            const successUrl = `/dashboard/buy/success?transaction=${transactionParam}&wallet_balance=${wallet_balance}&toman_balance=${toman_balance}&crypto_amount=${crypto_amount}&usd_amount=${usd_amount}&currency_id=${filtered.id}`;

            // Redirect to success page
            router.push(successUrl);
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-[1200px] mx-auto bg-[#18191D] px-4 py-9 sm:p-5 md:p-6 rounded-2xl relative overflow-hidden"
      style={{
        backgroundImage: "radial-gradient(circle at 10% 20%, rgba(35, 38, 47, 0.8) 0%, rgba(24, 25, 29, 0.9) 90%)",
        boxShadow: "0 10px 30px -10px rgba(0, 0, 0, 0.5)"
      }}
    >
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-blue-600/20 via-blue-400/40 to-blue-600/20" />
      <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-600/20 via-blue-400/40 to-blue-600/20" />

      {/* Background glow effects */}
      <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-blue-500/5 blur-3xl" />
      <div className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-green-500/5 blur-3xl" />

      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex flex-col sm:flex-row items-center sm:justify-between gap-3 mb-6 relative z-10"
      >
        <div className="w-full text-right sm:text-right">
          <h1 className="text-xl sm:text-2xl font-medium mb-2 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">خرید</h1>
          <p className="text-sm text-gray-400">خرید، فروش و مبدل ارز</p>
        </div>
      </motion.div>

      {/* Wallet Balance - موجودی کیف پول */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-gradient-to-r from-[#23262F] to-[#1C1E24] p-5 rounded-xl mb-6 border border-gray-800 relative overflow-hidden shadow-lg"
      >
        <div className="absolute inset-0 bg-[url('/images/noise.png')] opacity-5"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/30 to-blue-600/0"></div>

        <div className="flex justify-between items-center relative z-10">
          <div className="bg-blue-500/20 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-right">
            <p className="text-gray-400 mb-1 text-sm">موجودی تومانی</p>
            <motion.p
              className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
              initial={false}
              animate={isLoading ? { opacity: [1, 0.5, 1] } : { opacity: 1 }}
              transition={isLoading ? { repeat: Infinity, duration: 1.5 } : {}}
            >
              {isLoading
                ? "در حال بارگذاری..."
                : `${sliceNumber(Number(info.tomanBalance || 0).toFixed(0))} تومان`}
            </motion.p>
          </div>
        </div>
      </motion.div>

      {/* Main Content - Two Columns Layout */}
      <motion.div
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="grid md:grid-cols-2 gap-6 relative z-10"
      >
        {/* Right Column - Currency Exchange */}
        <motion.div
          whileHover={{ boxShadow: "0 8px 30px rgba(0, 0, 0, 0.12)" }}
          className="bg-gradient-to-br from-[#23262F]/80 to-[#18191D] p-5 sm:p-6 rounded-2xl border border-gray-800/50 backdrop-blur-sm relative overflow-hidden"
        >
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/20 to-blue-600/0"></div>
          {/* نمایش ارز و موجودی */}
          <div className="flex-1 relative">
            <motion.div
              whileTap={{ scale: 0.98 }}
              className="border border-gray-600/80 rounded-lg p-3 w-full text-right bg-gradient-to-r from-[#23262F] to-[#1C1E24] text-white cursor-pointer flex items-center justify-between shadow-md hover:shadow-lg transition-shadow duration-300"
              onClick={() => {
                setShowCardDropdown(!showCardDropdown);
                setActive(false);
              }}
            >
              {filtered ? (
                <div className="p-2 px-5 text-center w-full cursor-pointer transition-colors flex justify-between items-center gap-x-2">
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <Image
                        src={`https://api.exchangim.com/storage/${filtered?.coin_icon}`}
                        height={28}
                        width={28}
                        alt="TRX"
                        className="ml-2 rounded-full shadow-md"
                      />
                    </motion.div>
                    <p className="text-white font-medium">{filtered?.coin_type}</p>
                  </div>
                  <p className="text-[18px] bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">هر واحد: {sliceNumber(Number(filtered?.toman_sell_price || 0).toFixed(0))} تومان</p>
                </div>
              ) : (
                <span className="mx-auto text-gray-300">برای انتخاب کلیک کنید</span>
              )}

              <motion.svg
                animate={{ rotate: showCardDropdown ? 180 : 0 }}
                transition={{ duration: 0.3 }}
                className="w-5 h-5 text-blue-400"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path d="M19 9l-7 7-7-7" />
              </motion.svg>
            </motion.div>

            <AnimatePresence>
              {showCardDropdown && (
                <motion.ul
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute w-full mt-2 bg-gradient-to-b from-[#23262F] to-[#1C1E24] border border-gray-700 rounded-xl shadow-2xl z-20 max-h-60 overflow-y-auto text-sm text-white backdrop-blur-sm"
                >
                  {userCurrency?.map((item, index) => (
                    <motion.li
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: index * 0.03 }}
                      whileHover={{
                        backgroundColor: "rgba(59, 130, 246, 0.2)",
                        transition: { duration: 0.1 }
                      }}
                      onClick={() => handleCardSelect(item)}
                      className="p-4 text-center cursor-pointer transition-all flex justify-between items-center gap-x-2 border-b border-gray-700/50 last:border-b-0"
                      key={item.id}
                    >
                      <div className="flex items-center">
                        <Image
                          src={`https://api.exchangim.com/storage/${item.coin_icon}`}
                          height={28}
                          width={28}
                          alt="TRX"
                          className="ml-2 rounded-full"
                        />
                        <div>
                          <p className="text-white font-medium text-right">{item.coin_type}</p>
                          <p className="text-xs text-gray-400 text-right">{item.name}</p>
                        </div>
                      </div>
                      <p className="text-[16px] font-medium">هر واحد: {sliceNumber(Number(item.toman_sell_price || 0).toFixed(0))} تومان</p>
                    </motion.li>
                  ))}
                </motion.ul>
              )}
            </AnimatePresence>
          </div>

          <div className="flex flex-col gap-5 mt-6">
            <div className="relative">
              {filtered && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center border border-gray-600/80 rounded-lg bg-gradient-to-r from-[#23262F] to-[#1C1E24] text-center shadow-md relative overflow-hidden group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute right-3 flex items-center">
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                      <Image
                        src={`https://api.exchangim.com/storage/${filtered?.coin_icon}`}
                        height={24}
                        width={24}
                        alt="TRX"
                        className="mr-2 rounded-full shadow-sm"
                      />
                    </motion.div>
                  </div>
                  <input
                    type="text"
                    inputMode="decimal"
                    className="border-0 rounded-lg p-4 pr-12 w-full bg-transparent text-white placeholder-gray-400 text-center outline-none focus:ring-2 focus:ring-blue-500/30 transition-all duration-300 text-lg z-10"
                    placeholder={`مقدار ${filtered?.coin_type}`}
                    value={trxAmount}
                    onChange={(e) =>
                      handleTrxChange(e.target.value.replace(/[^0-9.]/g, ""))
                    }
                  />
                </motion.div>
              )}
            </div>

            {/* فیلد تومان */}
            <div className="relative">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="flex items-center border border-gray-600/80 rounded-lg bg-gradient-to-r from-[#23262F] to-[#1C1E24] shadow-md relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/0 via-green-500/5 to-green-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute right-3 flex items-center">
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                    <Image
                      src="/images/toman.png"
                      height={24}
                      width={24}
                      alt="تومان"
                      className="mr-2 rounded-full shadow-sm"
                    />
                  </motion.div>
                </div>
                <input
                  type="text"
                  inputMode="numeric"
                  className="border-0 rounded-lg p-4 pr-12 w-full bg-transparent text-white placeholder-gray-400 text-center outline-none focus:ring-2 focus:ring-blue-500/30 transition-all duration-300 text-lg z-10"
                  placeholder="مقدار به تومان"
                  value={formatNumber(tomanAmount)}
                  onChange={(e) => {
                    const onlyDigits = e.target.value.replace(/\D/g, "");
                    handleTomanChange(onlyDigits);
                  }}
                />
                <div className="absolute left-3 text-gray-400 font-medium">تومان</div>
              </motion.div>
            </div>

            {/* نمایش خطای کمبود موجودی */}
            <AnimatePresence>
              {!isBalanceSufficient() && tomanAmount && (
                <motion.div
                  initial={{ opacity: 0, y: -10, height: 0 }}
                  animate={{ opacity: 1, y: 0, height: 'auto' }}
                  exit={{ opacity: 0, y: -10, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="bg-gradient-to-r from-red-900/60 to-red-800/40 text-red-200 p-4 rounded-lg mt-4 border border-red-700/30 shadow-lg overflow-hidden"
                >
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-300 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <p className="font-medium">موجودی شما برای پرداخت کافی نمیباشد.</p>
                  </div>
                  <div className="flex justify-between items-center mt-3 bg-red-900/30 p-3 rounded-lg">
                    <p className="text-red-200">
                      مبلغ مورد نیاز برای شارژ:
                      <span className="font-bold mr-1 text-white">{sliceNumber(getRequiredChargeAmount().toFixed(0))} تومان</span>
                    </p>
                    <motion.button
                      whileHover={{ scale: 1.05, backgroundColor: 'rgba(74, 222, 128, 0.9)' }}
                      whileTap={{ scale: 0.95 }}
                      className="border border-green-600/30 text-white px-4 py-2 text-sm rounded-lg bg-green-600 shadow-md transition-all duration-300 flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      شارژ کیف پول
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="grid grid-cols-2 gap-4 mt-6"
            >
              <div className="bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24] p-3 rounded-lg border border-gray-800/30 shadow-md relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <p className="text-sm text-gray-400 mb-1">کل موجودی شما:</p>
                <motion.p
                  className="text-base font-medium text-white"
                  initial={false}
                  animate={isLoading ? { opacity: [1, 0.5, 1] } : { opacity: 1 }}
                  transition={isLoading ? { repeat: Infinity, duration: 1.5 } : {}}
                >
                  {isLoading
                    ? "در حال بارگذاری..."
                    : <span className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">{`${sliceNumber(
                        Number(info.tomanBalance || 0).toFixed(0)
                      )} تومان`}</span>}
                </motion.p>
              </div>

              <div className="bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24] p-3 rounded-lg border border-gray-800/30 shadow-md relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/0 via-green-500/5 to-green-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <p className="text-sm text-gray-400 mb-1">قیمت ارز:</p>
                <motion.p
                  className="text-base font-medium text-white"
                  initial={false}
                  animate={isLoading ? { opacity: [1, 0.5, 1] } : { opacity: 1 }}
                  transition={isLoading ? { repeat: Infinity, duration: 1.5 } : {}}
                >
                  {isLoading
                    ? "در حال بارگذاری..."
                    : <span className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">{`${sliceNumber(
                        Number(filtered?.toman_sell_price || 0).toFixed(0)
                      )} تومان`}</span>}
                </motion.p>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Left Column - Notices and Limitations */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="flex flex-col gap-5"
        >
          <motion.div
            whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.2)" }}
            transition={{ type: "spring", stiffness: 300, damping: 15 }}
            className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-yellow-500/20 shadow-md relative overflow-hidden"
          >
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-600/20 via-yellow-400/40 to-yellow-600/20"></div>
            <div className="absolute -top-10 -right-10 w-24 h-24 bg-yellow-500/10 rounded-full blur-2xl"></div>

            <div className="flex items-center mb-3">
              <div className="bg-yellow-500/20 p-2 rounded-full mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="text-yellow-400 font-medium text-lg">محدودیت های خرید</p>
            </div>

            {limitsLoading ? (
              <div className="bg-yellow-500/5 p-3 rounded-lg border border-yellow-500/10 flex justify-center">
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="text-gray-400"
                >
                  در حال بارگذاری...
                </motion.div>
              </div>
            ) : limitsData ? (
              <div className="bg-yellow-500/5 p-3 rounded-lg border border-yellow-500/10">
                <p className="text-sm text-gray-300">
                  مجموع خرید روزانه معادل : <span className="text-white font-medium">{sliceNumber(limitsData.buy.used.toFixed(0))} تومان</span> از <span className="text-white font-medium">{sliceNumber(limitsData.buy.limit.toFixed(0))} تومان</span>
                </p>

                <div className="w-full bg-gray-700/50 rounded-full h-2 mt-3 relative overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.min(100, Math.round((limitsData.buy.used / limitsData.buy.limit) * 100))}%` }}
                    transition={{ duration: 1, ease: "easeOut" }}
                    className="bg-gradient-to-r from-yellow-500 to-yellow-300 h-2 rounded-full"
                  />
                  {limitsData.buy.used > 0 && (
                    <motion.div
                      className="absolute top-0 left-0 right-0 h-full w-20 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                      animate={{ x: ['-100%', '400%'] }}
                      transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                    />
                  )}
                </div>
                <div className="flex justify-between mt-1 text-[10px] text-gray-400">
                  <span>باقیمانده: {sliceNumber(limitsData.buy.remaining.toFixed(0))} تومان</span>
                  <span>کل: {sliceNumber(limitsData.buy.limit.toFixed(0))} تومان</span>
                </div>

                <div className="mt-4 flex justify-center">
                  <Link href="/dashboard/authenticate">
                    <motion.button
                      whileHover={{ scale: 1.05, boxShadow: "0 5px 15px rgba(234, 179, 8, 0.3)" }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-500 hover:to-yellow-400 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-md flex items-center gap-2 transition-all duration-300"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                      </svg>
                      ارتقاء سطح کاربری
                    </motion.button>
                  </Link>
                </div>
              </div>
            ) : (
              <div className="bg-yellow-500/5 p-3 rounded-lg border border-yellow-500/10 text-center">
                <p className="text-sm text-gray-400">خطا در دریافت اطلاعات محدودیت‌ها</p>
              </div>
            )}
          </motion.div>

          <motion.div
            whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.2)" }}
            transition={{ type: "spring", stiffness: 300, damping: 15 }}
            className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-green-500/20 shadow-md relative overflow-hidden"
          >
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-600/20 via-green-400/40 to-green-600/20"></div>
            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-green-500/10 rounded-full blur-2xl"></div>

            <div className="flex items-center mb-3">
              <div className="bg-green-500/20 p-2 rounded-full mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-green-400 font-medium text-lg">نکات</p>
            </div>

            <div className="space-y-3">
              <div className="bg-green-500/5 p-3 rounded-lg border border-green-500/10">
                <p className="text-sm text-gray-300">
                  حداقل میزان خرید رمز ارز <span className="text-white font-medium">2 تتر</span> معادل <span className="text-white font-medium">162,696 تومان</span> میباشد.
                </p>
              </div>

              <div className="bg-green-500/5 p-3 rounded-lg border border-green-500/10">
                <p className="text-sm text-gray-300">
                  پیش از ثبت درخواست خرید و فروش از میزان ارز وارد شده و قیمت لحظه ای آن اطمینان حاصل فرمایید.
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Submit Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="text-center mt-8"
      >
        {tomanAmount && isBalanceSufficient() ? (
          <motion.div
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
          >
            <BtnLoader
              label="درخواست خرید"
              pending={loading}
              className={`px-10 py-4 rounded-xl bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-green-600/20 font-medium text-base relative overflow-hidden group`}
              onClick={buyHandler}
            />
            <div className="w-full h-1 bg-gradient-to-r from-green-600/0 via-green-400/50 to-green-600/0 mt-1"></div>
          </motion.div>
        ) : (
          <BtnLoader
            label="درخواست خرید"
            pending={loading}
            className={`px-10 py-4 rounded-xl bg-gray-600 cursor-not-allowed opacity-70 text-base`}
            onClick={undefined}
          />
        )}
      </motion.div>
    </motion.div>
  );
};

export default Buy;
