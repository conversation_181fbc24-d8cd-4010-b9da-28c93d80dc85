"use client";

import React, { useEffect, useState } from "react";
import { getTradingTransactions } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import jalaali from "jalaali-js";
import Link from "next/link";
import { motion } from "framer-motion";
import { FiChevronLeft, FiChevronRight, FiEye } from "react-icons/fi";

// API Transaction interface
interface Transaction {
  id: number;
  type: string;
  amount: string;
  price: number | null;
  status: string;
  created_at: string;
  currency_details: {
    id: number;
    name: string;
    coin_type: string;
    coin_price: string;
  };
  type_description: string;
  buy_details?: {
    toman_amount: string;
    usd_amount: number;
    usd_rate: string;
    crypto_amount: number;
  };
  sell_details?: {
    crypto_amount: string;
    usd_amount: string;
    usd_rate: string;
    toman_amount: number;
  };
}

interface Order {
  status: string;
  statusBg: string;
  amount: string;
  totalPrice: string;
  type: string;
  typeColor: string;
  currency: string;
  currencyName: string;
  price: string;
  time: string;
}

const OrderHistory = () => {
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [transactionType, setTransactionType] = useState('buy'); // Default to 'buy' transactions

  useEffect(() => {
    fetchTransactions();
  }, [currentPage, transactionType]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      // Fetch transactions based on selected type (buy or sell)
      console.log(`Fetching transactions with filter: ${transactionType}`);
      const result = await getTradingTransactions(currentPage, transactionType);
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت اطلاعات سفارشات");
      } else {
        // Log the response to see its structure
        console.log("API Response:", result);

        // Check if data exists and has the expected structure
        if (result.data && Array.isArray(result.data.data)) {
          setTransactions(result.data.data);
        } else if (result.data && Array.isArray(result.data)) {
          // Alternative structure - data might be directly in data property
          setTransactions(result.data);
        } else {
          console.error("Unexpected data structure:", result.data);
          setTransactions([]);
        }

        if (result.pagination) {
          setTotalPages(result.pagination.last_page);
        }
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error("خطا در دریافت اطلاعات سفارشات");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const jDate = jalaali.toJalaali(date);
      return `${jDate.jy}/${jDate.jm}/${jDate.jd} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (error) {
      return dateString;
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'done':
        return { text: 'موفق', bgColor: 'bg-[#103923]' };
      case 'pending':
        return { text: 'در انتظار', bgColor: 'bg-[#3A3A3D]' };
      case 'rejected':
      case 'failed':
        return { text: 'ناموفق', bgColor: 'bg-[#5A1C1E]' };
      default:
        return { text: status, bgColor: 'bg-[#3A3A3D]' };
    }
  };

  const getTypeInfo = (type: string) => {
    switch (type) {
      case 'buy':
        return { text: 'خرید', color: 'text-[#2FA766]' };
      case 'sell':
        return { text: 'فروش', color: 'text-red-500' };
      default:
        return { text: type, color: 'text-gray-500' };
    }
  };

  const transformTransactionToOrder = (transaction: Transaction): Order => {
    const statusInfo = getStatusInfo(transaction.status);
    const typeInfo = getTypeInfo(transaction.type);

    let price = transaction.price ? `${sliceNumber(transaction.price.toString())} تومان` : '-';
    let totalPrice = '-';

    if (transaction.buy_details) {
      totalPrice = `${sliceNumber(transaction.buy_details.toman_amount)} تومان`;
    } else if (transaction.sell_details) {
      totalPrice = `${sliceNumber(transaction.sell_details.toman_amount.toString())} تومان`;
    }

    return {
      status: statusInfo.text,
      statusBg: statusInfo.bgColor,
      amount: transaction.amount,
      totalPrice,
      type: typeInfo.text,
      typeColor: typeInfo.color,
      currency: transaction.currency_details.coin_type,
      currencyName: transaction.currency_details.name,
      price,
      time: formatDate(transaction.created_at),
    };
  };

  const renderOrderItem = (transaction: Transaction) => {
    const item = transformTransactionToOrder(transaction);
    return (
      <tr
        key={transaction.id}
        className="odd:bg-[#18191D]/80 even:bg-[#1C1E24]/80 hover:bg-[#23262F]/80 transition-colors duration-200 border-b border-[#353945]/20"
      >
        <td className="py-4 text-xs md:text-sm">{item.time}</td>
        <td className="py-4 text-xs md:text-sm">{item.price}</td>
        <td className="py-4">
          <div>
            <p className="text-base md:text-lg font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {item.currency}
            </p>
            <p className="text-[10px] md:text-xs text-[#B1B5C3] font-light">
              {item.currencyName}
            </p>
          </div>
        </td>
        <td className={`py-4 ${item.typeColor} text-xs md:text-sm font-medium`}>{item.type}</td>
        <td className="py-4 text-xs md:text-sm">{item.totalPrice}</td>
        <td className="py-4 text-xs md:text-sm">{item.amount}</td>
        <td className="py-4">
          <span
            className={`${item.statusBg} text-[#C5EFD8] p-2 rounded-lg text-xs md:text-sm shadow-sm`}
          >
            {item.status}
          </span>
        </td>
        <td className="py-4">
          <Link
            href={`/dashboard/transaction/${transaction.id}`}
            className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-3 py-1.5 rounded-lg text-xs md:text-sm transition-colors border border-[#353945]/50 hover:border-[#353945]/80 inline-flex items-center justify-center"
          >
            <FiEye className="h-4 w-4 ml-1 text-blue-400" />
            مشاهده
          </Link>
        </td>
      </tr>
    );
  };

  const renderMobileOrderItem = (transaction: Transaction) => {
    const item = transformTransactionToOrder(transaction);
    return (
      <div
        key={transaction.id}
        className="bg-gradient-to-br from-[#1C1E24] to-[#23262F] p-4 rounded-xl flex flex-col gap-y-4 w-full shadow-md border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 relative overflow-hidden"
      >
        {/* Background pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

        <div className="flex justify-between items-center relative z-10">
          <span className="text-[#B1B5C3] text-xs font-medium">تاریخ / زمان:</span>
          <span className="text-xs font-medium">{item.time}</span>
        </div>
        <div className="flex justify-between items-center relative z-10">
          <span className="text-[#B1B5C3] text-xs font-medium">قیمت:</span>
          <span className="text-xs font-medium">{item.price}</span>
        </div>
        <div className="flex justify-between items-center relative z-10">
          <span className="text-[#B1B5C3] text-xs font-medium">ارز:</span>
          <div className="text-right">
            <p className="text-base font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">{item.currency}</p>
            <p className="text-[10px] text-[#B1B5C3] font-light">{item.currencyName}</p>
          </div>
        </div>
        <div className="flex justify-between items-center relative z-10">
          <span className="text-[#B1B5C3] text-xs font-medium">نوع:</span>
          <span className={`${item.typeColor} text-xs font-medium`}>{item.type}</span>
        </div>
        <div className="flex justify-between items-center relative z-10">
          <span className="text-[#B1B5C3] text-xs font-medium">قیمت کل:</span>
          <span className="text-xs font-medium">{item.totalPrice}</span>
        </div>
        <div className="flex justify-between items-center relative z-10">
          <span className="text-[#B1B5C3] text-xs font-medium">مقدار:</span>
          <span className="text-xs font-medium">{item.amount}</span>
        </div>
        <div className="flex justify-between items-center relative z-10">
          <span className="text-[#B1B5C3] text-xs font-medium">وضعیت:</span>
          <span className={`${item.statusBg} text-[#C5EFD8] p-1.5 rounded-lg text-xs font-medium shadow-sm`}>
            {item.status}
          </span>
        </div>
        <div className="flex justify-center mt-3 relative z-10">
          <Link
            href={`/dashboard/transaction/${transaction.id}`}
            className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 px-4 py-2 rounded-lg text-white text-xs font-medium transition-colors shadow-md hover:shadow-blue-500/20 inline-flex items-center justify-center gap-2 w-full"
          >
            <FiEye className="h-4 w-4" />
            مشاهده جزئیات
          </Link>
        </div>
      </div>
    );
  };

  // Handle transaction type change
  const handleTypeChange = (type: string) => {
    setCurrentPage(1); // Reset to first page when changing type
    setTransactionType(type);
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center items-center mt-8 gap-2">
        <button
          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
          className={`p-2 rounded-lg ${
            currentPage === 1
              ? 'bg-[#23262F]/50 text-gray-500 cursor-not-allowed'
              : 'bg-[#23262F] hover:bg-[#2A2D38] text-white'
          }`}
        >
          <FiChevronRight className="h-5 w-5" />
        </button>

        <div className="flex gap-1">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Show pages around current page
            let pageToShow;
            if (totalPages <= 5) {
              pageToShow = i + 1;
            } else if (currentPage <= 3) {
              pageToShow = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageToShow = totalPages - 4 + i;
            } else {
              pageToShow = currentPage - 2 + i;
            }

            return (
              <button
                key={i}
                onClick={() => setCurrentPage(pageToShow)}
                className={`w-8 h-8 flex items-center justify-center rounded-lg text-sm ${
                  currentPage === pageToShow
                    ? 'bg-[#2FA766] text-white'
                    : 'bg-[#23262F] hover:bg-[#2A2D38] text-white'
                }`}
              >
                {pageToShow}
              </button>
            );
          })}
        </div>

        <button
          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
          disabled={currentPage === totalPages}
          className={`p-2 rounded-lg ${
            currentPage === totalPages
              ? 'bg-[#23262F]/50 text-gray-500 cursor-not-allowed'
              : 'bg-[#23262F] hover:bg-[#2A2D38] text-white'
          }`}
        >
          <FiChevronLeft className="h-5 w-5" />
        </button>
      </div>
    );
  };

  const renderSkeletonRow = () => (
    <tr className="odd:bg-[#18191D]/80 even:bg-[#1C1E24]/80 animate-pulse">
      <td className="py-4"><div className="h-4 bg-[#353945]/50 rounded w-20 mx-auto"></div></td>
      <td className="py-4"><div className="h-4 bg-[#353945]/50 rounded w-24 mx-auto"></div></td>
      <td className="py-4">
        <div className="flex flex-col items-center">
          <div className="h-5 bg-[#353945]/50 rounded w-12 mb-1"></div>
          <div className="h-3 bg-[#353945]/50 rounded w-16"></div>
        </div>
      </td>
      <td className="py-4"><div className="h-4 bg-[#353945]/50 rounded w-16 mx-auto"></div></td>
      <td className="py-4"><div className="h-4 bg-[#353945]/50 rounded w-24 mx-auto"></div></td>
      <td className="py-4"><div className="h-4 bg-[#353945]/50 rounded w-10 mx-auto"></div></td>
      <td className="py-4"><div className="h-6 bg-[#353945]/50 rounded w-16 mx-auto"></div></td>
      <td className="py-4"><div className="h-8 bg-[#353945]/50 rounded w-20 mx-auto"></div></td>
    </tr>
  );

  const renderMobileSkeletonItem = () => (
    <div className="bg-gradient-to-br from-[#1C1E24] to-[#23262F] p-4 rounded-xl flex flex-col gap-y-4 w-full shadow-md border border-[#353945]/30 animate-pulse">
      <div className="flex justify-between items-center">
        <span className="text-[#B1B5C3] text-xs">تاریخ / زمان:</span>
        <div className="h-4 bg-[#353945]/50 rounded w-20"></div>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-[#B1B5C3] text-xs">قیمت:</span>
        <div className="h-4 bg-[#353945]/50 rounded w-24"></div>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-[#B1B5C3] text-xs">ارز:</span>
        <div className="flex flex-col items-end">
          <div className="h-5 bg-[#353945]/50 rounded w-12 mb-1"></div>
          <div className="h-3 bg-[#353945]/50 rounded w-16"></div>
        </div>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-[#B1B5C3] text-xs">نوع:</span>
        <div className="h-4 bg-[#353945]/50 rounded w-16"></div>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-[#B1B5C3] text-xs">قیمت کل:</span>
        <div className="h-4 bg-[#353945]/50 rounded w-24"></div>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-[#B1B5C3] text-xs">مقدار:</span>
        <div className="h-4 bg-[#353945]/50 rounded w-10"></div>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-[#B1B5C3] text-xs">وضعیت:</span>
        <div className="h-6 bg-[#353945]/50 rounded w-16"></div>
      </div>
      <div className="flex justify-center mt-3">
        <div className="h-8 bg-[#353945]/50 rounded w-full"></div>
      </div>
    </div>
  );

  return (
    <div className="bg-transparent md:bg-[#18191D] p-6 rounded-2xl">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
        <motion.h1
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent"
        >
          {transactionType === 'buy' ? 'سوابق سفارشات خرید' : 'سوابق سفارشات فروش'}
        </motion.h1>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="flex gap-2"
        >
          <button
            onClick={() => handleTypeChange('buy')}
            className={`px-4 py-2 rounded-lg text-sm transition-colors ${
              transactionType === 'buy'
                ? 'bg-[#2FA766] text-white'
                : 'bg-[#23262F] hover:bg-[#2A2D38] text-white'
            }`}
          >
            <span className="ml-1">💰</span>
            خرید
          </button>
          <button
            onClick={() => handleTypeChange('sell')}
            className={`px-4 py-2 rounded-lg text-sm transition-colors ${
              transactionType === 'sell'
                ? 'bg-red-500 text-white'
                : 'bg-[#23262F] hover:bg-[#2A2D38] text-white'
            }`}
          >
            <span className="ml-1">💸</span>
            فروش
          </button>
        </motion.div>
      </div>

      {/* Desktop View */}
      <div className="hidden md:block landscape:block overflow-x-auto">
        <table className="w-full text-center">
          <thead className="text-xs text-[#B1B5C3] uppercase bg-[#23262F]">
            <tr>
              <th scope="col" className="px-4 md:px-6 py-4 first:rounded-tr-xl">
                تاریخ / زمان
              </th>
              <th scope="col" className="px-4 md:px-6 py-4">
                قیمت
              </th>
              <th scope="col" className="px-4 md:px-6 py-4">
                ارز
              </th>
              <th scope="col" className="px-4 md:px-6 py-4">
                نوع
              </th>
              <th scope="col" className="px-4 md:px-6 py-4">
                قیمت کل
              </th>
              <th scope="col" className="px-4 md:px-6 py-4">
                مقدار
              </th>
              <th scope="col" className="px-4 md:px-6 py-4">
                وضعیت
              </th>
              <th scope="col" className="px-4 md:px-6 py-4 last:rounded-tl-xl">
                عملیات
              </th>
            </tr>
          </thead>
          <tbody className="text-[#FCFCFD]">
            {loading ? (
              // Loading skeleton
              Array(5).fill(0).map((_, i) => <React.Fragment key={i}>{renderSkeletonRow()}</React.Fragment>)
            ) : transactions.length > 0 ? (
              transactions.map(transaction => renderOrderItem(transaction))
            ) : (
              <tr>
                <td colSpan={8} className="py-8 text-center text-gray-400">
                  هیچ سفارشی یافت نشد
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile View */}
      <div className="md:hidden landscape:hidden space-y-4">
        {loading ? (
          // Loading skeleton for mobile
          Array(3).fill(0).map((_, index) => (
            <div key={index} className="flex justify-center w-full">
              {renderMobileSkeletonItem()}
            </div>
          ))
        ) : transactions.length > 0 ? (
          transactions.map(transaction => (
            <div key={transaction.id} className="flex justify-center w-full">
              {renderMobileOrderItem(transaction)}
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-400">
            هیچ سفارشی یافت نشد
          </div>
        )}
      </div>

      {/* Pagination */}
      {!loading && renderPagination()}
    </div>
  );
};

export default OrderHistory;
