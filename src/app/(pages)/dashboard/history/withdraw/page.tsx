"use client";
import AddWithdrawModal from "@/components/dashboard/withdraw/addWithdrawModal";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import { getWithdrawal } from "@/requests/dashboardRequest";
import { useEffect, useState, useMemo } from "react";
import toast from "react-hot-toast";
import jalaali from "jalaali-js";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { FiDownload, FiClock, FiCheck, FiX, FiPlus, FiRefreshCw } from "react-icons/fi";

const Page = () => {
  const [data, setData] = useState<any>([]);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  useEffect(() => {
    getwithdrawalHandler();
  }, []);

  // Calculate total withdrawal amount
  const totalWithdrawalAmount = useMemo(() => {
    if (!data.withdrawals || data.withdrawals.length === 0) return 0;
    return data.withdrawals.reduce((total: number, item: any) => {
      return total + Number(item.amount);
    }, 0);
  }, [data.withdrawals]);

  // Count withdrawals by status
  const statusCounts = useMemo(() => {
    if (!data.withdrawals || data.withdrawals.length === 0)
      return { pending: 0, approved: 0, rejected: 0, total: 0 };

    return data.withdrawals.reduce((counts: any, item: any) => {
      counts[item.status] = (counts[item.status] || 0) + 1;
      counts.total += 1;
      return counts;
    }, { pending: 0, approved: 0, rejected: 0, total: 0 });
  }, [data.withdrawals]);

  const getwithdrawalHandler = async () => {
    setIsLoading(true);
    try {
      const result = await getWithdrawal();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setData(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات");
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    getwithdrawalHandler();
  };

  // Function to convert English numbers to Persian numbers
  const toPersianNumber = (num: string): string => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num
      .toString()
      .replace(/[0-9]/g, (match) => persianDigits[parseInt(match)]);
  };

  const convertToJalali = (dateString: string): string => {
    const date = new Date(dateString);
    const jalaliDate = jalaali.toJalaali(date);
    // Convert to Persian numbers
    return toPersianNumber(
      `${jalaliDate.jy}/${jalaliDate.jm}/${jalaliDate.jd}`
    );
  };

  const renderMobileTicketItem = (item: any, index: number) => (
    <motion.div
      key={index}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="bg-gradient-to-br from-[#1C1E24] to-[#23262F] p-5 rounded-xl w-[85vw] sm:w-[80vw] max-w-[600px] border border-gray-800/30 shadow-lg relative overflow-hidden"
      whileHover={{ scale: 1.02, boxShadow: "0 8px 30px rgba(0,0,0,0.12)" }}
    >
      {/* Decorative top gradient line */}
      <div
        className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
        style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
      />

      <div className="space-y-4">
        {/* Status indicator at top */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-x-2">
            {item.status === "pending" && (
              <span className="flex items-center gap-x-1.5 bg-gradient-to-r from-amber-600 to-amber-500 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
                <FiClock className="animate-pulse" />
                در حال بررسی
              </span>
            )}
            {item.status === "approved" && (
              <span className="flex items-center gap-x-1.5 bg-gradient-to-r from-green-600 to-green-500 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
                <FiCheck />
                تایید شده
              </span>
            )}
            {item.status === "rejected" && (
              <div className="flex flex-col gap-1">
                <span className="flex items-center gap-x-1.5 bg-gradient-to-r from-red-600 to-red-500 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
                  <FiX />
                  رد شده
                </span>
                {item.reject_reason && (
                  <span className="text-xs text-gray-400 line-clamp-2 mr-6">
                    {item.reject_reason}
                  </span>
                )}
              </div>
            )}
          </div>
          <span className="text-gray-400 text-sm">{convertToJalali(item.created_at)}</span>
        </div>

        {/* Amount - highlighted */}
        <div className="bg-[#23262F]/50 p-3 rounded-lg border border-gray-800/30">
          <span className="text-gray-400 text-sm block mb-1">میزان:</span>
          <span className="text-xl font-bold text-white">{sliceNumber(Number(item.amount).toFixed(0))} تومان</span>
        </div>

        {/* Card info with bank icon */}
        <div className="flex justify-between items-center">
          <span className="text-gray-400 text-sm">شماره کارت:</span>
          <div className="flex items-center gap-x-2">
            <Image
              className="rounded-md"
              src={`https://api.exchangim.com/storage/${item.card?.bank?.icon}`}
              height={24}
              width={24}
              alt="card"
            />
            <span className="text-white">{item.card.number}</span>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-400 text-sm">شبا:</span>
          <span className="text-white">{item.card.sheba}</span>
        </div>

        {item.card.tracking_number && (
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm">شماره پیگیری:</span>
            <span className="text-white">{item.card.tracking_number}</span>
          </div>
        )}
      </div>
    </motion.div>
  );

  return (
    <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-6 rounded-2xl border border-gray-800/30 shadow-lg">
      {/* Header Section with Animated Elements */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-8 relative"
      >
        <div className="relative">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-gray-400 text-transparent bg-clip-text">
            سوابق برداشت
          </h1>
          <motion.div
            className="h-1 w-24 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full mt-2"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          />
        </div>

        <div className="flex items-center gap-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefresh}
            disabled={refreshing}
            className="bg-[#23262F] hover:bg-[#2A2D36] p-2.5 rounded-lg text-white transition-colors duration-300 flex items-center justify-center"
          >
            <FiRefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin text-blue-400' : 'text-gray-300'}`} />
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsOpen(true)}
            className="cursor-pointer bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 rounded-lg px-5 py-2.5 text-white inline-flex items-center justify-center gap-2 transition-all duration-300 shadow-lg hover:shadow-blue-500/20"
          >
            <FiPlus className="text-white" />
            درخواست برداشت
          </motion.button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      {!isLoading && data.withdrawals && data.withdrawals.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8"
        >
          {/* Total Withdrawals Card */}
          <div className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl border border-gray-800/30 shadow-md relative overflow-hidden group">
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
              style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
            />

            <div className="flex items-start justify-between">
              <div>
                <p className="text-gray-400 text-sm">مجموع برداشت‌ها</p>
                <p className="text-2xl font-bold mt-2 text-white">{sliceNumber(totalWithdrawalAmount.toFixed(0))} <span className="text-sm font-normal text-gray-400">تومان</span></p>
              </div>
              <div className="bg-blue-500/20 p-2 rounded-lg">
                <FiDownload className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          {/* Status Cards */}
          <div className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl border border-gray-800/30 shadow-md relative overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
              style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
            />

            <p className="text-gray-400 text-sm">تعداد کل</p>
            <p className="text-2xl font-bold mt-2 text-white">{statusCounts.total} <span className="text-sm font-normal text-gray-400">برداشت</span></p>
          </div>

          <div className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl border border-gray-800/30 shadow-md relative overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(39,174,96,0.5)]"
              style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(39,174,96,1) 50%, rgba(211,211,211,0.1) 100%)' }}
            />

            <div className="flex items-start justify-between">
              <div>
                <p className="text-gray-400 text-sm">تایید شده</p>
                <p className="text-2xl font-bold mt-2 text-white">{statusCounts.approved} <span className="text-sm font-normal text-gray-400">برداشت</span></p>
              </div>
              <div className="bg-green-500/20 p-2 rounded-lg">
                <FiCheck className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl border border-gray-800/30 shadow-md relative overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(235,87,87,0.5)]"
              style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(235,87,87,1) 50%, rgba(211,211,211,0.1) 100%)' }}
            />

            <div className="flex items-start justify-between">
              <div>
                <p className="text-gray-400 text-sm">در انتظار</p>
                <p className="text-2xl font-bold mt-2 text-white">{statusCounts.pending} <span className="text-sm font-normal text-gray-400">برداشت</span></p>
              </div>
              <div className="bg-amber-500/20 p-2 rounded-lg">
                <FiClock className="w-6 h-6 text-amber-400" />
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {isLoading ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex flex-col items-center justify-center py-12"
        >
          <div className="w-16 h-16 border-4 border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-gray-400">در حال بارگذاری اطلاعات...</p>
        </motion.div>
      ) : (
        <>
          {/* Desktop View */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="hidden md:block landscape:block overflow-hidden rounded-xl border border-gray-800/30 shadow-lg"
          >
            <table className="w-full text-center">
              <thead className="text-xs text-gray-300 uppercase bg-gradient-to-r from-[#23262F] to-[#1C1E24]">
                <tr>
                  <th scope="col" className="px-6 py-4">
                    تاریخ / زمان
                  </th>
                  <th scope="col" className="px-6 py-4">
                    میزان
                  </th>
                  <th scope="col" className="px-6 py-4">
                    شماره کارت
                  </th>
                  <th scope="col" className="px-6 py-4">
                    شبا
                  </th>
                  <th scope="col" className="px-6 py-4">
                    شماره پیگیری
                  </th>
                  <th scope="col" className="px-6 py-4">
                    وضعیت
                  </th>
                </tr>
              </thead>
              <tbody className="text-[#FCFCFD] divide-y divide-gray-800/30">
                {data.withdrawals?.map((item: any, index: number) => {
                  return (
                    <motion.tr
                      key={item.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="bg-gradient-to-r from-[#1C1E24] to-[#23262F] hover:bg-gradient-to-r hover:from-[#23262F] hover:to-[#1C1E24] transition-colors duration-300"
                    >
                      <td className="py-4 px-6">
                        {convertToJalali(item.created_at)}
                      </td>
                      <td className="py-4 px-6 font-medium">
                        {sliceNumber(Number(item.amount).toFixed(0))} تومان
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center justify-center gap-x-2">
                          <Image
                            className="rounded-md"
                            src={`https://api.exchangim.com/storage/${item.card?.bank?.icon}`}
                            height={28}
                            width={28}
                            alt="card"
                          />
                          <span>{item.card.number}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">{item.card.sheba}</td>
                      <td className="py-4 px-6">{item.card.tracking_number || "-"}</td>
                      <td className="py-4 px-6">
                        {item.status === "approved" && (
                          <span className="inline-flex items-center gap-x-1.5 bg-gradient-to-r from-green-600 to-green-500 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
                            <FiCheck />
                            تایید شده
                          </span>
                        )}
                        {item.status === "pending" && (
                          <span className="inline-flex items-center gap-x-1.5 bg-gradient-to-r from-amber-600 to-amber-500 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
                            <FiClock className="animate-pulse" />
                            در حال بررسی
                          </span>
                        )}
                        {item.status === "rejected" && (
                          <div className="flex flex-col items-center gap-1">
                            <span className="inline-flex items-center gap-x-1.5 bg-gradient-to-r from-red-600 to-red-500 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
                              <FiX />
                              رد شده
                            </span>
                            {item.reject_reason && (
                              <span className="text-xs text-gray-400 max-w-[150px] line-clamp-2" title={item.reject_reason}>
                                {item.reject_reason}
                              </span>
                            )}
                          </div>
                        )}
                      </td>
                    </motion.tr>
                  );
                })}
              </tbody>
            </table>
          </motion.div>

          {/* Mobile View */}
          <div className="md:hidden landscape:hidden space-y-4 flex flex-col items-center">
            {data.withdrawals?.map(renderMobileTicketItem)}
          </div>

          {/* Empty State */}
          {(!data.withdrawals || data.withdrawals.length === 0) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex flex-col items-center justify-center py-12 text-center"
            >
              <div className="bg-[#23262F] p-6 rounded-full mb-4">
                <FiDownload className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-white mb-2">هیچ برداشتی یافت نشد</h3>
              <p className="text-gray-400 max-w-md mb-6">شما هنوز هیچ برداشتی انجام نداده‌اید. برای برداشت وجه از حساب خود، روی دکمه زیر کلیک کنید.</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsOpen(true)}
                className="cursor-pointer bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 rounded-lg px-5 py-2.5 text-white inline-flex items-center justify-center gap-2 transition-all duration-300 shadow-lg hover:shadow-blue-500/20"
              >
                <FiPlus className="text-white" />
                درخواست برداشت
              </motion.button>
            </motion.div>
          )}
        </>
      )}

      <AnimatePresence>
        {isOpen && (
          <AddWithdrawModal
            setIsOpen={setIsOpen}
            cards={data.cards}
            getwithdrawalHandler={getwithdrawalHandler}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Page;
