"use client";

import Image from "next/image";
import React, { useEffect, useState } from "react";
import { getDepositTransactions } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import { FiChevronLeft, FiChevronRight, FiCopy } from "react-icons/fi";

// API Response interfaces based on the provided sample
interface DepositTransaction {
  id: number;
  transaction_id: string;
  address: string;
  from_address: string;
  amount: {
    value: string;
    formatted: string;
    display: string;
  };
  received_amount: {
    value: string;
    formatted: string;
    display: string;
  };
  fees: {
    value: string;
    formatted: string;
    display: string;
  };
  btc_value?: {
    value: string;
    formatted: string;
    display: string;
  };
  usd_value?: {
    value: string;
    formatted: string;
    display: string;
  };
  status: {
    value: number;
    label: string;
    color: string;
  };
  confirmations: {
    current: number;
    required: number;
    percentage: number;
    is_confirmed: boolean;
  };
  coin: {
    id: number;
    name: string;
    coin_type: string;
    icon: string;
    decimal: number;
  };
  network: {
    id: number;
    name: string;
    slug: string;
    chain_id: string;
    block_confirmation: number;
  };
  receiver_wallet: {
    id: number;
    name: string;
    balance: string;
  };
  blockchain_info: {
    block_number: number;
    network_type: string;
    address_type: string;
  };
  admin_info: {
    is_admin_receive: boolean;
    updated_by: any;
    notification_status: string;
  };
  timestamps: {
    created_at: string;
    updated_at: string;
    created_at_persian: string;
    created_at_human: string;
  };
}

interface DepositPagination {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
  has_more_pages: boolean;
}

interface DepositSummary {
  overview: {
    total_transactions: number;
    confirmed_transactions: number;
    pending_transactions: number;
    failed_transactions: number;
    total_amount: number;
    confirmed_amount: number;
    total_fees: number;
    total_usd_value: number;
    average_amount: number;
    max_amount: number;
    min_amount: number;
  };
  status_breakdown: Array<{
    status: number;
    count: number;
    total_amount: number;
    label: string;
  }>;
  coin_breakdown: Array<{
    coin_id: number;
    coin_type: string;
    coin_name: string;
    count: number;
    total_amount: number;
  }>;
}



const Page = () => {
  const [transactions, setTransactions] = useState<DepositTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [summary, setSummary] = useState<DepositSummary | null>(null);

  // Fetch deposit transactions
  const fetchDepositTransactions = async () => {
    setLoading(true);
    try {
      const result = await getDepositTransactions(currentPage);
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت تاریخچه واریز");
      } else {
        setTransactions(result.data.transactions);
        setTotalPages(result.pagination.last_page);
        setSummary(result.summary);
      }
    } catch (error) {
      console.error("Error fetching deposit transactions:", error);
      toast.error("خطا در دریافت تاریخچه واریز");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDepositTransactions();
  }, [currentPage]);

  // Copy transaction ID to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("کپی شد");
  };

  // Get status color based on status value
  const getStatusColor = (status: { value: number; color: string }) => {
    switch (status.color) {
      case 'success':
        return 'text-green-400 bg-green-400/10';
      case 'warning':
        return 'text-yellow-400 bg-yellow-400/10';
      case 'danger':
        return 'text-red-400 bg-red-400/10';
      default:
        return 'text-gray-400 bg-gray-400/10';
    }
  };

  // Truncate transaction hash for display
  const truncateHash = (hash: string, start = 8, end = 8) => {
    if (hash.length <= start + end) return hash;
    return `${hash.slice(0, start)}...${hash.slice(-end)}`;
  };

  // Loading skeleton for desktop
  const renderSkeletonRow = () => (
    <tr className="border-b border-[#353945]/30 animate-pulse">
      <td className="px-4 py-6">
        <div className="space-y-2">
          <div className="h-4 bg-gray-600/30 rounded w-24"></div>
          <div className="h-3 bg-gray-600/20 rounded w-16"></div>
        </div>
      </td>
      <td className="px-4 py-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-600/30 rounded-full"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-600/30 rounded w-16"></div>
            <div className="h-3 bg-gray-600/20 rounded w-20"></div>
            <div className="h-3 bg-gray-600/20 rounded w-24"></div>
          </div>
        </div>
      </td>
      <td className="px-4 py-6">
        <div className="space-y-2">
          <div className="h-4 bg-gray-600/30 rounded w-20"></div>
          <div className="h-3 bg-gray-600/20 rounded w-16"></div>
        </div>
      </td>
      <td className="px-4 py-6">
        <div className="space-y-2">
          <div className="h-4 bg-gray-600/30 rounded w-32"></div>
          <div className="h-3 bg-gray-600/20 rounded w-24"></div>
        </div>
      </td>
      <td className="px-4 py-6">
        <div className="h-6 bg-gray-600/30 rounded-full w-20"></div>
      </td>
      <td className="px-4 py-6">
        <div className="h-8 bg-gray-600/30 rounded w-16"></div>
      </td>
    </tr>
  );

  const renderDepositItem = (transaction: DepositTransaction) => (
    <tr key={transaction.id} className="border-b border-[#353945]/30 hover:bg-[#1C1E24]/50 transition-all duration-200">
      <td className="px-4 py-6 text-right">
        <div className="text-sm text-[#FCFCFD]">{transaction.timestamps.created_at_persian}</div>
        <div className="text-xs text-[#B1B5C3] mt-1">{transaction.timestamps.created_at_human}</div>
      </td>
      <td className="px-4 py-6">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Image
              className="w-10 h-10 rounded-full border-2 border-[#353945]"
              src={`https://api.exchangim.com/storage/${transaction.coin.icon}`}
              height={40}
              width={40}
              alt={transaction.coin.coin_type}
              onError={(e) => {
                e.currentTarget.src = '/images/default-coin.svg';
              }}
            />
          </div>
          <div>
            <div className="text-base font-semibold text-[#FCFCFD]">
              {transaction.coin.coin_type}
            </div>
            <div className="text-sm text-[#B1B5C3]">
              {transaction.coin.name}
            </div>
            <div className="text-xs text-[#B1B5C3]">
              {transaction.network.name}
            </div>
          </div>
        </div>
      </td>
      <td className="px-4 py-6 text-right">
        <div className="text-base font-semibold text-[#FCFCFD]">
          {transaction.amount.display}
        </div>
        {transaction.usd_value && (
          <div className="text-sm text-[#B1B5C3] mt-1">
            {transaction.usd_value.display}
          </div>
        )}
      </td>
      <td className="px-4 py-6">
        <div className="flex items-center gap-2">
          <span className="text-sm text-[#FCFCFD] font-mono">
            {truncateHash(transaction.transaction_id)}
          </span>
          <button
            onClick={() => copyToClipboard(transaction.transaction_id)}
            className="p-1.5 rounded-lg hover:bg-[#353945]/50 transition-colors group"
            title="کپی آدرس تراکنش"
          >
            <FiCopy className="w-4 h-4 text-[#B1B5C3] group-hover:text-[#FCFCFD]" />
          </button>
        </div>
        <div className="text-xs text-[#B1B5C3] mt-1">
          تأیید: {transaction.confirmations.current}/{transaction.confirmations.required}
        </div>
      </td>
      <td className="px-4 py-6">
        <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
          <div className={`w-2 h-2 rounded-full mr-2 ${
            transaction.status.color === 'success' ? 'bg-green-400' :
            transaction.status.color === 'warning' ? 'bg-yellow-400' :
            transaction.status.color === 'danger' ? 'bg-red-400' : 'bg-gray-400'
          }`}></div>
          {transaction.status.label}
        </span>
      </td>
      <td className="px-4 py-6">
        <button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg">
          جزییات
        </button>
      </td>
    </tr>
  );

  // Loading skeleton for mobile
  const renderMobileSkeletonItem = () => (
    <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-5 rounded-xl border border-[#353945]/30 shadow-lg animate-pulse">
      {/* Header skeleton */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gray-600/30 rounded-full"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-600/30 rounded w-16"></div>
            <div className="h-3 bg-gray-600/20 rounded w-20"></div>
          </div>
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-gray-600/30 rounded w-20"></div>
          <div className="h-3 bg-gray-600/20 rounded w-16"></div>
        </div>
      </div>

      {/* Content skeleton */}
      <div className="space-y-4">
        {Array(4).fill(0).map((_, i) => (
          <div key={i} className="flex justify-between items-center">
            <div className="h-3 bg-gray-600/20 rounded w-16"></div>
            <div className="h-3 bg-gray-600/30 rounded w-24"></div>
          </div>
        ))}
      </div>

      {/* Transaction hash skeleton */}
      <div className="bg-[#23262F] rounded-lg p-3 my-4">
        <div className="h-3 bg-gray-600/20 rounded w-20 mb-2"></div>
        <div className="flex items-center justify-between">
          <div className="h-4 bg-gray-600/30 rounded w-32"></div>
          <div className="w-8 h-8 bg-gray-600/20 rounded"></div>
        </div>
      </div>

      {/* Button skeleton */}
      <div className="h-12 bg-gray-600/30 rounded-lg w-full"></div>
    </div>
  );

  const renderMobileDepositItem = (transaction: DepositTransaction) => (
    <div
      key={transaction.id}
      className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-5 rounded-xl border border-[#353945]/30 shadow-lg"
    >
      {/* Header with coin info */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Image
              className="w-12 h-12 rounded-full border-2 border-[#353945]"
              src={`https://api.exchangim.com/storage/${transaction.coin.icon}`}
              height={48}
              width={48}
              alt={transaction.coin.coin_type}
              onError={(e) => {
                e.currentTarget.src = '/images/default-coin.svg';
              }}
            />
          </div>
          <div>
            <div className="text-lg font-bold text-[#FCFCFD]">
              {transaction.coin.coin_type}
            </div>
            <div className="text-sm text-[#B1B5C3]">
              {transaction.coin.name}
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold text-[#FCFCFD]">
            {transaction.amount.display}
          </div>
          {transaction.usd_value && (
            <div className="text-sm text-[#B1B5C3]">
              {transaction.usd_value.display}
            </div>
          )}
        </div>
      </div>

      {/* Status */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-[#B1B5C3] text-sm">وضعیت:</span>
        <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
          <div className={`w-2 h-2 rounded-full mr-2 ${
            transaction.status.color === 'success' ? 'bg-green-400' :
            transaction.status.color === 'warning' ? 'bg-yellow-400' :
            transaction.status.color === 'danger' ? 'bg-red-400' : 'bg-gray-400'
          }`}></div>
          {transaction.status.label}
        </span>
      </div>

      {/* Network */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-[#B1B5C3] text-sm">شبکه:</span>
        <span className="text-sm text-[#FCFCFD]">{transaction.network.name}</span>
      </div>

      {/* Date */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-[#B1B5C3] text-sm">تاریخ:</span>
        <div className="text-right">
          <div className="text-sm text-[#FCFCFD]">{transaction.timestamps.created_at_persian}</div>
          <div className="text-xs text-[#B1B5C3]">{transaction.timestamps.created_at_human}</div>
        </div>
      </div>

      {/* Confirmations */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-[#B1B5C3] text-sm">تأیید:</span>
        <span className="text-sm text-[#FCFCFD]">
          {transaction.confirmations.current}/{transaction.confirmations.required}
        </span>
      </div>

      {/* Transaction Hash */}
      <div className="bg-[#23262F] rounded-lg p-3 mb-4">
        <div className="text-[#B1B5C3] text-xs mb-2">آدرس تراکنش:</div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-[#FCFCFD] font-mono">
            {truncateHash(transaction.transaction_id, 8, 8)}
          </span>
          <button
            onClick={() => copyToClipboard(transaction.transaction_id)}
            className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors group"
            title="کپی آدرس تراکنش"
          >
            <FiCopy className="w-4 h-4 text-[#B1B5C3] group-hover:text-[#FCFCFD]" />
          </button>
        </div>
      </div>

      {/* Action Button */}
      <button className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white py-3 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg">
        مشاهده جزییات
      </button>
    </div>
  );

  // Pagination component
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        {/* Page Info */}
        <div className="text-sm text-[#B1B5C3]">
          صفحه {currentPage} از {totalPages}
        </div>

        {/* Pagination Controls */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className={`p-2.5 rounded-xl border transition-all duration-200 ${
              currentPage === 1
                ? 'bg-[#23262F]/50 border-[#353945]/30 text-gray-500 cursor-not-allowed'
                : 'bg-[#23262F] border-[#353945] hover:bg-[#2A2D38] hover:border-blue-500/50 text-white hover:shadow-lg'
            }`}
          >
            <FiChevronRight className="h-4 w-4" />
          </button>

          <div className="flex gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-4 py-2.5 rounded-xl text-sm font-medium border transition-all duration-200 ${
                    currentPage === pageNum
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 border-blue-500 text-white shadow-lg shadow-blue-500/25'
                      : 'bg-[#23262F] border-[#353945] hover:bg-[#2A2D38] hover:border-blue-500/50 text-white hover:shadow-lg'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className={`p-2.5 rounded-xl border transition-all duration-200 ${
              currentPage === totalPages
                ? 'bg-[#23262F]/50 border-[#353945]/30 text-gray-500 cursor-not-allowed'
                : 'bg-[#23262F] border-[#353945] hover:bg-[#2A2D38] hover:border-blue-500/50 text-white hover:shadow-lg'
            }`}
          >
            <FiChevronLeft className="h-4 w-4" />
          </button>
        </div>

        {/* Quick Jump */}
        <div className="hidden sm:flex items-center gap-2 text-sm">
          <span className="text-[#B1B5C3]">رفتن به صفحه:</span>
          <input
            type="number"
            min="1"
            max={totalPages}
            value={currentPage}
            onChange={(e) => {
              const page = parseInt(e.target.value);
              if (page >= 1 && page <= totalPages) {
                setCurrentPage(page);
              }
            }}
            className="w-16 px-2 py-1 bg-[#23262F] border border-[#353945] rounded-lg text-white text-center focus:outline-none focus:border-blue-500"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-transparent">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-[#1C1E24] to-[#18191D] p-6 rounded-2xl mb-6 border border-[#353945]/30">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-[#FCFCFD] mb-2">سوابق واریز</h1>
            <p className="text-[#B1B5C3]">مشاهده تاریخچه تراکنش‌های واریز شما</p>
          </div>
          {summary && (
            <div className="bg-[#23262F] rounded-xl p-4 border border-[#353945]/30">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-400">{summary.overview.total_transactions}</div>
                  <div className="text-xs text-[#B1B5C3]">کل تراکنش</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-400">{summary.overview.confirmed_transactions}</div>
                  <div className="text-xs text-[#B1B5C3]">تأیید شده</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-400">{summary.overview.pending_transactions}</div>
                  <div className="text-xs text-[#B1B5C3]">در انتظار</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-400">{summary.overview.failed_transactions}</div>
                  <div className="text-xs text-[#B1B5C3]">ناموفق</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden md:block landscape:block">
        <div className="bg-[#1C1E24] rounded-2xl border border-[#353945]/30 overflow-hidden">
          <table className="w-full">
            <thead className="bg-gradient-to-r from-[#23262F] to-[#1C1E24] border-b border-[#353945]/30">
              <tr>
                <th scope="col" className="px-4 py-4 text-right text-sm font-semibold text-[#FCFCFD]">
                  تاریخ / زمان
                </th>
                <th scope="col" className="px-4 py-4 text-right text-sm font-semibold text-[#FCFCFD]">
                  ارز دیجیتال
                </th>
                <th scope="col" className="px-4 py-4 text-right text-sm font-semibold text-[#FCFCFD]">
                  مقدار
                </th>
                <th scope="col" className="px-4 py-4 text-right text-sm font-semibold text-[#FCFCFD]">
                  آدرس تراکنش
                </th>
                <th scope="col" className="px-4 py-4 text-center text-sm font-semibold text-[#FCFCFD]">
                  وضعیت
                </th>
                <th scope="col" className="px-4 py-4 text-center text-sm font-semibold text-[#FCFCFD]">
                  عملیات
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-[#353945]/20">
              {loading ? (
                // Loading skeleton
                Array(5).fill(0).map((_, i) => <React.Fragment key={i}>{renderSkeletonRow()}</React.Fragment>)
              ) : transactions.length > 0 ? (
                transactions.map(transaction => renderDepositItem(transaction))
              ) : (
                <tr>
                  <td colSpan={6} className="py-16 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <div className="w-16 h-16 bg-[#23262F] rounded-full flex items-center justify-center mb-4">
                        <svg className="w-8 h-8 text-[#B1B5C3]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-[#FCFCFD] mb-2">هیچ تراکنش واریزی یافت نشد</h3>
                      <p className="text-[#B1B5C3]">تراکنش‌های واریز شما در اینجا نمایش داده خواهد شد</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile View */}
      <div className="md:hidden landscape:hidden space-y-4">
        {loading ? (
          // Loading skeleton for mobile
          Array(3).fill(0).map((_, index) => (
            <div key={index} className="w-full">
              {renderMobileSkeletonItem()}
            </div>
          ))
        ) : transactions.length > 0 ? (
          transactions.map(transaction => (
            <div key={transaction.id} className="w-full">
              {renderMobileDepositItem(transaction)}
            </div>
          ))
        ) : (
          <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-8 rounded-xl border border-[#353945]/30 text-center">
            <div className="w-16 h-16 bg-[#23262F] rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-[#B1B5C3]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-[#FCFCFD] mb-2">هیچ تراکنش واریزی یافت نشد</h3>
            <p className="text-[#B1B5C3]">تراکنش‌های واریز شما در اینجا نمایش داده خواهد شد</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!loading && totalPages > 1 && (
        <div className="mt-8 bg-[#1C1E24] rounded-xl border border-[#353945]/30 p-4">
          {renderPagination()}
        </div>
      )}
    </div>
  );
};

export default Page;
