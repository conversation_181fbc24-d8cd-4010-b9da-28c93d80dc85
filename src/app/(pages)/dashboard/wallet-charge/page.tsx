"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { getCards, chargeWallet } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import BtnLoader from "@/components/form/BtnLoader";
import AddCardModal from "@/components/dashboard/card-managment/AddCardModal";

// Types
type Bank = {
  id: number;
  name: string;
  icon: string;
  prefix: string;
};

type Card = {
  id: number;
  user_id: number;
  number: string;
  sheba: string | null;
  iban: string | null;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  bank: Bank;
};

const WalletChargePage = () => {
  // State
  const [activeTab, setActiveTab] = useState<"direct" | "id">("direct");
  const [amount, setAmount] = useState<string>("");
  const [cards, setCards] = useState<Card[]>([]);
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showCardDropdown, setShowCardDropdown] = useState<boolean>(false);
  const [showAddCardModal, setShowAddCardModal] = useState<boolean>(false);

  // Fetch user cards on component mount
  useEffect(() => {
    fetchUserCards();
  }, []);

  // Fetch user cards from API
  const fetchUserCards = async () => {
    setIsLoading(true);
    try {
      const result = await getCards();
      if (result.isError) {
        toast.error("خطا در دریافت اطلاعات کارت‌ها");
      } else {
        setCards(result.data);
        if (result.data.length > 0) {
          // Find the first approved card
          const approvedCard = result.data.find((card: Card) => card.status === "approved");
          if (approvedCard) {
            setSelectedCard(approvedCard.id);
          } else {
            setSelectedCard(null);
          }
        } else {
          // Show modal if no cards are found
          setShowAddCardModal(true);
        }
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات کارت‌ها");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle card modal success
  const handleCardAddSuccess = () => {
    fetchUserCards();
  };

  // Handle amount input change
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    setAmount(value);
  };

  // Handle card selection
  const handleCardSelect = (cardId: number) => {
    const card = cards.find(card => card.id === cardId);
    // Only allow selecting approved cards
    if (card && card.status === "approved") {
      setSelectedCard(cardId);
      setShowCardDropdown(false);
    } else {
      toast.error("فقط کارت‌های تایید شده قابل استفاده هستند");
    }
  };

  // Handle direct payment submission
  const handleDirectPayment = async () => {
    if (!amount) {
      toast.error("لطفا مبلغ را وارد کنید");
      return;
    }

    if (!selectedCard) {
      toast.error("لطفا کارت بانکی را انتخاب کنید");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await chargeWallet(amount, selectedCard);
      if (result.isError) {
        toast.error(result.message || "خطا در ثبت درخواست شارژ کیف پول");
      } else {
        toast.success(result.message || "درخواست شارژ کیف پول با موفقیت ثبت شد");
        setAmount("");
      }
    } catch (error) {
      toast.error("خطا در ثبت درخواست شارژ کیف پول");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get selected card details
  const getSelectedCard = () => {
    return cards.find(card => card.id === selectedCard);
  };

  // Format card number with spaces
  const formatCardNumber = (number: string) => {
    return number.replace(/(\d{4})/g, "$1 ").trim();
  };

  return (
    <div className="bg-transparent md:bg-[#18191D] p-6 rounded-2xl relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-500/5 rounded-full blur-3xl -z-10"></div>

      {/* Add Card Modal */}
      <AnimatePresence>
        {showAddCardModal && (
          <AddCardModal
            isOpen={showAddCardModal}
            onClose={() => setShowAddCardModal(false)}
            onSuccess={handleCardAddSuccess}
          />
        )}
      </AnimatePresence>

      {/* Header with Animation */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center mb-8"
      >
        <div className="relative">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">
            شارژ کیف پول
          </h1>
          <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0"></div>
        </div>
        <div className="mr-auto flex items-center">
          <motion.div
            animate={{
              scale: [1, 1.1, 1],
              rotate: [0, 5, 0, -5, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 3
            }}
            className="bg-gradient-to-br from-blue-500 to-purple-600 p-2 rounded-lg shadow-lg"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </motion.div>
        </div>
      </motion.div>

      {/* Stylish Tabs */}
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-[#23262F]/50 rounded-xl -z-10"></div>
        <div className="flex p-1 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm">
          <motion.div
            className="absolute top-1 bottom-1 rounded-lg bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm z-0 transition-all duration-300"
            initial={false}
            animate={{
              left: activeTab === "direct" ? '0%' : '50%',
              right: activeTab === "direct" ? '50%' : '0%',
            }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          />

          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setActiveTab("direct")}
            className={`relative z-10 w-1/2 py-3 flex items-center justify-center gap-x-2 cursor-pointer rounded-lg ${
              activeTab === "direct" ? "text-white" : "text-gray-400"
            }`}
          >
            <span className="font-medium">درگاه مستقیم</span>
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 ${activeTab === "direct" ? "text-blue-400" : "text-gray-500"}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setActiveTab("id")}
            className={`relative z-10 w-1/2 py-3 flex items-center justify-center gap-x-2 cursor-pointer rounded-lg ${
              activeTab === "id" ? "text-white" : "text-gray-400"
            }`}
          >
            <span className="font-medium">واریز با شناسه</span>
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 ${activeTab === "id" ? "text-purple-400" : "text-gray-500"}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </motion.div>
        </div>
      </div>

      {/* Direct Payment Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === "direct" && (
          <motion.div
            key="direct-payment"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5, type: "spring", stiffness: 100 }}
            className="space-y-6"
          >
            {/* Amount Input with Animation - Improved Layout */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-gradient-to-br from-[#23262F] to-[#1E2126] p-5 rounded-xl shadow-lg border border-[#353945]/30 relative overflow-hidden group"
            >
              <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/40 to-blue-500/0 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-700"></div>

              <div className="flex flex-col md:flex-row md:items-center md:gap-6">
                <div className="md:w-1/2">
                  <label className="block text-gray-300 mb-2 font-medium">مبلغ (تومان)</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={amount ? sliceNumber(amount) : ""}
                      onChange={handleAmountChange}
                      placeholder="مبلغ مورد نظر را وارد کنید"
                      className="w-full bg-[#1A1D21] p-3 pr-10 rounded-lg text-white border border-[#353945] focus:border-blue-500 outline-none transition-all duration-300 shadow-inner text-right"
                    />
                    <div className="absolute top-1/2 right-3 transform -translate-y-1/2 bg-[#353945] p-1 rounded-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="md:w-1/2 mt-3 md:mt-0">
                  <h4 className="text-sm font-medium text-gray-300 mb-2">راهنمای پرداخت</h4>
                  <ul className="text-xs text-gray-400 space-y-1.5">
                    <li className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-blue-400 mt-0.5 ml-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>حداقل مبلغ قابل پرداخت ۱۰,۰۰۰ تومان است</span>
                    </li>
                    <li className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-blue-400 mt-0.5 ml-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>پس از تایید، به درگاه پرداخت منتقل خواهید شد</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Card Selection with Animation - Improved Layout */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gradient-to-br from-[#23262F] to-[#1E2126] p-5 rounded-xl shadow-lg border border-[#353945]/30 relative overflow-hidden group"
            >
              <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500/0 via-purple-500/40 to-purple-500/0 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-700"></div>

              <div className="flex flex-col md:flex-row md:items-start md:gap-6">
                <div className="md:w-1/2">
                  <label className="block text-gray-300 mb-2 font-medium">انتخاب کارت بانکی</label>

                  {isLoading ? (
                    <div className="bg-[#1A1D21] p-3 rounded-lg border border-[#353945] flex items-center justify-center h-12">
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : cards.length === 0 ? (
                    <div className="bg-[#1A1D21] p-4 rounded-lg border border-[#353945] text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto mb-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                      </svg>
                      <p className="text-gray-300 mb-3 text-sm">کارت بانکی یافت نشد.</p>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowAddCardModal(true)}
                        className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 px-4 py-2 rounded-lg text-white inline-flex items-center justify-center gap-1.5 transition-all duration-300 shadow-lg hover:shadow-blue-500/20 text-sm"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        افزودن کارت جدید
                      </motion.button>
                    </div>
                  ) : (
                    <div className="relative">
                      <div
                        onClick={() => setShowCardDropdown(!showCardDropdown)}
                        className="bg-[#1A1D21] p-3 rounded-lg border border-[#353945] flex items-center justify-between cursor-pointer hover:border-purple-500/30 transition-all duration-300 shadow-inner"
                      >
                        {getSelectedCard() ? (
                          <div className="flex items-center w-full">
                            {getSelectedCard()?.bank?.icon && (
                              <div className="w-8 h-8 rounded-full overflow-hidden ml-2 flex-shrink-0 bg-white p-1 shadow-md">
                                <Image
                                  src={`https://api.exchangim.com/storage/${getSelectedCard()?.bank?.icon}`}
                                  width={32}
                                  height={32}
                                  alt={getSelectedCard()?.bank?.name || "bank icon"}
                                  className="w-full h-full object-contain"
                                />
                              </div>
                            )}
                            <div className="flex-1">
                              <div className="flex justify-between items-center">
                                <p className="text-white text-sm font-medium">{getSelectedCard()?.bank?.name}</p>
                                {getSelectedCard()?.status === "approved" && (
                                  <div className="flex items-center text-green-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-green-500 ml-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span className="text-xs">تایید شده</span>
                                  </div>
                                )}
                              </div>
                              <p className="text-gray-400 text-xs mt-0.5 text-right">{formatCardNumber(getSelectedCard()?.number || "")}</p>
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">انتخاب کارت</span>
                        )}
                        <motion.div
                          animate={{ rotate: showCardDropdown ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                          className="bg-[#353945] p-1 rounded-lg"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </motion.div>
                      </div>

                      {/* Dropdown with Animation - Fixed positioning */}
                      <AnimatePresence>
                        {showCardDropdown && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.2 }}
                            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
                            onClick={() => setShowCardDropdown(false)}
                          >
                            <motion.div
                              initial={{ scale: 0.9 }}
                              animate={{ scale: 1 }}
                              exit={{ scale: 0.9 }}
                              onClick={(e) => e.stopPropagation()}
                              className="bg-[#1A1D21] rounded-xl border border-[#353945] shadow-2xl w-[90%] max-w-md max-h-[80vh] overflow-y-auto m-4"
                            >
                              <div className="sticky top-0 bg-[#23262F] p-3 border-b border-[#353945] flex justify-between items-center">
                                <h3 className="text-white font-medium">انتخاب کارت بانکی</h3>
                                <button
                                  onClick={() => setShowCardDropdown(false)}
                                  className="bg-[#353945] p-1.5 rounded-lg hover:bg-[#4A4F5E] transition-colors"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </div>

                              <div className="p-2">
                                {cards.map((card) => {
                                  // Define status info based on card status
                                  const statusInfo = {
                                    approved: {
                                      text: "تایید شده",
                                      color: "text-green-500",
                                      icon: (
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-500 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                      )
                                    },
                                    pending: {
                                      text: "در انتظار تایید",
                                      color: "text-yellow-500",
                                      icon: (
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                      )
                                    },
                                    rejected: {
                                      text: "رد شده",
                                      color: "text-red-500",
                                      icon: (
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-500 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                      )
                                    }
                                  };

                                  const status = statusInfo[card.status];
                                  const isSelectable = card.status === "approved";

                                  return (
                                    <motion.div
                                      key={card.id}
                                      whileHover={isSelectable ? { backgroundColor: "rgba(53, 57, 69, 0.5)" } : {}}
                                      onClick={() => isSelectable && handleCardSelect(card.id)}
                                      className={`p-4 flex items-center transition-colors duration-200 rounded-lg mb-2 ${
                                        selectedCard === card.id ? 'bg-[#353945]/30 border-r-4 border-purple-500' : isSelectable ? 'hover:bg-[#23262F] cursor-pointer' : 'opacity-70 bg-[#1A1D21]/50'
                                      }`}
                                    >
                                      {card.bank?.icon && (
                                        <div className="w-12 h-12 rounded-full overflow-hidden ml-4 flex-shrink-0 bg-white p-1 shadow-md">
                                          <Image
                                            src={`https://api.exchangim.com/storage/${card.bank.icon}`}
                                            width={48}
                                            height={48}
                                            alt={card.bank.name}
                                            className="w-full h-full object-contain"
                                          />
                                        </div>
                                      )}
                                      <div className="flex-1">
                                        <div className="flex justify-between items-center">
                                          <p className="text-white text-sm font-medium">{card.bank.name}</p>
                                          <div className={`flex items-center ${status.color}`}>
                                            {status.icon}
                                            <span className="text-xs">{status.text}</span>
                                          </div>
                                        </div>
                                        <p className="text-gray-400 text-xs mt-1 text-right">{formatCardNumber(card.number)}</p>
                                      </div>
                                    </motion.div>
                                  );
                                })}
                              </div>
                            </motion.div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  )}
                </div>

                <div className="md:w-1/2 mt-4 md:mt-0">
                  <h4 className="text-sm font-medium text-gray-300 mb-2">قوانین پرداخت</h4>
                  <div className="bg-[#1A1D21] p-3 rounded-lg border border-[#353945]">
                    <ul className="text-xs text-gray-400 space-y-2">
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-red-400 mt-0.5 ml-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <span>پرداخت باید با کارت بانکی انتخاب شده انجام شود، در غیر اینصورت مبلغ به حساب شما برگشت خواهد خورد.</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-yellow-400 mt-0.5 ml-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>فقط کارت‌های تایید شده قابل استفاده برای شارژ کیف پول هستند.</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-blue-400 mt-0.5 ml-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>پس از تکمیل پرداخت، مبلغ به صورت خودکار به کیف پول شما اضافه می‌شود.</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Submit Button with Enhanced Animation - Improved Layout */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-gradient-to-br from-[#23262F] to-[#1E2126] p-5 rounded-xl shadow-lg border border-[#353945]/30 relative overflow-hidden group mt-4"
            >
              <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500/0 via-green-500/40 to-green-500/0 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-700"></div>

              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div className="md:w-1/2 mb-4 md:mb-0">
                  <h4 className="text-sm font-medium text-gray-300 mb-2">تایید و پرداخت</h4>
                  <p className="text-xs text-gray-400">
                    با کلیک بر روی دکمه پرداخت، به درگاه بانکی منتقل خواهید شد. لطفا از کارت بانکی انتخاب شده برای پرداخت استفاده کنید.
                  </p>
                </div>

                <div className="md:w-auto">
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    className="relative inline-block"
                  >
                    {/* Button Shadow Effect */}
                    <div className="absolute -inset-1 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl blur-lg opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"></div>

                    <BtnLoader
                      label="پرداخت و شارژ کیف پول"
                      pending={isSubmitting}
                      className="px-6 py-3 rounded-xl bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-500 hover:to-blue-500 transition-all duration-300 cursor-pointer shadow-lg font-medium text-sm relative overflow-hidden z-10"
                      onClick={handleDirectPayment}
                    />

                    {/* Animated Underline */}
                    <motion.div
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ duration: 0.8, delay: 0.5 }}
                      className="w-full h-0.5 bg-gradient-to-r from-green-600/0 via-blue-400/50 to-green-600/0 mt-1"
                    />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Deposit with ID Tab Content */}
      {activeTab === "id" && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {/* Account Information */}
          <div className="bg-[#23262F] p-4 rounded-xl">
            <h3 className="text-lg font-medium mb-4">اطلاعات حساب</h3>

            <div className="space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-[#1A1D21] rounded-lg border border-[#353945]">
                <span className="text-gray-400 mb-1 sm:mb-0">شبا:</span>
                <div className="flex items-center">
                  <span className="text-white font-medium">**************************</span>
                  <button className="mr-2 p-1.5 bg-[#353945] rounded-lg hover:bg-[#4A4F5E] transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-[#1A1D21] rounded-lg border border-[#353945]">
                <span className="text-gray-400 mb-1 sm:mb-0">شماره حساب:</span>
                <div className="flex items-center">
                  <span className="text-white font-medium">30102539604608</span>
                  <button className="mr-2 p-1.5 bg-[#353945] rounded-lg hover:bg-[#4A4F5E] transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-[#1A1D21] rounded-lg border border-[#353945]">
                <span className="text-gray-400 mb-1 sm:mb-0">به نام:</span>
                <span className="text-white font-medium">آرمین افق داده گستر</span>
              </div>
            </div>
          </div>

          {/* Allowed Banks */}
          <div className="bg-[#23262F] p-4 rounded-xl">
            <h3 className="text-lg font-medium mb-4">مبداهای مجاز برای واریز با شناسه</h3>

            {isLoading ? (
              <div className="flex justify-center p-4">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                {cards.map((card) => {
                  // Define status colors
                  const statusColors = {
                    approved: "border-green-500/50",
                    pending: "border-yellow-500/50",
                    rejected: "border-red-500/50"
                  };

                  return (
                    <div
                      key={card.id}
                      className={`bg-[#1A1D21] p-3 rounded-lg border ${statusColors[card.status]} flex flex-col items-center ${card.status !== "approved" ? "opacity-70" : ""}`}
                    >
                      {card.bank?.icon && (
                        <div className="w-12 h-12 rounded-full overflow-hidden mb-2">
                          <Image
                            src={`https://api.exchangim.com/storage/${card.bank.icon}`}
                            width={48}
                            height={48}
                            alt={card.bank.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                      <p className="text-white text-sm text-center">{card.bank.name}</p>
                      <div className={`mt-2 text-xs ${
                        card.status === "approved" ? "text-green-500" :
                        card.status === "pending" ? "text-yellow-500" : "text-red-500"
                      }`}>
                        {card.status === "approved" ? "تایید شده" :
                         card.status === "pending" ? "در انتظار تایید" : "رد شده"}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-[#23262F] p-4 rounded-xl">
            <h3 className="text-lg font-medium mb-4">راهنمای واریز با شناسه</h3>

            <div className="space-y-4">
              <div className="bg-[#1A1D21] p-4 rounded-lg border border-[#353945]">
                <h4 className="text-white font-medium mb-2">مرحله اول:</h4>
                <p className="text-gray-300 text-sm">
                  انتقال مبلغ با شناسه<br />
                  مبلغ موردنظر خود را از طریق ساتنا، پایا یا انتقال حساب به حساب، همراه با شناسه واریز، به حساب مقصد منتقل کنید.
                </p>
              </div>

              <div className="bg-[#1A1D21] p-4 rounded-lg border border-[#353945]">
                <h4 className="text-white font-medium mb-2">مرحله دوم:</h4>
                <p className="text-gray-300 text-sm">
                  انتقال مبلغ دلخواه<br />
                  مبلغ به صورت خودکار طی سیکل پایا* به حساب شما اضافه خواهد شد و به اطلاع شما خواهد رسید.<br />
                  * تا پیش از طی سیکل پایا، اکسچنجیم به وضعیت تراکنش شما، دسترسی ندارد.
                </p>
              </div>

              <div className="bg-[#1A1D21] p-4 rounded-lg border border-yellow-500/30">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 mt-0.5 ml-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-300 text-sm">
                    در واریز شناسه‌دار، با انتخاب بانک مبدا و مقصد یکسان، تراکنش سریع‌تر انجام خواهد شد.
                  </p>
                </div>
              </div>

              <div className="bg-[#1A1D21] p-4 rounded-lg border border-red-500/30">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mt-0.5 ml-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  <p className="text-gray-300 text-sm">
                    هنگام انتقال وجه، ثبت شناسه واریز الزامی است. در صورت عدم ثبت یا ثبت نادرست شناسه واریز، مبلغ به حساب شما واریز نخواهد شد و پیگیری آن توسط اکسچنجیم امکان‌پذیر نخواهد بود.
                  </p>
                </div>
              </div>

              <div className="bg-[#1A1D21] p-4 rounded-lg border border-blue-500/30">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mt-0.5 ml-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-300 text-sm">
                    مبلغ واریزی شما پس از کسر 0.025% کارمزد انتقال (معادل 250 تومان به ازای هر 1 میلیون تومان) به حساب شما در اکسچنجیم واریز خواهد شد.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default WalletChargePage;
