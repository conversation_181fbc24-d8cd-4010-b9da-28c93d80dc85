"use client";
import Image from "next/image";

const cryptoData = [
  {
    name: "BTC/USDT",
    symbol: "Bitcoin",
    image: "/images/btc-logo.png",
    price: "$65,230.56",
    change: "8.88%+",
    marketCap: "$1,149,301,700,874",
    volume: "$37,169,951,326",
    volumeCrypto: "638,587 BTC"
  },
  {
    name: "ETH/USDT",
    symbol: "Ethereum",
    image: "/images/eth-logo.png",
    price: "$3,456.78",
    change: "5.67%+",
    marketCap: "$415,789,456,123",
    volume: "$18,456,789,123",
    volumeCrypto: "5,345,678 ETH"
  },
  {
    name: "Tron/USDT",
    symbol: "Tron",
    image: "/images/tron.png",
    price: "$567.89",
    change: "3.45%+",
    marketCap: "$87,654,321,098",
    volume: "$5,678,901,234",
    volumeCrypto: "10,012,345 BNB"
  },
  {
    name: "BNB/USDT",
    symbol: "Binance Coin",
    image: "/images/bnb-logo.png",
    price: "$123.45",
    change: "12.34%+",
    marketCap: "$45,678,901,234",
    volume: "$3,456,789,012",
    volumeCrypto: "28,012,345 SOL"
  },
  {
    name: "DOGE/USDT",
    symbol: "Dogecoin",
    image: "/images/dogcoin.png",
    price: "$0.56",
    change: "2.34%+",
    marketCap: "$19,876,543,210",
    volume: "$1,234,567,890",
    volumeCrypto: "2,201,234,567 ADA"
  },
  {
    name: "LTC/USDT",
    symbol: "Litecoin",
    image: "/images/ltc.png",
    price: "$0.78",
    change: "4.21%+",
    marketCap: "$37,654,321,098",
    volume: "$2,345,678,901",
    volumeCrypto: "3,012,345,678 XRP"
  },
  {
    name: "ONT/USDT",
    symbol: "Ontology Token",
    image: "/images/ont.png",
    price: "$145.67",
    change: "9.87%+",
    marketCap: "$56,789,012,345",
    volume: "$4,567,890,123",
    volumeCrypto: "31,234,567 SOL"
  },
  {
    name: "ADA/USDT",
    symbol: "Cardano",
    image: "/images/ada.png",
    price: "$0.45",
    change: "1.23%+",
    marketCap: "$15,678,901,234",
    volume: "$987,654,321",
    volumeCrypto: "2,198,765,432 ADA"
  },
  {
    name: "ZIL/USDT",
    symbol: "Zilliqa",
    image: "/images/zil.png",
    price: "$34.56",
    change: "6.78%+",
    marketCap: "$12,345,678,901",
    volume: "$876,543,210",
    volumeCrypto: "25,432,109 AVAX"
  }
];

export default function Price() {
  return (
    <div className="bg-transparent md:bg-[#18191D] text-white p-6 rounded-2xl">
      <h1 className="text-2xl font-bold text-center md:text-right mb-5 whitespace-nowrap">
        قیمت لحظه ای رمز ارز ها
      </h1>
      <div className="hidden md:flex md:justify-start">
        <input
          type="text"
          placeholder="جستجو"
          className="p-2 pr-10 bg-[#23262F] rounded-md w-full md:w-[286px] h-[45px] text-right my-5"
        />
      </div>

      {/* Mobile View */}
      <div className="md:hidden landscape:hidden flex flex-col items-center space-y-4">
        {cryptoData.map((crypto, index) => (
          <CryptoCard key={index} crypto={crypto} index={index} />
        ))}
      </div>

      {/* Desktop/Tablet/Landscape View */}
      <div className="hidden md:block landscape:block overflow-x-auto">
        <table className="w-full text-center" dir="rtl">
          <thead className="text-xs text-[#B1B5C3] bg-[#23262F]">
            <tr>
              <th scope="col" className="px-6 py-3">
                رمز ارز
              </th>
              <th scope="col" className="px-6 py-3">
                قیمت
              </th>
              <th scope="col" className="px-6 py-3">
                تغییرات 24 ساعت
              </th>
              <th scope="col" className="px-6 py-3">
                حجم بازار
              </th>
              <th scope="col" className="px-6 py-3">
                24 valume
              </th>
              <th scope="col" className="px-6 py-3">
                نمودار
              </th>
              <th scope="col" className="px-6 py-3">
                عملیات
              </th>
            </tr>
          </thead>
          <tbody className="text-[#FCFCFD]">
            {cryptoData.map((crypto, index) => (
              <CryptoRow key={index} crypto={crypto} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function CryptoCard({ crypto, index }) {
  return (
    <div
      className={`w-[85vw] sm:w-[80vw] max-w-[600px] p-4 rounded-lg ${
        index % 2 === 0 ? "bg-[#18191D]" : "bg-[#1C1E24]"
      }`}
    >
      <div className="flex flex-row-reverse items-center mb-4 justify-between">
        <div className="flex flex-col items-end">
          <p className="font-medium text-lg text-right">{crypto.name}</p>
          <span className="text-xs text-[#B1B5C3] text-right">
            {crypto.symbol}
          </span>
        </div>
        <div className="w-10 h-10 relative z-10">
          <Image
            className="w-full h-full"
            src={crypto.image}
            height={100}
            width={100}
            alt={crypto.name}
          />
        </div>
      </div>
      <div className="flex justify-between items-center mb-3 text-center">
        <div className="text-[#2FA766] flex items-center">
          <div className="w-2 h-2 ml-2">
            <Image
              className="w-2 h-2"
              src="/images/up.png"
              height={100}
              width={100}
              alt="change-icon"
            />
          </div>
          {crypto.change}
        </div>
        <p className="text-lg">{crypto.price}</p>
      </div>
      <div className="mb-3 text-center">
        <p className="text-[#B1B5C3] mb-1">حجم بازار</p>
        <p>{crypto.marketCap}</p>
      </div>
      <div className="mb-3 text-center">
        <p className="text-[#B1B5C3] mb-1">24 valume</p>
        <p>{crypto.volume}</p>
        <p className="text-xs text-[#B1B5C3]">{crypto.volumeCrypto}</p>
      </div>
      <div className="mt-auto pt-4 flex justify-center">
        <button className="w-full border border-[#353945] rounded-lg py-3 text-base cursor-pointer bg-[#23262F]">
          معامله
        </button>
      </div>
    </div>
  );
}

function CryptoRow({ crypto }) {
  return (
    <tr className="odd:bg-[#18191D] even:bg-[#1C1E24]">
      <td className="py-4 text-base">
        <div className="flex items-center justify-start">
          <div className="w-6 h-6 ml-2">
            <Image
              className="w-full h-full"
              src="/images/star.png"
              height={100}
              width={100}
              alt="icon"
            />
          </div>
          <div className="w-10 h-10 relative ml-3">
            <Image
              className="w-full h-full"
              src={crypto.image}
              height={100}
              width={100}
              alt={crypto.name}
            />
          </div>
          <div className="mr-3 text-right">
            <p className="font-medium text-right">{crypto.name}</p>
            <span className="text-xs text-[#B1B5C3] text-right">
              {crypto.symbol}
            </span>
          </div>
        </div>
      </td>
      <td className="py-4 text-base">{crypto.price}</td>
      <td className="py-4 text-[#2FA766] flex items-center justify-center">
        <div className="w-5 h-5 ml-2">
          <Image
            className="w-full h-full"
            src="/images/up.png"
            height={100}
            width={100}
            alt="change-icon"
          />
        </div>
        {crypto.change}
      </td>
      <td>{crypto.marketCap}</td>
      <td className="py-4">
        <div>
          <p className="text-lg font-medium">{crypto.volume}</p>
          <p className="text-sm text-[#B1B5C3] font-light" dir="ltr">
            {crypto.volumeCrypto}
          </p>
        </div>
      </td>
      <th className="relative">
        <div className="w-20 h-10 mx-auto">
          <Image
            className="w-full h-full object-contain"
            src="/images/chart.png"
            height={100}
            width={200}
            alt="chart"
          />
        </div>
      </th>
      <th scope="row" className="py-4 font-medium whitespace-nowrap">
        <button className="border border-[#353945] rounded-lg px-8 py-2 text-base cursor-pointer mr-3 bg-[#23262F]">
          معامله
        </button>
      </th>
    </tr>
  );
}
