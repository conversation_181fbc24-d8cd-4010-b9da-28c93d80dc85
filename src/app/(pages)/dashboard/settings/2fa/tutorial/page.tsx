"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { FiArrowLeft, FiShield, FiSmartphone, FiDownload, FiSearch, FiKey, FiCheckCircle } from "react-icons/fi";
import { FaShieldAlt, FaLock, FaUserShield } from "react-icons/fa";

// Animated background component with particles
const ParticlesBackground = () => {
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, size: number, color: string, speed: number}>>([]);

  useEffect(() => {
    // Create particles
    const newParticles = Array.from({ length: 30 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      color: i % 3 === 0 ? 'rgba(59, 130, 246, 0.3)' : i % 3 === 1 ? 'rgba(16, 185, 129, 0.3)' : 'rgba(99, 102, 241, 0.3)',
      speed: Math.random() * 0.2 + 0.1
    }));

    setParticles(newParticles);

    // Animation loop
    const interval = setInterval(() => {
      setParticles(prev =>
        prev.map(particle => ({
          ...particle,
          x: (particle.x + particle.speed) % 100,
          y: (particle.y + particle.speed * 0.5) % 100
        }))
      );
    }, 50);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map(particle => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color,
          }}
          animate={{
            opacity: [0.3, 0.8, 0.3],
          }}
          transition={{
            duration: 2 + Math.random() * 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

const TwoFactorTutorialPage: React.FC = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState<number | null>(null);

  const steps = [
    {
      id: 1,
      title: "دانلود برنامه Google Authenticator",
      description: "ابتدا برنامه Google Authenticator را از App Store یا Google Play دانلود و نصب کنید.",
      icon: <FiDownload className="w-6 h-6 text-blue-400" />,
      image: "/images/2fa/google-auth-download.png",
      links: [
        {
          title: "دانلود برای Android",
          url: "https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2",
        },
        {
          title: "دانلود برای iOS",
          url: "https://apps.apple.com/us/app/google-authenticator/id388497605",
        },
      ],
    },
    {
      id: 2,
      title: "باز کردن برنامه و افزودن حساب جدید",
      description: "برنامه Google Authenticator را باز کنید و روی دکمه + در گوشه صفحه ضربه بزنید تا حساب جدیدی اضافه کنید.",
      icon: <FiSmartphone className="w-6 h-6 text-blue-400" />,
      image: "/images/2fa/google-auth-add.png",
    },
    {
      id: 3,
      title: "فعال‌سازی احراز هویت دو مرحله‌ای در اکسچنجیم",
      description: "به صفحه تنظیمات اکسچنجیم بروید و گزینه 'فعال‌سازی' را برای احراز هویت دو مرحله‌ای انتخاب کنید.",
      icon: <FiShield className="w-6 h-6 text-blue-400" />,
      image: "/images/2fa/exchangim-2fa-setup.png",
    },
    {
      id: 4,
      title: "اسکن کد QR",
      description: "در برنامه Google Authenticator، گزینه 'اسکن بارکد' را انتخاب کنید و کد QR نمایش داده شده در وب‌سایت اکسچنجیم را اسکن کنید.",
      icon: <FiSearch className="w-6 h-6 text-blue-400" />,
      image: "/images/2fa/scan-qr-code.png",
    },
    {
      id: 5,
      title: "وارد کردن کد تایید",
      description: "پس از اسکن کد QR، برنامه Google Authenticator یک کد 6 رقمی نمایش می‌دهد. این کد را در فیلد مربوطه در وب‌سایت اکسچنجیم وارد کنید.",
      icon: <FiKey className="w-6 h-6 text-blue-400" />,
      image: "/images/2fa/enter-verification-code.png",
    },
    {
      id: 6,
      title: "تکمیل فرآیند",
      description: "پس از وارد کردن کد تایید، دکمه 'تایید' را بزنید تا فرآیند فعال‌سازی احراز هویت دو مرحله‌ای تکمیل شود.",
      icon: <FiCheckCircle className="w-6 h-6 text-blue-400" />,
      image: "/images/2fa/2fa-complete.png",
    },
  ];

  useEffect(() => {
    // Set active step on hover
    const handleStepHover = (id: number | null) => {
      setActiveStep(id);
    };

    // Add event listeners for step cards
    document.querySelectorAll('.step-card').forEach((card, index) => {
      card.addEventListener('mouseenter', () => handleStepHover(index + 1));
      card.addEventListener('mouseleave', () => handleStepHover(null));
    });

    return () => {
      // Clean up event listeners
      document.querySelectorAll('.step-card').forEach((card, index) => {
        card.removeEventListener('mouseenter', () => handleStepHover(index + 1));
        card.removeEventListener('mouseleave', () => handleStepHover(null));
      });
    };
  }, []);

  return (
    <div className="bg-transparent p-6 rounded-2xl">
      <motion.div
        className="p-6 space-y-6 rounded-2xl bg-gradient-to-br from-[#18191D] to-[#1C1E24] border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 shadow-lg relative overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
      >
        {/* Animated particles background */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 30 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: Math.random() * 3 + 1,
                height: Math.random() * 3 + 1,
                backgroundColor: i % 3 === 0 ? 'rgba(59, 130, 246, 0.3)' : i % 3 === 1 ? 'rgba(16, 185, 129, 0.3)' : 'rgba(99, 102, 241, 0.3)',
              }}
              animate={{
                x: [0, Math.random() * 50 - 25],
                y: [0, Math.random() * 50 - 25],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        {/* Dotted background pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

        {/* Decorative gradient elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl -mr-32 -mt-32"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl -ml-32 -mb-32"></div>

        {/* Header with enhanced styling */}
        <div className="flex items-center justify-between mb-8 relative">
          <motion.button
            whileHover={{ x: -5 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => router.push("/dashboard/settings")}
            className="flex items-center text-[#B1B5C3] hover:text-white transition-colors bg-[#23262F]/50 px-3 py-2 rounded-lg"
          >
            <FiArrowLeft className="mr-2" />
            بازگشت به تنظیمات
          </motion.button>

          <motion.div
            className="flex items-center"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <div className="bg-blue-500/20 p-2 rounded-full ml-3 relative">
              <FaShieldAlt className="w-6 h-6 text-blue-400" />
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-blue-400/30"
                animate={{ scale: [1, 1.2, 1], opacity: [0.7, 0, 0.7] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
            <h3 className="text-2xl text-[#FCFCFD] font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              آموزش فعال‌سازی احراز هویت دو مرحله‌ای
            </h3>
          </motion.div>
        </div>

        {/* Introduction card with enhanced styling */}
        <motion.div
          className="bg-gradient-to-br from-[#23262F]/80 to-[#23262F]/50 p-6 rounded-xl mb-8 border border-[#353945]/20 relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          whileHover={{ boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)", y: -5 }}
        >
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500/5 rounded-full blur-2xl"></div>
          <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-purple-500/5 rounded-full blur-2xl"></div>

          <div className="flex items-start">
            <div className="bg-gradient-to-br from-blue-500/30 to-blue-600/30 p-3 rounded-xl mr-4 relative">
              <FaUserShield className="w-8 h-8 text-blue-400" />
              <motion.div
                className="absolute inset-0 rounded-xl border border-blue-400/30"
                animate={{ scale: [1, 1.1, 1], opacity: [1, 0.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
            <div>
              <h4 className="text-[#FCFCFD] font-bold text-xl mb-2">چرا باید احراز هویت دو مرحله‌ای را فعال کنیم؟</h4>
              <p className="text-[#B1B5C3] text-base leading-relaxed">
                احراز هویت دو مرحله‌ای یک لایه امنیتی اضافی برای حساب کاربری شما فراهم می‌کند. با فعال‌سازی این قابلیت، حتی اگر کسی به رمز عبور شما دسترسی پیدا کند، بدون دسترسی به تلفن همراه شما نمی‌تواند وارد حساب کاربری شما شود.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Steps with enhanced styling */}
        <div className="space-y-8">
          {steps.map((step) => (
            <motion.div
              key={step.id}
              className="step-card bg-gradient-to-br from-[#23262F]/70 to-[#1C1E24]/70 p-6 rounded-xl border border-[#353945]/20 relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: step.id * 0.1 }}
              whileHover={{
                y: -5,
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
                borderColor: "rgba(59, 130, 246, 0.3)"
              }}
            >
              {/* Step number indicator */}
              <div className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-full text-blue-400 font-bold text-lg">
                {step.id}
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500/5 rounded-full blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-purple-500/5 rounded-full blur-2xl"></div>

              <div className="flex items-start mb-4 pr-10">
                <div className="bg-gradient-to-br from-[#353945]/80 to-[#2A2D36]/80 p-3 rounded-xl mr-4 relative">
                  <motion.div
                    className="text-blue-400"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    {step.icon}
                  </motion.div>
                  <motion.div
                    className="absolute inset-0 rounded-xl border border-blue-400/20"
                    animate={{ scale: [1, 1.1, 1], opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </div>
                <div>
                  <h4 className="text-[#FCFCFD] font-bold text-xl mb-2">
                    {step.title}
                  </h4>
                  <p className="text-[#B1B5C3] text-base">{step.description}</p>

                  {step.links && (
                    <div className="mt-4 space-x-3 space-x-reverse">
                      {step.links.map((link, index) => (
                        <motion.a
                          key={index}
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-block px-4 py-2 bg-gradient-to-r from-[#353945]/90 to-[#2A2D36]/90 text-[#B1B5C3] rounded-lg text-sm hover:text-white transition-colors border border-[#353945]/50"
                          whileHover={{ y: -2, boxShadow: "0 5px 15px rgba(0, 0, 0, 0.2)" }}
                          whileTap={{ y: 0 }}
                        >
                          {link.title}
                        </motion.a>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {step.image && (
                <motion.div
                  className="mt-4 bg-gradient-to-br from-[#141416]/90 to-[#1A1B1F]/90 p-3 rounded-xl border border-[#353945]/20 overflow-hidden"
                  whileHover={{ boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)" }}
                >
                  <div className="h-[220px] w-full rounded-lg overflow-hidden flex items-center justify-center relative">
                    {/* Decorative background for images */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
                    <motion.div
                      className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-10"
                      animate={{ opacity: [0.05, 0.1, 0.05] }}
                      transition={{ duration: 3, repeat: Infinity }}
                    ></motion.div>

                    <div className="relative z-10 text-center p-4">
                      <motion.div
                        animate={{
                          scale: [1, 1.05, 1],
                          rotate: [0, 2, 0, -2, 0]
                        }}
                        transition={{ duration: 5, repeat: Infinity }}
                        className="mb-4"
                      >
                        {step.id === 1 && <FiDownload className="w-16 h-16 text-blue-400 mx-auto" />}
                        {step.id === 2 && <FiSmartphone className="w-16 h-16 text-blue-400 mx-auto" />}
                        {step.id === 3 && <FiShield className="w-16 h-16 text-blue-400 mx-auto" />}
                        {step.id === 4 && <FiSearch className="w-16 h-16 text-blue-400 mx-auto" />}
                        {step.id === 5 && <FiKey className="w-16 h-16 text-blue-400 mx-auto" />}
                        {step.id === 6 && <FiCheckCircle className="w-16 h-16 text-blue-400 mx-auto" />}
                      </motion.div>
                      <p className="text-[#B1B5C3] font-medium">تصویر مرحله {step.id}: {step.title}</p>
                    </div>
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>

        {/* Important notes with enhanced styling */}
        <motion.div
          className="mt-10 bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24]/80 p-6 rounded-xl border border-[#353945]/20 relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          whileHover={{ boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)", y: -5 }}
        >
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500/5 rounded-full blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-500/5 rounded-full blur-2xl"></div>

          <div className="flex items-start mb-4">
            <div className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 p-3 rounded-xl mr-4 relative">
              <FaLock className="w-7 h-7 text-blue-400" />
              <motion.div
                className="absolute inset-0 rounded-xl border border-blue-400/30"
                animate={{ scale: [1, 1.1, 1], opacity: [1, 0.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
            <div>
              <h4 className="text-[#FCFCFD] font-bold text-xl mb-3">نکات مهم</h4>
              <ul className="text-[#B1B5C3] text-base space-y-3">
                <motion.li
                  className="flex items-start"
                  whileHover={{ x: 5 }}
                >
                  <span className="inline-block w-5 h-5 bg-blue-500/20 rounded-full flex items-center justify-center mr-2 mt-1">•</span>
                  <span>کد مخفی نمایش داده شده را در جایی امن ذخیره کنید تا در صورت از دست دادن دسترسی به تلفن همراه، بتوانید حساب خود را بازیابی کنید.</span>
                </motion.li>
                <motion.li
                  className="flex items-start"
                  whileHover={{ x: 5 }}
                >
                  <span className="inline-block w-5 h-5 bg-blue-500/20 rounded-full flex items-center justify-center mr-2 mt-1">•</span>
                  <span>کدهای تایید هر 30 ثانیه تغییر می‌کنند، بنابراین باید کد را در همان زمان نمایش وارد کنید.</span>
                </motion.li>
                <motion.li
                  className="flex items-start"
                  whileHover={{ x: 5 }}
                >
                  <span className="inline-block w-5 h-5 bg-blue-500/20 rounded-full flex items-center justify-center mr-2 mt-1">•</span>
                  <span>در صورت تغییر تلفن همراه، باید قبل از حذف برنامه از گوشی قبلی، آن را در گوشی جدید نصب و حساب خود را منتقل کنید.</span>
                </motion.li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Button with enhanced styling */}
        <motion.div
          className="flex justify-center mt-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.5 }}
        >
          <motion.button
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 25px rgba(37, 99, 235, 0.5)"
            }}
            whileTap={{ scale: 0.95 }}
            className="py-3 px-8 rounded-xl bg-gradient-to-r from-blue-600 to-blue-400 text-white text-lg font-bold hover:from-blue-500 hover:to-blue-300 transition-all duration-300 relative overflow-hidden group"
            onClick={() => router.push("/dashboard/settings/2fa/setup")}
          >
            {/* Button glow effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/30 to-blue-400/0"
              animate={{ x: ['-100%', '100%'] }}
              transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1 }}
            />

            {/* Button text with icon */}
            <div className="relative z-10 flex items-center">
              <span>شروع فعال‌سازی احراز هویت دو مرحله‌ای</span>
              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="mr-2"
              >
                <FiShield className="w-5 h-5" />
              </motion.div>
            </div>
          </motion.button>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default TwoFactorTutorialPage;
