"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { reset2FA } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import OTPInput from "react-otp-input";
import { useRouter } from "next/navigation";

const ResetTwoFactorPage: React.FC = () => {
  const router = useRouter();
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleResetTwoFactor = async () => {
    if (verificationCode.length !== 6) {
      toast.error("لطفا کد تایید را وارد کنید");
      return;
    }

    setIsLoading(true);
    try {
      const result = await reset2FA(verificationCode);
      if (result.isError) {
        toast.error(result.message || "خطا در بازنشانی احراز هویت دو مرحله‌ای");
      } else {
        toast.success(result.message || "احراز هویت دو مرحله‌ای با موفقیت بازنشانی شد");
        setTimeout(() => {
          router.push("/dashboard/settings");
        }, 500);
      }
    } catch (error) {
      toast.error("خطا در بازنشانی احراز هویت دو مرحله‌ای");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-transparent p-6 rounded-2xl">
      <motion.div
        className="p-6 space-y-6 rounded-2xl bg-gradient-to-br from-[#18191D] to-[#1C1E24] border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 shadow-lg relative overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
      >
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
        <h3 className="text-xl text-[#FCFCFD] font-medium text-center mb-4">بازنشانی احراز هویت دو مرحله‌ای</h3>
        <p className="text-[#B1B5C3] text-sm mb-6 text-center">
          برای بازنشانی احراز هویت دو مرحله‌ای، کد تایید را از برنامه Google Authenticator وارد کنید.
        </p>

        <div className="mb-6">
          <p className="text-[#FCFCFD] text-sm mb-2 text-center">کد تایید را وارد کنید:</p>
          <div className="flex justify-center" dir="ltr">
            <OTPInput
              value={verificationCode}
              onChange={setVerificationCode}
              numInputs={6}
              shouldAutoFocus={true}
              renderSeparator={<span className="w-2"></span>}
              renderInput={(props) => (
                <input
                  {...props}
                  className="!w-[40px] !h-[45px] !text-white !font-bold !bg-[#141416]/80 !border !border-gray-700 !rounded-lg !mx-1 focus:!border-blue-500 focus:!shadow-[0px_0px_15px_rgba(72,153,235,0.3)] !transition-all !duration-300 !outline-none"
                />
              )}
              containerStyle="flex justify-center items-center gap-1"
            />
          </div>
        </div>

        <div className="flex justify-between gap-4">
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="flex-1 py-2.5 rounded-lg bg-[#353945] text-[#B1B5C3] hover:bg-[#4A4F5E] hover:text-white transition-all duration-300"
            onClick={() => router.push("/dashboard/settings")}
          >
            انصراف
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="flex-1 py-2.5 rounded-lg bg-gradient-to-r from-orange-600 to-orange-400 text-white hover:from-orange-500 hover:to-orange-300 transition-all duration-300"
            onClick={handleResetTwoFactor}
            disabled={isLoading || verificationCode.length !== 6}
          >
            {isLoading ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
              </div>
            ) : (
              "بازنشانی"
            )}
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

export default ResetTwoFactorPage;
