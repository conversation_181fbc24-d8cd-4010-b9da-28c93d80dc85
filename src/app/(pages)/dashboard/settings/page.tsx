"use client";

import SecureTab from "@/components/dashboard/settings/secureTab";
import UserinfoTab from "@/components/dashboard/settings/userinfoTab";
import Image from "next/image";
import React, { useState } from "react";

interface TabProps {
  label: string;
  iconSrc: string;
  isActive: boolean;
  onClick: () => void;
}

const Tab: React.FC<TabProps> = ({ label, iconSrc, isActive, onClick }) => (
  <div
    onClick={onClick}
    className={`${
      isActive ? "border-b-2 border-[#E6E7E8] text-[#777E90]" : "border-b-2 border-[#353945]"
    } w-1/2 flex items-center gap-x-2 justify-center pb-2 cursor-pointer`}
  >
    <span>{label}</span>
    <Image className="w-5 h-5" src={iconSrc} height={20} width={20} alt={label} />
  </div>
);

const Page: React.FC = () => {
  const [selected, setSelected] = useState<number>(1);

  return (
    <div className="bg-transparent p-6 rounded-2xl">
      <div className="flex justify-between mb-6">
        <Tab
          label="امنیت"
          iconSrc="/images/secure.svg"
          isActive={selected === 1}
          onClick={() => setSelected(1)}
        />
        <Tab
          label="اطلاعات شخصی"
          iconSrc="/images/user-info.svg"
          isActive={selected === 2}
          onClick={() => setSelected(2)}
        />
      </div>

      <div>
        {selected === 1 && <SecureTab />}
        {selected === 2 && <UserinfoTab />}
      </div>
    </div>
  );
};

export default Page;
