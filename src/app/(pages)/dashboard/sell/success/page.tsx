"use client";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { getUserCurrency } from "@/requests/dashboardRequest";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  toman_sell_price: number;
  name: string;
  id: number;
}

interface Transaction {
  id: number;
  type: string;
  amount: string;
  balance_before: string;
  balance_after: string;
  created_at: string;
  currency_id: number;
}

const SellSuccessPage = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [currency, setCurrency] = useState<CurrencyItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get URL parameters
  const transactionParam = searchParams.get("transaction");
  const walletBalance = searchParams.get("wallet_balance");
  const tomanBalance = searchParams.get("toman_balance");
  const cryptoAmount = searchParams.get("crypto_amount");
  const currencyId = searchParams.get("currency_id");

  useEffect(() => {
    const loadData = async () => {
      try {
        // Parse transaction data
        if (transactionParam) {
          const parsedTransaction = JSON.parse(decodeURIComponent(transactionParam));
          setTransaction(parsedTransaction);
        }

        // Get currency data
        if (currencyId) {
          const result = await getUserCurrency();
          if (!result.isError) {
            const foundCurrency = result.data.find(
              (curr: CurrencyItem) => curr.id === parseInt(currencyId)
            );
            setCurrency(foundCurrency || null);
          }
        }
      } catch (error) {
        console.error("Error loading transaction data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [transactionParam, currencyId]);

  if (!transaction && !isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-[800px] mx-auto bg-[#18191D] p-6 rounded-2xl text-center"
      >
        <div className="py-12">
          <div className="bg-yellow-500/20 p-4 rounded-full mb-4 mx-auto w-fit">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-yellow-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            اطلاعات تراکنش در دسترس نیست
          </h1>
          <p className="text-gray-400 mb-8">
            متأسفانه اطلاعات تراکنش فروش در دسترس نیست.
          </p>
          <Link href="/dashboard/sell">
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="px-6 py-3 rounded-xl bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-green-600/20 font-medium text-base"
            >
              بازگشت به صفحه فروش
            </motion.button>
          </Link>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-[800px] mx-auto bg-[#18191D] p-6 rounded-2xl relative overflow-hidden"
    >
      {/* Success Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex flex-col items-center justify-center mb-8 relative z-10"
      >
        <div className="bg-green-500/20 p-4 rounded-full mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-green-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
          فروش با موفقیت انجام شد
        </h1>
        <p className="text-sm text-gray-400">
          تراکنش شما با موفقیت ثبت و پردازش شد
        </p>
      </motion.div>

      {/* Transaction Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-gradient-to-br from-[#1A1D21] to-[#23262F] p-6 rounded-xl mb-6 border border-[#353945]/30 relative z-10"
      >
        <h2 className="text-lg font-semibold mb-4 text-center">خلاصه تراکنش</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Transaction Details */}
          <div className="space-y-4">
            {/* Transaction ID */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">شماره تراکنش:</span>
              <span className="font-medium text-white">#{transaction?.id}</span>
            </div>

            {/* Transaction Type */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">نوع تراکنش:</span>
              <span className="font-medium text-green-400">فروش</span>
            </div>

            {/* Transaction Date */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">تاریخ:</span>
              <span className="font-medium text-white">
                {transaction?.created_at ? new Date(transaction.created_at).toLocaleDateString('fa-IR') : 'نامشخص'}
              </span>
            </div>

            {/* Toman Balance */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">موجودی تومانی:</span>
              <span className="font-medium text-white">
                {tomanBalance ? `${sliceNumber(Number(tomanBalance).toFixed(0))} تومان` : 'نامشخص'}
              </span>
            </div>
          </div>

          <div className="space-y-4">
            {/* Crypto Amount */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">مقدار فروخته شده:</span>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={20}
                    width={20}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <span className="font-medium text-white">
                  {cryptoAmount ? `${Number(cryptoAmount).toFixed(6)} ${currency?.coin_type}` : 'نامشخص'}
                </span>
              </div>
            </div>

            {/* Balance Before */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">موجودی قبلی:</span>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={20}
                    width={20}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <span className="font-medium text-white">
                  {transaction?.balance_before ? `${Number(transaction.balance_before).toFixed(6)} ${currency?.coin_type}` : 'نامشخص'}
                </span>
              </div>
            </div>

            {/* Balance After */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">موجودی فعلی:</span>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={20}
                    width={20}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <span className="font-medium text-white">
                  {walletBalance ? `${Number(walletBalance).toFixed(6)} ${currency?.coin_type}` : 'نامشخص'}
                </span>
              </div>
            </div>

            {/* Current Wallet Balance */}
            <div className="flex justify-between items-center bg-[#1A1D21]/70 p-3 rounded-lg border border-[#353945]/20">
              <span className="text-gray-400">موجودی کیف پول:</span>
              <div className="flex items-center">
                {!isLoading && currency && (
                  <Image
                    src={`https://api.exchangim.com/storage/${currency.coin_icon}`}
                    height={20}
                    width={20}
                    alt={currency.coin_type}
                    className="ml-2 rounded-full"
                  />
                )}
                <span className="font-medium text-white">
                  {transaction?.balance_after ? `${Number(transaction.balance_after).toFixed(6)} ${currency?.coin_type}` : 'نامشخص'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-8"
      >
        <Link href="/dashboard">
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="px-6 py-3 rounded-xl bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-blue-600/20 font-medium text-base relative overflow-hidden w-full sm:w-auto"
          >
            بازگشت به داشبورد
          </motion.button>
        </Link>

        <Link href="/dashboard/sell">
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="px-6 py-3 rounded-xl bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-green-600/20 font-medium text-base relative overflow-hidden w-full sm:w-auto"
          >
            فروش مجدد
          </motion.button>
        </Link>
      </motion.div>
    </motion.div>
  );
};

export default SellSuccessPage;
