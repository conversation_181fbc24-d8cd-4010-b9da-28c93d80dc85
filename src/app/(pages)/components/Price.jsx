"use client";

import { useState, useEffect } from "react";
import "./price.css";

export default function CryptoDashboard() {
  const [cryptoData, setCryptoData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState([]);
  const [isMounted, setIsMounted] = useState(false);
  const [activeTab, setActiveTab] = useState("top"); // "top", "gainers", "losers"
  const [visibleItems, setVisibleItems] = useState(5); // Number of items to show initially
  const [showAll, setShowAll] = useState(false); // Track if all items are shown

  // اطمینان از اینکه کامپوننت فقط در سمت کلاینت رندر می‌شود
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Fetch cryptocurrency data from CoinGecko API
  const fetchCryptoData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(
        "https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&ids=bitcoin,ethereum,ripple,cardano,solana,polkadot,dogecoin,shiba-inu,litecoin,tron,binancecoin,avalanche-2,chainlink,polygon,uniswap,stellar,cosmos,monero,algorand,tezos&order=market_cap_desc&per_page=20&page=1&sparkline=false&price_change_percentage=24h"
      );

      if (!response.ok) {
        throw new Error("Failed to fetch data from CoinGecko API");
      }

      const data = await response.json();
      setCryptoData(data);
      setFilteredData(data);
    } catch (error) {
      console.error("Error fetching crypto data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // اطمینان از اینکه کد فقط در سمت کلاینت اجرا می‌شود
    if (typeof window !== 'undefined') {
      fetchCryptoData();
      // Refresh data every 60 seconds
      const interval = setInterval(fetchCryptoData, 60000);
      return () => clearInterval(interval);
    }
  }, []);

  // Calculate counts for tab badges
  const getTabCounts = () => {
    if (!cryptoData || cryptoData.length === 0) return { top: 0, gainers: 0, losers: 0 };

    const gainersCount = cryptoData.filter(crypto => crypto.price_change_percentage_24h > 0).length;
    const losersCount = cryptoData.filter(crypto => crypto.price_change_percentage_24h < 0).length;

    return {
      top: cryptoData.length,
      gainers: gainersCount,
      losers: losersCount
    };
  };

  // Function to handle showing more items
  const handleShowMore = () => {
    if (filteredData.length <= visibleItems) {
      setShowAll(true);
    } else {
      const newVisibleItems = visibleItems + 5; // Show 5 more items
      setVisibleItems(newVisibleItems);
      setShowAll(newVisibleItems >= filteredData.length);
    }
  };

  // Filter data based on search term and active tab
  useEffect(() => {
    let tabFiltered = [...cryptoData];

    // First apply tab filtering
    if (activeTab === "gainers") {
      // Sort by price change percentage (descending)
      tabFiltered = [...cryptoData]
        .filter(crypto => crypto.price_change_percentage_24h > 0)
        .sort((a, b) => b.price_change_percentage_24h - a.price_change_percentage_24h);
    } else if (activeTab === "losers") {
      // Sort by price change percentage (ascending)
      tabFiltered = [...cryptoData]
        .filter(crypto => crypto.price_change_percentage_24h < 0)
        .sort((a, b) => a.price_change_percentage_24h - b.price_change_percentage_24h);
    } else {
      // Default "top" tab - already sorted by market cap in the API call
      tabFiltered = [...cryptoData];
    }

    // Then apply search filtering
    if (searchTerm.trim() === "") {
      setFilteredData(tabFiltered);
    } else {
      const searchFiltered = tabFiltered.filter(
        (crypto) =>
          crypto.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          crypto.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredData(searchFiltered);
    }

    // Reset visible items when tab or search changes
    setVisibleItems(5);
    setShowAll(false);
  }, [searchTerm, cryptoData, activeTab]);

  return (
    <div className="bg-[#18191D] text-white p-4 sm:p-6 md:px-16 md:py-12 rounded-2xl min-h-screen mx-2 sm:mx-4 md:mx-8">
      <div className="flex flex-col md:flex-row-reverse justify-between items-center mb-6 md:mb-10">
        <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6 md:mb-0 text-center md:text-right bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
          قیمت لحظه ای رمز ارز ها
        </h1>

        <div className="relative w-full md:w-auto">
          <input
            type="text"
            placeholder="جستجو..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="p-3 px-12 sm:px-14 bg-[#23262F] rounded-xl w-full md:w-[320px] h-[45px] sm:h-[50px] text-right border border-[#353945] focus:border-blue-500 focus:outline-none transition-all shadow-md"
          />
          <div className="absolute right-4 sm:right-5 top-1/2 transform -translate-y-1/2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex justify-center mb-6 sm:mb-8 overflow-x-auto no-scrollbar">
        <div className="flex bg-[#23262F] rounded-xl p-1 sm:p-1.5 shadow-lg">

          <button
            onClick={() => setActiveTab("gainers")}
            className={`px-3 sm:px-6 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-all flex items-center ${
              activeTab === "gainers"
                ? "bg-gradient-to-r from-green-500 to-teal-500 text-white shadow-md"
                : "text-[#B1B5C3] hover:text-white"
            }`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
            </svg>
            <span>پرسودها</span>
            <span className={`mr-1 sm:mr-2 px-1.5 sm:px-2 py-0.5 text-xs rounded-full ${
              activeTab === "gainers" ? "bg-white/20" : "bg-[#353945]"
            }`}>
              {getTabCounts().gainers}
            </span>
          </button>
          <button
            onClick={() => setActiveTab("top")}
            className={`px-3 sm:px-6 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-all flex items-center ${
              activeTab === "top"
                ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md"
                : "text-[#B1B5C3] hover:text-white"
            }`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
            <span>رمز ارز های برتر</span>
            <span className={`mr-1 sm:mr-2 px-1.5 sm:px-2 py-0.5 text-xs rounded-full ${
              activeTab === "top" ? "bg-white/20" : "bg-[#353945]"
            }`}>
              {getTabCounts().top}
            </span>
          </button>
          <button
            onClick={() => setActiveTab("losers")}
            className={`px-3 sm:px-6 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-all flex items-center ${
              activeTab === "losers"
                ? "bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-md"
                : "text-[#B1B5C3] hover:text-white"
            }`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
            <span>پرضررها</span>
            <span className={`mr-1 sm:mr-2 px-1.5 sm:px-2 py-0.5 text-xs rounded-full ${
              activeTab === "losers" ? "bg-white/20" : "bg-[#353945]"
            }`}>
              {getTabCounts().losers}
            </span>

          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex flex-col justify-center items-center h-48 sm:h-64">
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-4 border-t-blue-500 border-r-purple-500 border-b-teal-500 border-l-pink-500"></div>
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
              <div className="h-6 w-6 sm:h-8 sm:w-8 rounded-full bg-[#18191D]"></div>
            </div>
          </div>
          <p className="mt-3 sm:mt-4 text-sm sm:text-base text-gray-400">در حال بارگذاری...</p>
        </div>
      ) : isMounted ? (
        <>
          <div className="mb-4 sm:mb-6 text-right">
            <h2 className={`text-lg sm:text-xl font-bold ${
              activeTab === "top"
                ? "text-blue-500"
                : activeTab === "gainers"
                  ? "text-green-500"
                  : "text-red-500"
            }`}>
              {activeTab === "top"
                ? "رمز ارز های برتر بازار"
                : activeTab === "gainers"
                  ? "رمز ارز های با بیشترین سود"
                  : "رمز ارز های با بیشترین ضرر"}
            </h2>
            <p className="text-[#B1B5C3] text-xs sm:text-sm mt-1">
              {activeTab === "top"
                ? "لیست برترین رمز ارز ها بر اساس ارزش بازار"
                : activeTab === "gainers"
                  ? "رمز ارز هایی که بیشترین رشد قیمت را در 24 ساعت گذشته داشته اند"
                  : "رمز ارز هایی که بیشترین کاهش قیمت را در 24 ساعت گذشته داشته اند"}
            </p>
          </div>

          {/* Desktop View - Table */}
          <div className="hidden md:block overflow-x-auto rounded-xl shadow-lg">
            <table className="w-full text-center" dir="rtl">
              <thead className="text-xs text-[#B1B5C3] bg-[#23262F]">
                <tr>
                  <th scope="col" className="px-6 py-4 text-right rounded-tr-xl">
                    رمز ارز
                  </th>
                  <th scope="col" className="px-6 py-4">
                    قیمت (دلار)
                  </th>
                  <th scope="col" className="px-6 py-4">
                    تغییرات 24 ساعت
                  </th>
                  <th scope="col" className="px-6 py-4">
                    حجم معاملات (24h)
                  </th>
                  <th scope="col" className="px-6 py-4">
                    حجم بازار
                  </th>
                 
                  <th scope="col" className="px-6 py-4 rounded-tl-xl">
                    عملیات
                  </th>
                </tr>
              </thead>
              <tbody className="text-[#FCFCFD]">
                {filteredData.slice(0, visibleItems).map((crypto) => {
                  const priceChange = crypto.price_change_percentage_24h;
                  const isPositive = priceChange >= 0;

                  return (
                    <tr key={crypto.id} className="odd:bg-[#18191D] even:bg-[#1C1E24] border-b border-[#353945]/20 hover:bg-[#23262F]/50 transition-colors">
                      <td className="py-4 px-6 text-base">
                        <div className="flex items-center justify-start">
                          <div className="w-10 h-10 relative ml-3">
                            <img
                              className="w-full h-full rounded-full object-cover shadow-md"
                              src={crypto.image}
                              alt={crypto.name}
                            />
                          </div>
                          <div className="mr-3 text-right">
                            <p className="font-medium text-right text-base">{crypto.name}</p>
                            <span className="text-xs text-[#B1B5C3] text-right">
                              {crypto.symbol.toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-base font-medium">
                        ${crypto.current_price.toLocaleString()}
                      </td>
                      <td className="py-4 px-6">
                        <div className={`flex items-center justify-center ${isPositive ? 'text-green-500' : 'text-red-500'} font-medium`}>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 ml-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d={isPositive ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"}
                            />
                          </svg>
                          <span>{isPositive ? '+' : ''}{priceChange.toFixed(2)}%</span>
                        </div>
                      </td>
                      <td className="py-4 px-6 font-medium">
                        ${crypto.total_volume.toLocaleString()}
                      </td>
                      <td className="py-4 px-6 font-medium">
                        ${crypto.market_cap.toLocaleString()}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex justify-center space-x-3 rtl:space-x-reverse">
                          <button className="border border-[#353945] rounded-lg px-4 py-2 text-sm font-medium cursor-pointer bg-[#23262F] hover:bg-[#2c2f3a] transition-colors shadow-md">
                            خرید
                          </button>
                          <button className="border border-[#353945] rounded-lg px-4 py-2 text-sm font-medium cursor-pointer bg-[#23262F] hover:bg-[#2c2f3a] transition-colors shadow-md">
                            فروش
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile View - Cards */}
          <div className="md:hidden space-y-4" dir="rtl">
            {filteredData.slice(0, visibleItems).map((crypto) => {
              const priceChange = crypto.price_change_percentage_24h;
              const isPositive = priceChange >= 0;

              return (
                <div key={crypto.id} className="bg-[#23262F] rounded-xl p-4 shadow-lg border border-[#353945]/20">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="w-10 h-10 relative ml-3">
                        <img
                          className="w-full h-full rounded-full object-cover shadow-md"
                          src={crypto.image}
                          alt={crypto.name}
                        />
                      </div>
                      <div>
                        <p className="font-medium text-base">{crypto.name}</p>
                        <span className="text-xs text-[#B1B5C3]">
                          {crypto.symbol.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className={`flex items-center ${isPositive ? 'text-green-500' : 'text-red-500'} font-medium`}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 ml-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d={isPositive ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"}
                        />
                      </svg>
                      <span>{isPositive ? '+' : ''}{priceChange.toFixed(2)}%</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mb-3">
                    <div>
                      <p className="text-xs text-[#B1B5C3]">قیمت (دلار)</p>
                      <p className="font-medium">${crypto.current_price.toLocaleString()}</p>
                    </div>
                    <div className="h-10 w-24">
                      <svg viewBox="0 0 100 50" className="w-full h-full">
                        <defs>
                          <linearGradient id={`gradient-mobile-${crypto.id}`} x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor={isPositive ? "rgba(34, 197, 94, 0.4)" : "rgba(239, 68, 68, 0.4)"} />
                            <stop offset="100%" stopColor={isPositive ? "rgba(34, 197, 94, 0)" : "rgba(239, 68, 68, 0)"} />
                          </linearGradient>
                        </defs>
                        <path
                          d={isPositive
                            ? "M0,50 L10,45 L20,48 L30,40 L40,42 L50,35 L60,30 L70,25 L80,20 L90,15 L100,10"
                            : "M0,10 L10,15 L20,12 L30,20 L40,18 L50,25 L60,30 L70,35 L80,40 L90,45 L100,50"}
                          fill="none"
                          stroke={isPositive ? "#22c55e" : "#ef4444"}
                          strokeWidth="2.5"
                        />
                        <path
                          d={isPositive
                            ? "M0,50 L10,45 L20,48 L30,40 L40,42 L50,35 L60,30 L70,25 L80,20 L90,15 L100,10 L100,50 L0,50"
                            : "M0,10 L10,15 L20,12 L30,20 L40,18 L50,25 L60,30 L70,35 L80,40 L90,45 L100,50 L100,50 L0,50 L0,10"}
                          fill={`url(#gradient-mobile-${crypto.id})`}
                          strokeWidth="0"
                        />
                      </svg>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-4 text-sm">
                    <div>
                      <p className="text-xs text-[#B1B5C3]">حجم معاملات (24h)</p>
                      <p className="font-medium">${crypto.total_volume.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-xs text-[#B1B5C3]">حجم بازار</p>
                      <p className="font-medium">${crypto.market_cap.toLocaleString()}</p>
                    </div>
                  </div>

                  <div className="flex justify-between space-x-reverse space-x-3">
                    <button className="flex-1 border border-[#353945] rounded-lg py-2.5 text-sm font-medium cursor-pointer bg-[#18191D] hover:bg-[#2c2f3a] transition-colors shadow-md">
                      خرید
                    </button>
                    <button className="flex-1 border border-[#353945] rounded-lg py-2.5 text-sm font-medium cursor-pointer bg-[#18191D] hover:bg-[#2c2f3a] transition-colors shadow-md">
                      فروش
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Show More Button - Only show if there are more items to display */}
          {filteredData.length > visibleItems && (
            <div className="mt-8 flex justify-center">
              <button
                onClick={handleShowMore}
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white font-medium shadow-lg hover:shadow-xl transition-all transform hover:scale-105 flex items-center"
              >
                <span>مشاهده بیشتر</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
          )}
        </>
      ) : null}
    </div>
  );
}