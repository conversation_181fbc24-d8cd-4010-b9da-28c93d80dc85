"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaStar, FaQuoteRight } from 'react-icons/fa';

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  
  const testimonials = [
    {
      id: 1,
      name: "علی محمدی",
      role: "معامله‌گر حرفه‌ای",
      avatar: "/images/avatar1.png",
      rating: 5,
      text: "اکسچنجیم بهترین پلتفرم معاملاتی است که تا به حال استفاده کرده‌ام. رابط کاربری عالی، کارمزد پایین و پشتیبانی سریع، تجربه فوق‌العاده‌ای را برایم رقم زده است."
    },
    {
      id: 2,
      name: "مریم حسینی",
      role: "سرمایه‌گذار تازه‌کار",
      avatar: "/images/avatar2.png",
      rating: 5,
      text: "به عنوان یک تازه‌کار در دنیای ارزهای دیجیتال، اکسچنجیم بهترین انتخاب برای من بود. آموزش‌های کاربردی و رابط کاربری ساده، شروع کار را برایم بسیار آسان کرد."
    },
    {
      id: 3,
      name: "رضا کریمی",
      role: "تحلیلگر بازار",
      avatar: "/images/avatar3.png",
      rating: 4,
      text: "ابزارهای تحلیلی اکسچنجیم بسیار پیشرفته و دقیق هستند. به عنوان یک تحلیلگر، تمام اطلاعات مورد نیاز برای تصمیم‌گیری درست را در اختیار دارم."
    },
  ];
  
  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [testimonials.length]);
  
  return (
    <section className="py-20 relative overflow-hidden" dir="rtl">
      {/* Background glow effect */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[70%] h-[400px] bg-blue-500/5 rounded-full blur-[100px] pointer-events-none"></div>
      
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <p className="text-center mb-4 flex items-center justify-center">
            <span className="w-3 h-3 bg-[#4899EB] rounded-full shadow-[0_0_8px_3px_rgba(72,153,235,0.7)] ml-2 relative top-[-5px]"></span>
            نظرات کاربران
          </p>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">کاربران ما چه می‌گویند؟</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            تجربه کاربران ما از استفاده از پلتفرم اکسچنجیم را بخوانید و با اطمینان بیشتری به ما بپیوندید.
          </p>
        </motion.div>
        
        <div className="max-w-5xl mx-auto">
          <div className="relative">
            {/* Testimonial cards */}
            <div className="overflow-hidden">
              <div className="flex transition-all duration-500" style={{ transform: `translateX(${activeIndex * 100}%)` }}>
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={testimonial.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: activeIndex === index ? 1 : 0.5, scale: activeIndex === index ? 1 : 0.9 }}
                    transition={{ duration: 0.5 }}
                    className="w-full flex-shrink-0 px-4"
                  >
                    <div className="bg-gradient-to-b from-[#1A1E2D] to-[#131725] p-8 rounded-2xl border border-gray-800 relative">
                      {/* Quote icon */}
                      <div className="absolute top-6 right-6 text-blue-400/20 text-4xl">
                        <FaQuoteRight />
                      </div>
                      
                      {/* Rating */}
                      <div className="flex mb-6">
                        {[...Array(5)].map((_, i) => (
                          <FaStar 
                            key={i} 
                            className={`${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-600'} mr-1`} 
                          />
                        ))}
                      </div>
                      
                      {/* Testimonial text */}
                      <p className="text-gray-300 mb-8 text-lg leading-relaxed">
                        {testimonial.text}
                      </p>
                      
                      {/* User info */}
                      <div className="flex items-center">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-blue-400 ml-4">
                          <img 
                            src={testimonial.avatar || "https://via.placeholder.com/48"} 
                            alt={testimonial.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <h4 className="font-bold text-white">{testimonial.name}</h4>
                          <p className="text-gray-400 text-sm">{testimonial.role}</p>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
            
            {/* Navigation dots */}
            <div className="flex justify-center mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`w-3 h-3 rounded-full mx-2 transition-all duration-300 ${
                    activeIndex === index 
                      ? 'bg-blue-500 shadow-[0_0_8px_rgba(72,153,235,0.7)]' 
                      : 'bg-gray-600'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
