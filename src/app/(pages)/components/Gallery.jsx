"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { useState } from "react";
import { FiChevronDown, FiChevronUp } from "react-icons/fi";

const Gallery = () => {
  const [hoveredCard, setHoveredCard] = useState(null);
  const [expandedCards, setExpandedCards] = useState({});

  // Toggle expanded state for a card
  const toggleCardExpansion = (id) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const galleryItems = [
    {
      id: 1,
      image: "/images/id1.png",
      description: "دروازه ای برای معاملات امن و آسان",
      paragraph:
        "یک پلتفرم تخصصی که تجارت ارزهای دیجیتال را ساده می کند. چه مبتدی باشید و چه حرفه ای، داده های بازار را در زمان واقعی کاوش کنید، معاملات نقطه ای و مدیریت دارایی ایمن را بررسی کنید.",
    },
    {
      id: 2,
      image: "/images/id2.png",
      description: "واریز و برداشت فوری",
      paragraph:
        "با کیف پول های اختصاصی هر کاربر، واریز و برداشت سریع را تجربه کنید. وجوه خود را بدون زحمت مدیریت کنید و در هر زمان به تاریخچه تراکنش خود دسترسی داشته باشید.",
    },
    {
      id: 3,
      image: "/images/id3.png",
      description: "پنل کاربری شخصی سازی شده برای شما",
      paragraph:
        "دارایی‌ها، سفارشات و تمامی عملکردهای خودر را پیگیری کنید، همه از یک داشبورد قابل تنظیم که برای رفع نیازهای منحصر به فرد شما طراحی شده است.",
    },
    {
      id: 4,
      image: "/images/id4.png",
      description: "بالا تر از ابرها پرواز کنید",
      paragraph:
        "چارت بازارها همواره به صورت لحظه ای در دسترس شماست. نمودارها را تجزیه و تحلیل کنید، قیمت ها را ردیابی کنید و با اطمینان تصمیمات آگاهانه بگیرید.",
    },
    {
      id: 5,
      image: "/images/id5.png",
      description: "ترندهای روز و اخبار رمز ارزها",
      paragraph:
        "با جدیدترین اخبار، بینش و روندهای رمز ارزها در وبلاگ ما به روز باشید. از تجزیه و تحلیل های متخصص بیاموزید و با جامعه ای از علاقه مندان به رمزنگاری تعامل داشته باشید.",
    },
    {
      id: 6,
      image: "/images/id6.png",
      description: "پشتیبانی حرفه ای، هر زمان",
      paragraph:
        "سوالی دارید یا نیاز به کمک دارید؟ تیم پشتیبانی اختصاصی ما همواره در دسترس است تا شما را در تمام مراحل سفر معاملاتی خود راهنمایی کند",
    },
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    },
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="w-full max-w-[1200px] px-6 mx-auto flex flex-col gap-10 justify-center items-center mb-24 relative">
      {/* Decorative background elements */}
      <div className="absolute top-20 right-0 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl -z-10" />
      <div className="absolute bottom-20 left-0 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl -z-10" />

      {/* Header */}
      <motion.div
        className="flex flex-col items-center gap-3 text-white w-full relative"
        initial="hidden"
        animate="visible"
        variants={headerVariants}
      >
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full blur-3xl opacity-10 bg-gradient-to-r from-blue-500 to-purple-500 -z-10" />

        <motion.h4
          className="text-[18px] md:text-[20px] font-medium leading-[36px] md:leading-[56px] text-center mb-[-10px] relative rtl"
          style={{ direction: 'rtl' }}
          whileHover={{ scale: 1.05 }}
        >
          چرا <span className="text-[#3772FF] relative">
            اکسچنجیم؟
            <motion.span
              className="absolute bottom-0 right-0 w-full h-[2px] bg-[#3772FF]"
              initial={{ width: 0 }}
              animate={{ width: "100%" }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </span>
        </motion.h4>

        <motion.h2
          className="text-[36px] md:text-[52px] leading-[44px] md:leading-[60px] text-center text-white font-bold relative rtl"
          style={{ direction: 'rtl' }}
          whileHover={{ scale: 1.02 }}
        >
          ویژگی های کلیدی <span className="text-[#3772FF] relative">
            اکسچنجیم
            <motion.div
              className="absolute -bottom-2 right-0 w-full h-[3px]"
              style={{
                background: "linear-gradient(90deg, rgba(55,114,255,0) 0%, rgba(55,114,255,1) 50%, rgba(55,114,255,0) 100%)",
              }}
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: "100%", opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            />
          </span>
        </motion.h2>

        {/* Decorative line */}
        <motion.div
          className="w-24 h-1 bg-gradient-to-r from-blue-500/20 via-blue-500 to-blue-500/20 rounded-full mt-2"
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: "6rem", opacity: 1 }}
          transition={{ duration: 0.8, delay: 1 }}
        />
      </motion.div>

      {/* Gallery */}
      <motion.div
        className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {galleryItems.map((item) => (
          <motion.div
            key={item.id}
            variants={itemVariants}
            onMouseEnter={() => setHoveredCard(item.id)}
            onMouseLeave={() => setHoveredCard(null)}
            className={`w-full max-w-[350px] ${expandedCards[item.id] ? 'h-[360px]' : 'h-[320px]'} rounded-[24px] relative overflow-hidden text-center
                      transition-all duration-500 bg-gradient-to-br from-[#23262F] to-[#1C1E24]
                      border border-gray-800/50 group`}
            whileHover={{
              y: -8,
              boxShadow: "0px 15px 30px rgba(0, 0, 0, 0.3), 0px 0px 20px rgba(70,111,247,0.5)",
              borderColor: "rgba(70,111,247,0.3)"
            }}
          >
            {/* Top gradient line */}
            <div
              className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_0px_15px_5px_rgba(72,153,235,0.5)]"
              style={{
                background:
                  "linear-gradient(90deg,rgba(211, 211, 211, 0.01) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.01) 100%)",
              }}
            />

            {/* Animated glow effect */}
            <motion.div
              className="absolute top-0 left-0 right-0 h-full w-20 bg-gradient-to-r from-transparent via-white/10 to-transparent"
              animate={{ x: ['-100%', '400%'] }}
              transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
            />

            {/* Background glow */}
            <div className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full blur-3xl opacity-20
                           bg-gradient-to-r from-blue-500 to-purple-500 transition-opacity duration-300
                           ${hoveredCard === item.id ? 'opacity-40' : 'opacity-10'}`} />

            {/* Corner decorative element */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-bl-full" />

            {/* Image container with enhanced styling */}
            <div className="w-full h-[160px] flex justify-center items-center pt-4 relative">
              <motion.div
                initial={{ scale: 1 }}
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ duration: 0.3 }}
                className="relative"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur-sm" />
                <Image
                  src={item.image}
                  alt={item.description}
                  width={160}
                  height={110}
                  className="object-contain rounded-lg shadow-lg relative z-10"
                />
              </motion.div>
            </div>

            {/* Content container with enhanced styling */}
            <div className="w-full absolute bottom-0 left-0 p-4 bg-gradient-to-t from-[rgba(24,25,29,0.95)] to-[rgba(24,25,29,0.5)] backdrop-blur-sm rounded-t-2xl">
              <motion.p
                className="font-bold text-[17px] md:text-[19px] leading-[24px] text-white mb-2 rtl"
                style={{ direction: 'rtl', textAlign: 'right' }}
                initial={{ opacity: 0.9 }}
                whileHover={{ scale: 1.02 }}
              >
                {item.description}
              </motion.p>
              <div className="relative">
                <motion.p
                  className="text-[13px] md:text-[14px] leading-[20px] text-center text-blue-400 opacity-90 overflow-hidden transition-all duration-300 rtl"
                  style={{
                    maxHeight: expandedCards[item.id] ? '200px' : '40px',
                    WebkitMaskImage: !expandedCards[item.id] ? 'linear-gradient(to bottom, black 60%, transparent 100%)' : 'none',
                    direction: 'rtl',
                    textAlign: 'right'
                  }}
                  initial={{ opacity: 0.7 }}
                  animate={{ opacity: expandedCards[item.id] ? 1 : 0.7 }}
                >
                  {item.paragraph}
                </motion.p>

                {/* View More / View Less Button */}
                <motion.button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleCardExpansion(item.id);
                  }}
                  className="mt-1 flex flex-row-reverse items-center justify-center mx-auto text-[12px] text-blue-500 hover:text-blue-400 transition-colors duration-300 bg-transparent border-none cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {expandedCards[item.id] ? (
                    <>
                      <span>نمایش کمتر</span>
                      <FiChevronUp className="ml-1" />
                    </>
                  ) : (
                    <>
                      <span>مشاهده بیشتر</span>
                      <FiChevronDown className="ml-1" />
                    </>
                  )}
                </motion.button>
              </div>
            </div>

            {/* Bottom decorative line */}
            <div
              className="absolute bottom-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px]"
              style={{
                background:
                  "linear-gradient(90deg,rgba(211, 211, 211, 0.01) 0%, rgba(72,153,235,0.5) 50%, rgba(211, 211, 211, 0.01) 100%)",
              }}
            />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default Gallery;
