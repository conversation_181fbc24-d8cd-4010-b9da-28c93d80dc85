import React from 'react';
import Link from 'next/link';
import { FaInstagram, FaLinkedin } from 'react-icons/fa';

const Footer = () => {
  const footerLinks = [
    {
      title: 'منو',
      links: ['خرید و فروش رمز ارز', 'ماشین حساب تبدیل', 'عضویت']
    },
    {
      title: 'اکسچنجیم',
      links: [
        {name:'باگ بانتی', href:'/privacy' },
        {name:'اپلیکیشن اندروید', href:'/userAgreement' },
        {name:'قیمت لحظه‌ای ارزها',href:'#'}
      ]
    },
    {
      title: 'راهنما',
      links: [
        {name:'راهنمای کلی', href:'#' },
        {name:'سوالات متداول کاربران', href:'#' },
        {name:'امنیت دارایی و حساب کاربری', href:'#' }
      ]
    },
    {
      title: 'پشتیبانی',
      links: [
        { name: 'تماس با ما',  href: '/support'},
        { name: 'تیکت', href: '/auth/login'},
        { name: 'قوانین و سیاست‌ها',  href: '/faq' }
      ]
    },
    {
      title: 'وبلاگ',
      links: [
        { name: 'اخبار', href: '/blog' },
        { name: 'تحلیل', href: '/blog' },
        { name: 'پادکست', href: '/blog' }
      ]
    },
  ];

  return (
    <div className="w-full bg-[#18191D] border-t border-gray-800 mt-24">
      <footer className="container mx-auto px-4 py-16">
        <div className="flex flex-col md:flex-row">
          
          {/* Logo and Social Icons Section */}
          <div className="hidden md:flex md:w-[40%] flex-col items-center">
            <h2 className="text-6xl text-center mb-4 font-bold text-white">   <img src="/images/main-logo.png" alt="Logo" className="h-12 w-40" />  </h2>
            <div className="flex gap-4">
              <Link href="#" className="text-gray-400 hover:text-white">
                <FaInstagram size={24} />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <FaLinkedin size={24} />
              </Link>
            </div>
          </div>

          {/* Links Section */}
          <div className="w-full md:w-[80%] grid grid-cols-2 md:grid-cols-5 gap-8">
            {footerLinks.map((column, index) => (
              <div key={index} className="flex flex-col">
                <h3 className="text-lg font-bold mb-6 text-white text-center md:text-start relative overflow-hidden  px-5 py-2">
                  {column.title}
           
                </h3>
                <ul className="space-y-4">
                  {column.links.map((link, linkIndex) => (
                    <li key={linkIndex} className="text-center md:text-start">
                      <Link 
                        href={typeof link === 'string' ? '#' : link.href} 
                        className="text-gray-400 hover:text-white text-sm"
                      >
                        {typeof link === 'string' ? link : link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-16">
          <div className="border-t border-gray-800 mx-8"></div>
          <p className="text-center text-gray-400 text-sm mt-8">
            © تمامی حقوق این وب‌سایت متعلق به صرافی ارز دیجیتال اکسچنجیم می‌باشد
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Footer;