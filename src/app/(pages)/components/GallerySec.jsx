import Image from "next/image";
import styles from '@/app/styles/rtl.module.css';

export default function SecuritySection() {
  const features = [
    {
      icon: '/images/id7.png',
      title: "!صرافی تحسین شده",
      description: "امنیت و سرعت در اکسچنجیم به رای کاربران تحسین کاربران تبدیل شده است",
    },
    {
      icon: "/images/id8.png",
      title: "امنیت بالای حساب کاربران",
      description: "قابلیت تایید با رمزها تضمین‌گر دارایی‌های شماست",
    },
    {
      icon: "/images/id9.png",
      title: "!کارمزد کم و گاهی رایگان",
      description: "با تکنولوژی ویژه ما، تراکنش‌های پرسرعت را بدون کارمزد انجام دهید",
    },
    {
      icon: "/images/id10.png",
      title: "خزانه امن برای دارایی‌های شما",
      description: "دارایی شما در کیف پول‌های سخت‌افزاری ذخیره می‌شود",
    },
  ];

  return (
    <section className={`py-16 px-6 md:px-20 ${styles.rtlSection}`} dir="rtl">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center text-white">
        <div className="w-full md:w-1/2 grid grid-cols-1 md:grid-cols-2 gap-6 mt-12 md:mt-0 md:pr-8">
          {features.map((feature, index) => (
            <div key={index} className={`relative rounded-2xl shadow-lg flex flex-col overflow-hidden bg-[url('/images/grid-pattern.png')] bg-cover bg-center border border-gray-700 ${styles.rtlCard}`}>
              <div className="absolute top-0 right-1/2 translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_100px_25px_rgba(72,153,235,0.7)]" style={{ background: 'linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)' }} />

              <div className="p-6 flex justify-end">
                <Image src={feature.icon} alt={feature.title} width={146} height={129} unoptimized />
              </div>

              <div className="bg-[rgba(24,25,29,0.10)] p-4 w-full">
                <h3 className="text-lg text-white font-bold bg-transparent text-right">{feature.title}</h3>
                <p className="text-sm text-blue mt-2 bg-transparent text-right">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="md:w-1/2 md:text-right md:pl-8">
          <h2 className="text-3xl md:text-4xl text-white font-bold leading-relaxed text-right">
            کمترین کارمزد و بالاترین <br /> امنیت در میان رقبا
          </h2>
          <p className="text-sm mt-4 text-right text-blue">
            در اکسچنجیم کمترین کارمزد برای معاملات پرداخت کنید و
            <br /> تراکنش‌های خود را در بالاترین سطح امنیت بلاکچین انجام دهید.
          </p>
        </div>
      </div>
    </section>
  );
}