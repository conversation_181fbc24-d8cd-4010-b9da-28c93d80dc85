import Image from "next/image";

export default function ExchangeApp() {
  return (
    <div className="text-white font-sans relative py-10 md:py-20">
      <div className="container mx-auto flex flex-col md:flex-row items-center justify-center text-center md:text-right px-4 md:px-20 my-6 md:my-12">
        {/* Mobile and QR Code Section */}
        <div className="relative">
          {/* Coming Soon Badge */}
          <div className="absolute right-56 translate-x-[57%] md:right-64 md:translate-x-0 bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-md flex text-center space-x-2 w-[120px] shadow-[4px_4px_8px_2px_rgba(192,192,192,0.5)]">
            <span className="text-white text-xs font-bold bg-transparent">
              به زودی
            </span>
          </div>

          {/* Mobile Image */}
          <Image
            src="/images/mobile.png"
            alt="اکسچنجیم"
            width={350}
            height={700}
            className="w-[200px] sm:w-[250px] md:w-[350px]"
            priority
          />
        </div>

        {/* Text and Buttons Section */}
        <div className="max-w-lg text-center md:text-right mt-8 md:mt-0 md:ml-24">
          <h2 className="text-xl sm:text-2xl md:text-3xl font-bold leading-relaxed text-center">
            اپلیکیشن اکسچنجیم <br /> به زودی
          </h2>
          <p className="text-sm opacity-75 mt-4 text-center">
            برای شما که به دنبال یک صرافی امن هستید. اپلیکیشن ما در حال توسعه است
          </p>

          {/* Coming Soon Message */}
          <div className="flex justify-center md:justify-end mt-6">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-3 rounded-lg text-lg font-bold">
              به زودی در گوگل پلی و اپ استور
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
