"use client";

import { motion } from 'framer-motion';
import { useState } from 'react';
import { FaUserPlus, FaWallet, FaExchangeAlt, FaChartLine } from 'react-icons/fa';
import styles from '@/app/styles/rtl.module.css';

const HowItWorks = () => {
  const [hoveredStep, setHoveredStep] = useState(null);

  const steps = [
    {
      icon: <FaUserPlus className="text-4xl md:text-5xl text-blue-400" />,
      title: "ثبت‌نام و احراز هویت",
      description: "در چند دقیقه ثبت‌نام کنید و با احراز هویت سریع، حساب خود را فعال کنید.",
      color: "from-blue-600/20 to-blue-400/5"
    },
    {
      icon: <FaWallet className="text-4xl md:text-5xl text-purple-400" />,
      title: "شارژ کیف پول",
      description: "کیف پول خود را با روش‌های متنوع پرداخت به راحتی شارژ کنید.",
      color: "from-purple-600/20 to-purple-400/5"
    },
    {
      icon: <FaExchangeAlt className="text-4xl md:text-5xl text-cyan-400" />,
      title: "خرید و فروش ارز دیجیتال",
      description: "به سادگی ارزهای دیجیتال مورد نظر خود را خرید و فروش کنید.",
      color: "from-cyan-600/20 to-cyan-400/5"
    },
    {
      icon: <FaChartLine className="text-4xl md:text-5xl text-green-400" />,
      title: "رشد سرمایه",
      description: "با ابزارهای پیشرفته تحلیل، سرمایه خود را مدیریت و رشد دهید.",
      color: "from-green-600/20 to-green-400/5"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section className={`py-20 relative overflow-hidden ${styles.rtlSection}`} dir="rtl">
      {/* Background glow effect */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[80%] h-[500px] bg-blue-500/5 rounded-full blur-[100px] pointer-events-none"></div>

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <p className="text-center mb-4 flex items-center justify-center">
            <span className="w-3 h-3 bg-[#4899EB] rounded-full shadow-[0_0_8px_3px_rgba(72,153,235,0.7)] ml-2 relative top-[-5px]"></span>
            مراحل ساده
          </p>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">چگونه شروع کنیم؟</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            در چهار مرحله ساده، وارد دنیای ارزهای دیجیتال شوید و از مزایای آن بهره‌مند شوید.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {steps.map((step, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              onMouseEnter={() => setHoveredStep(index)}
              onMouseLeave={() => setHoveredStep(null)}
              className="relative"
            >
              <div className={`h-full rounded-2xl border border-gray-800 p-6 flex flex-col items-center text-center transition-all duration-300 ${hoveredStep === index ? 'border-blue-500/50 shadow-[0_0_20px_rgba(72,153,235,0.3)]' : ''}`}>
                {/* Step number */}
                <div className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </div>

                {/* Icon with glow */}
                <div className={`w-20 h-20 rounded-full flex items-center justify-center mb-6 relative bg-gradient-to-b ${step.color}`}>
                  <div className="absolute inset-0 rounded-full blur-md bg-gradient-to-b from-blue-500/20 to-transparent opacity-70"></div>
                  {step.icon}
                </div>

                <h3 className="text-xl font-bold text-white mb-3">{step.title}</h3>
                <p className="text-gray-400">{step.description}</p>
              </div>

              {/* Connection line between steps */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-blue-500/50 to-transparent"></div>
              )}
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <a href="/auth/login" className="inline-block px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300">
            همین حالا شروع کنید
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default HowItWorks;
