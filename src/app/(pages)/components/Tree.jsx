import React from "react";

const AdventureSection = () => {
  return (
    <div
      className="relative sm:w-[1224px] w-[360px] sm:h-[336px] h-[500px] 
      border border-gray-800
      bg-[#18191D]/35 rounded-2xl flex sm:flex-row flex-col items-center justify-between px-8 mx-auto my-[168px]"
    >
      <div
        className="absolute top-1/2 left-0 -translate-y-1/2 w-[2px] h-[70%] shadow-[60px_0px_80px_10px_rgba(72,153,235,0.7)]"
        style={{
          background:
            "linear-gradient(180deg, rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
        }}
      ></div>

      <div className="sm:w-1/3 w-full bg-transparent sm:mt-0 mt-8">
        <img
          src="/images/tree.png"
          alt="Tree"
          className="w-full sm:w-[750px] sm:h-[413px] h-[200px] sm:mb-28 mb-2"
        />
      </div>

      <div className="text-center sm:flex-1 w-full bg-transparent sm:mb-0 mb-8">
        <div className="flex justify-center items-center text-white sm:mb-2 mb-1 bg-transparent">
          <p className="text-center flex items-center justify-center bg-transparent">
            <span className="w-3 h-3 bg-[#4899EB] rounded-full shadow-[0_0_8px_3px_rgba(72,153,235,0.7)] mr-2 relative top-[-5px]"></span>
            همین حالا شروع کنید
          </p>
        </div>

        <h2 className="text-white sm:text-[40px] text-2xl font-bold font-abhayaLibre leading-snug bg-transparent sm:mb-4 mb-2">
          ،اکسچنجیم در این سفر پرماجرا <br /> همسفر شماست
        </h2>

        <p className="text-white text-sm sm:mt-2 mt-1 bg-transparent">
          اکسچنجیم در این سفر پرماجرا، همسفر شماست
        </p>

        <button className="sm:mt-4 mt-2 w-[250px] h-[48px] bg-blue text-white rounded-lg">
          بزن بریم
        </button>
      </div>
    </div>
  );
};

export default AdventureSection;
