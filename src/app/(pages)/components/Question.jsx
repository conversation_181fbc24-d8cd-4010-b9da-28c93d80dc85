'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const faqs = [
  { question: 'چطور به صرافی اکسچنجیم اعتماد کنم؟', answer: 'برای اعتماد به صرافی، بهتر است نظرات کاربران و مجوزهای مربوطه را بررسی کنید.' },
  { question: 'چگونه از صرافی اکسچنجیم رمزارز خریداری کنم؟', answer: 'می‌توانید با ثبت‌نام و احراز هویت، خرید رمزارز را آغاز کنید.' },
  { question: 'چطور رمز ارزهای خود را در اکسچنجیم به ریال تبدیل کنم؟', answer: 'با فروش رمزارزها در بازار صرافی، معادل ریالی آن را دریافت کنید.' },
  { question: 'قوانین معامله رمزارزها در ایران به چه صورت است؟', answer: 'قوانین معامله رمزارزها در ایران به‌طور مداوم در حال تغییر است، پیشنهاد می‌شود از مراجع قانونی اطلاعات کسب کنید.' },
  { question: 'چطور رمز ارزهای خود را به‌صورت کامل نگهداری کنم؟', answer: 'برای نگهداری امن، از کیف پول‌های سخت‌افزاری و روش‌های امنیتی مانند 2FA استفاده کنید.' },
];

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="max-w-5xl w-full mx-auto p-6 text-white rounded-xl">
   <p className="text-center mb-8 flex items-center justify-center">
      <span className="w-3 h-3 bg-[#4899EB] rounded-full shadow-[0_0_8px_3px_rgba(72,153,235,0.7)] mr-2 relative top-[-8px]"></span>
      سیاست و قوانین
    </p>
      <h2 className="text-6xl text-center mb-4">!سوالات پرتکرار شما</h2>
      
        <p className="text-center text-2xl text-blue mb-10 max-w-5xl w-full mx-auto">
          در ادامه به برخی سوالات پرتکرار کاربران محترم پاسخ داده‌ایم
        </p>
      
      <div className="space-y-4 w-full max-w-4xl mx-auto">
        {faqs.map((faq, index) => (
          <div key={index} className="border border-gray-700 bg-[#18191D] rounded-lg overflow-hidden relative w-full max-w-4xl mx-auto">
            {/* لاینر آبی روی بردر بالای سوال */}
            {openIndex === index && (
              <div
                className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_0px_50px_10px_rgba(72,153,235,0.7)]"
                style={{
                  background: 'linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)',
                }}
              ></div>
            )}

            {/* سوال */}
            <button
              className={`w-full flex flex-row-reverse items-center justify-between p-4 text-right focus:outline-none ${
                openIndex === index ? 'text-white' : 'text-blue'
              }`}
              onClick={() => toggleFAQ(index)}
            >
              {/* متن سوال راست‌چین دقیقاً بعد از آیکون */}
              <div className="flex text-lg items-center space-x-4 rtl:space-x-reverse w-full bg-transparent">
                <span className="w-full bg-transparent">{faq.question}</span>
                {openIndex === index ? (
                  <ChevronUp className="border border-white p-1 rounded-full text-white" />
                ) : (
                  <ChevronDown className="border border-blue p-1 rounded-full text-blue" />
                )}
              </div>
            </button>

            {/* پاسخ */}
            {openIndex === index && (
              <>
                <div className="border-t border-gray-800 mx-4"></div>
                <div className="p-4 text-blue text-right">{faq.answer}</div>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
