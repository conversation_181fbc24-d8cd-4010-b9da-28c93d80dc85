'use client';

import Footer from "../components/Footer";
import Navbar from "../components/Navbar";
import { FaCalendarAlt, FaHeart, FaCommentDots, FaShareAlt, FaEye } from "react-icons/fa";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import BlogSeo from "./components/BlogSeo";
import BlogSeoContent from "./components/BlogSeoContent";

const getCommentsCount = (blogId: number) => {
  if (typeof window !== 'undefined') {
    const comments = JSON.parse(localStorage.getItem(`comments-${blogId}`) || '[]');
    return comments.length;
  }
  return 0;
}

interface BlogCard {
  id: number;
  title: string;
  date: string;
  image: string;
  description: string;
  category: string;
  views: number;
  likes: number;
  comments: number;
}

export default function BlogPage() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [showAll, setShowAll] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [shareUrl, setShareUrl] = useState("");

  const [cardsData, setCardsData] = useState<BlogCard[]>([
    { id: 1, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image: "/images/blog1.png", description: "The Bitcoin price is trading near its highest levels", category: "Announcements#", views: 0, likes: 0, comments: 0 },
    { id: 2, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image: "/images/blog2.png", description: "The Bitcoin price is trading near its highest levels", category: "#Partnerships", views: 0, likes: 0, comments: 0 },
    { id: 3, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image: "/images/blog3.png", description: "The Bitcoin price is trading near its highest levels", category: "#Announcements", views: 0, likes: 0, comments: 0 },
    { id: 4, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image: "/images/blog4.png", description: "The Bitcoin price is trading near its highest levels", category: "#Announcements", views: 0, likes: 0, comments: 0 },
    { id: 5, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image: "/images/blog5.png", description: "The Bitcoin price is trading near its highest levels", category: "#Partnerships", views: 0, likes: 0, comments: 0 },
    { id: 6, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image:"/images/blog6.png", description: "The Bitcoin price is trading near its highest levels", category: "#Announcements", views: 0, likes: 0, comments: 0 },
    { id: 7, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image: "/images/blog3.png", description: "The Bitcoin price is trading near its highest levels", category: "#Announcements", views: 0, likes: 0, comments: 0 },
    { id: 8, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image:"/images/blog2.png", description: "The Bitcoin price is trading near its highest levels", category: "#Partnerships", views: 0, likes: 0, comments: 0 },
    { id: 9, title: "Why is Bitcoin price up today", date: "09 Sep, 2024", image: "/images/blog1.png", description: "The Bitcoin price is trading near its highest levels", category: "#Announcements", views: 0, likes: 0, comments: 0 },
  ]);

  // Load data from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem('cardsData');
      if (savedData) {
        setCardsData(JSON.parse(savedData));
      } else {
        // If no saved data, initialize localStorage with default data
        localStorage.setItem('cardsData', JSON.stringify(cardsData));
      }
    }
  }, []);

  // Update comments count for each card
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const updatedCards = cardsData.map(card => ({
        ...card,
        comments: getCommentsCount(card.id)
      }));
      setCardsData(updatedCards);
    }
  }, []);

  const saveToLocalStorage = (newData: BlogCard[]) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('cardsData', JSON.stringify(newData));
      setCardsData(newData);
    }
  };

  const handleShare = (e: React.MouseEvent, id: number) => {
    e.stopPropagation();
    if (typeof window !== 'undefined') {
      setShareUrl(`${window.location.origin}/blog/${id}`);
      setShowShareModal(true);
    }
  };

  const handleLike = (e: React.MouseEvent, id: number) => {
    e.stopPropagation();
    const newData = cardsData.map(card =>
      card.id === id ? { ...card, likes: card.likes + 1 } : card
    );
    saveToLocalStorage(newData);
  };

  const handleView = (id: number) => {
    const newData = cardsData.map(card =>
      card.id === id ? { ...card, views: card.views + 1 } : card
    );
    saveToLocalStorage(newData);
  };

  const handleCardClick = (id: number) => {
    handleView(id);
    router.push(`/blog/${id}`);
  };

  const filteredCards = cardsData;
  const cardsPerPage = 6;
  const totalPages = Math.ceil(filteredCards.length / cardsPerPage);
  const currentCards = showAll ? filteredCards : filteredCards.slice((currentPage - 1) * cardsPerPage, currentPage * cardsPerPage);

  return (
    <div className="bg-[#18191D] text-white min-h-screen">
      {/* SEO Structured Data */}
      <BlogSeo />

      <div className="flex justify-center w-full bg-transparent fixed top-0 left-0 right-0 z-50">
        <div className="w-full max-w-[1100px]">
          <Navbar
            className="py-4 border-b border-gray-800 backdrop-blur-sm bg-transparent"
            hideSearch
            hideSignUp
          />
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <Swiper
          spaceBetween={30}
          slidesPerView={1}
          className="w-full lg:w-[1100px] mt-20"
        >
          <SwiperSlide>
            <img src="/images/bitcoin.png" alt="آخرین اخبار بیت کوین" className="w-full h-[250px] md:h-[400px] object-cover rounded" />
          </SwiperSlide>
          <SwiperSlide>
            <img src="/images/bitcoin.png" alt="تحلیل قیمت ارزهای دیجیتال" className="w-full h-[250px] md:h-[400px] object-cover rounded" />
          </SwiperSlide>
          <SwiperSlide>
            <img src="/images/bitcoin.png" alt="آموزش ارزهای دیجیتال" className="w-full h-[250px] md:h-[400px] object-cover rounded" />
          </SwiperSlide>
        </Swiper>
      </div>

      <div className="container mx-auto px-4 py-10 flex flex-col items-center">
        <div className="flex justify-between w-full lg:w-[1100px] items-center">
          <h1 className="text-3xl font-bold text-left">مقالات اخیر</h1>
          <button onClick={() => setShowAll(!showAll)} className="text-right text-black bg-white rounded-lg py-2 px-4 text-sm">
            {showAll ? "مقالات صفحه‌بندی شده" : "همه مقالات"}
          </button>
        </div>

        <div className="mt-6 grid grid-cols-1 place-items-center lg:grid-cols-3 gap-4 justify-center items-center w-full lg:w-[1100px]">
          {currentCards.map((card) => (
            <div
              key={card.id}
              onClick={() => handleCardClick(card.id)}
              className="rounded-lg shadow-lg relative cursor-pointer"
            >
              <div className="p-0 shadow-lg relative w-full h-[550px] flex flex-col border border-gray-700 rounded-lg hover:transform hover:scale-105 hover:shadow-2xl transition-all duration-300">
                <div
                  className="relative w-full h-[50%] bg-cover bg-center rounded-t-lg cursor-pointer"
                  style={{ backgroundImage: `url(${card.image})` }}
                  aria-label={card.title}
                />

                <div className="flex flex-col justify-between h-[50%] p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-400 text-sm text-left">
                      <FaCalendarAlt className="mr-2 inline" /> {card.date}
                    </span>
                    <span className="text-white rounded p-1 text-sm">{card.category}</span>
                  </div>

                  <h2 className="text-xl font-semibold mb-2 text-left">{card.title || "Untitled"}</h2>
                  <p className="text-gray-400 mb-4 text-sm text-left">{card.description}</p>

                  <div className="mt-4 flex justify-between items-center text-gray-400">
                    <button
                      className="border border-gray-700 p-2 rounded-lg flex items-center justify-center"
                      onClick={(e) => handleShare(e, card.id)}
                      aria-label="اشتراک گذاری"
                    >
                      <FaShareAlt size={16} />
                    </button>
                    <div className="flex items-center gap-6">
                      <div className="flex items-center border border-gray-700 p-2 rounded-lg">
                        <FaEye size={16} className="ml-3" />
                        <span>{card.views}</span>
                      </div>
                      <div className="flex items-center border border-gray-700 p-2 rounded-lg">
                        <FaHeart
                          size={16}
                          className={`ml-3 cursor-pointer ${card.likes > 0 ? 'text-red-500' : ''}`}
                          onClick={(e) => handleLike(e, card.id)}
                          aria-label="لایک"
                        />
                        <span>{card.likes}</span>
                      </div>
                      <div className="flex items-center border border-gray-700 p-2 rounded-lg">
                        <FaCommentDots size={16} className="ml-3 cursor-pointer" />
                        <span>{getCommentsCount(card.id)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {!showAll && (
          <div className="flex justify-center mt-4">
            <button
              onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}
              className="px-4 py-2 text-white rounded-l-lg"
              disabled={currentPage === 1}
              aria-label="صفحه قبلی"
            >
              &#60;
            </button>
            <div className="flex items-center">
              {[...Array(totalPages)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index + 1)}
                  className={`px-3 py-1 rounded-lg ${currentPage === index + 1 ? "bg-gray-500" : "bg-gray-700"} text-white mx-1`}
                  aria-label={`صفحه ${index + 1}`}
                >
                  {index + 1}
                </button>
              ))}
            </div>
            <button
              onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}
              className="px-5 py-2 text-white rounded-r-lg"
              disabled={currentPage === totalPages}
              aria-label="صفحه بعدی"
            >
              &#62;
            </button>
          </div>
        )}
      </div>

      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-bold mb-4 text-white text-center">اشتراک‌گذاری مقاله</h3>
            <div className="flex gap-2">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="border p-2 rounded bg-gray-700 text-white"
                aria-label="آدرس مقاله برای اشتراک گذاری"
              />
              <button
                onClick={() => {
                  navigator.clipboard.writeText(shareUrl);
                  setShowShareModal(false);
                }}
                className="bg-white text-black px-4 py-2 rounded hover:bg-blue hover:text-white"
                aria-label="کپی آدرس"
              >
                کپی
              </button>
            </div>
            <button
              onClick={() => setShowShareModal(false)}
              className="mt-4 text-white border border-gray-500 px-4 py-1 rounded hover:text-white"
              aria-label="بستن"
            >
              بستن
            </button>
          </div>
        </div>
      )}

      {/* SEO Content Section - Rich Text for Search Engines */}
      <BlogSeoContent />

      <Footer />
    </div>
  );
}
