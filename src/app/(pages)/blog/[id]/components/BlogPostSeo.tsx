'use client';

import JsonLd from '@/components/seo/JsonLd';
import { useParams } from 'next/navigation';

interface Article {
  title: string;
  date: string;
  image: string;
  content: string;
}

interface BlogPostSeoProps {
  article: Article;
}

export default function BlogPostSeo({ article }: BlogPostSeoProps) {
  const { id } = useParams();
  const postUrl = `https://exchangim.com/blog/${id}`;

  // Article schema
  const articleData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: article.title,
    image: [article.image],
    datePublished: article.date,
    dateModified: article.date,
    author: {
      '@type': 'Organization',
      name: 'اکسچنجیم',
      url: 'https://exchangim.com',
    },
    publisher: {
      '@type': 'Organization',
      name: 'اکسچنجیم',
      logo: {
        '@type': 'ImageObject',
        url: 'https://exchangim.com/images/main-logo.png',
      },
    },
    description: article.content.substring(0, 150) + '...',
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': postUrl,
    },
  };

  // BreadcrumbList schema
  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'صفحه اصلی',
        item: 'https://exchangim.com',
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'وبلاگ',
        item: 'https://exchangim.com/blog',
      },
      {
        '@type': 'ListItem',
        position: 3,
        name: article.title,
        item: postUrl,
      },
    ],
  };

  return (
    <>
      <JsonLd data={articleData} />
      <JsonLd data={breadcrumbData} />
    </>
  );
}
