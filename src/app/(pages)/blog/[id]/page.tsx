import { Metadata } from 'next';
import BlogPostPage from './BlogPostPage';

// Define the articles data
const articles = {
  1: {
    title: "Bitcoin Price Up",
    date: "09 Sep, 2024",
    image: "/images/blog1.png",
    content: "Bitcoin is rising because of new investors joining the market. Many analysts believe that Bitcoin will continue to grow in value due to its limited supply."
  },
  2: {
    title: "Ethereum News",
    date: "10 Sep, 2024",
    image: "/images/blog2.png",
    content: "Ethereum's new upgrade has brought major improvements in speed and scalability. Experts suggest that Ethereum 2.0 will revolutionize decentralized finance (DeFi)."
  },
  // Add more articles as needed
};

// Define the generateMetadata function
export async function generateMetadata(
  { params }: { params: Promise<{ id: string }> }
): Promise<Metadata> {
  const { id } = await params;
  const numId = parseInt(id);
  const article = articles[numId as keyof typeof articles];

  if (!article) {
    return {
      title: 'مقاله یافت نشد | اکسچنجیم',
      description: 'متاسفانه مقاله مورد نظر یافت نشد.',
    };
  }

  return {
    title: `${article.title} | وبلاگ اکسچنجیم`,
    description: article.content.substring(0, 160),
    openGraph: {
      title: article.title,
      description: article.content.substring(0, 160),
      url: `https://exchangim.com/blog/${id}`,
      siteName: 'Exchangim',
      images: [
        {
          url: article.image,
          width: 1200,
          height: 630,
          alt: article.title,
        },
      ],
      locale: 'fa_IR',
      type: 'article',
      publishedTime: article.date,
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.content.substring(0, 160),
      images: [article.image],
    },
    alternates: {
      canonical: `/blog/${id}`,
    },
  };
}

// Define the page component
export default async function BlogPost({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params;
  return <BlogPostPage id={id} />;
}
