'use client';

import { useState, useEffect } from 'react';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import { FaHeart, FaCommentDots, FaShareAlt } from 'react-icons/fa';
import BlogPostSeo from './components/BlogPostSeo';

interface BlogPostPageProps {
  id: string;
}

export default function BlogPostPage({ id }: BlogPostPageProps) {
  const [showComments, setShowComments] = useState(false);
  const [comments, setComments] = useState<string[]>([]);
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    const savedComments = JSON.parse(localStorage.getItem(`comments-${id}`) || '[]');
    setComments(savedComments);
  }, [id]);

  const handleAddComment = () => {
    if (newComment.trim() === '') return;
    const updatedComments = [...comments, newComment];
    setComments(updatedComments);
    localStorage.setItem(`comments-${id}`, JSON.stringify(updatedComments));
    setNewComment('');
  };

  // Define articles with numeric keys
  const articles: Record<number, {
    title: string;
    date: string;
    image: string;
    content: string;
  }> = {
    1: {
      title: 'Bitcoin Price Up',
      date: '09 Sep, 2024',
      image: '/images/blog1.png',
      content: `Bitcoin is rising because of new investors joining the market. Many analysts believe that Bitcoin will continue to grow in value due to its limited supply.`,
    },
    2: {
      title: 'Ethereum News',
      date: '10 Sep, 2024',
      image: '/images/blog2.png',
      content: `Ethereum's new upgrade has brought major improvements in speed and scalability. Experts suggest that Ethereum 2.0 will revolutionize decentralized finance (DeFi).`,
    },
    3: {
      title: 'Crypto Market Analysis',
      date: '11 Sep, 2024',
      image: '/images/blog3.png',
      content: `The cryptocurrency market has shown significant growth in recent months. Experts predict continued expansion as institutional investors enter the space.`,
    },
    4: {
      title: 'NFT Revolution',
      date: '12 Sep, 2024',
      image: '/images/blog4.png',
      content: `Non-fungible tokens are changing the art world. Artists are finding new ways to monetize their work through blockchain technology.`,
    },
    5: {
      title: 'DeFi Explained',
      date: '13 Sep, 2024',
      image: '/images/blog5.png',
      content: `Decentralized Finance is revolutionizing traditional banking. Learn how DeFi platforms are creating new financial opportunities without intermediaries.`,
    },
    6: {
      title: 'Blockchain Security',
      date: '14 Sep, 2024',
      image: '/images/blog6.png',
      content: `Blockchain technology offers unprecedented security features. Understand how cryptography and distributed ledgers protect digital assets.`,
    },
    7: {
      title: 'Mining Explained',
      date: '15 Sep, 2024',
      image: '/images/blog3.png',
      content: `Cryptocurrency mining is the process of validating transactions and adding them to the blockchain. This article explains the technical aspects of mining.`,
    },
    8: {
      title: 'Crypto Regulations',
      date: '16 Sep, 2024',
      image: '/images/blog2.png',
      content: `Governments worldwide are developing regulations for cryptocurrencies. Stay informed about the latest legal developments affecting digital assets.`,
    },
    9: {
      title: 'Litecoin Update',
      date: '17 Sep, 2024',
      image: '/images/blog1.png',
      content: `Litecoin has introduced enhanced security features, making transactions faster and safer. This is a significant step towards wider adoption in online payments.`,
    },
  };

  // Convert id to number for type safety
  const articleId = parseInt(id);
  const article = articles[articleId];

  if (!article) {
    return (
      <div className="text-center text-white mt-10 text-xl">
        ❌ مقاله‌ای پیدا نشد!
      </div>
    );
  }

  return (
    <div className="text-white min-h-[1500px] flex flex-col items-center bg-[#141416]">
      {/* SEO Structured Data */}
      <BlogPostSeo article={article} />
      
      <div className="w-full max-w-[1100px]">
        <Navbar
          className="py-4 border-b border-gray-800 backdrop-blur-sm bg-transparent"
          hideSearch
          hideSignUp
        />
      </div>

      <div className="container px-4 py-10 max-w-3xl text-left rounded-lg bg-[#18191D]/70 mt-10">
        <p className="text-gray-400 mb-4 bg-transparent">{article.date}</p>
        <h1 className="text-4xl font-bold mb-4 bg-transparent">{article.title}</h1>
        <img 
          src={article.image} 
          alt={article.title} 
          className="w-full h-[400] rounded-lg mb-6 shadow-lg" 
        />
        <article className="text-lg leading-relaxed mb-4 bg-transparent">
          {article.content}
        </article>

        <div className="flex justify-between items-center mt-8 mb-4">
          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-1 text-gray-400 hover:text-blue-500">
              <FaHeart />
              <span>Like</span>
            </button>
            <button
              onClick={() => setShowComments(!showComments)}
              className="flex items-center space-x-1 text-gray-400 hover:text-blue-500"
            >
              <FaCommentDots />
              <span>{comments.length} Comments</span>
            </button>
          </div>
          <button className="flex items-center space-x-1 text-gray-400 hover:text-blue-500">
            <FaShareAlt />
            <span>Share</span>
          </button>
        </div>

        {showComments && (
          <div className="mt-8">
            <h3 className="text-xl font-semibold mb-4">Comments</h3>
            <div className="space-y-4">
              {comments.map((comment, index) => (
                <div key={index} className="bg-[#1E1F25] p-4 rounded-lg">
                  <p className="text-gray-300">{comment}</p>
                </div>
              ))}
            </div>
            <div className="mt-6">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="w-full p-3 bg-[#1E1F25] rounded-lg text-white"
                rows={3}
              ></textarea>
              <button
                onClick={handleAddComment}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Post Comment
              </button>
            </div>
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
}
