import { Metadata } from 'next';
import BlogPage from './BlogPage';

export const metadata: Metadata = {
  title: 'وبلاگ اکسچنجیم | آخرین اخبار و مقالات ارزهای دیجیتال',
  description: 'آخرین اخبار، تحلیل‌ها و مقالات آموزشی در حوزه ارزهای دیجیتال و بلاکچین در وبلاگ اکسچنجیم',
  keywords: 'وبلاگ ارز دیجیتال, اخبار بیت کوین, آموزش ارز دیجیتال, تحلیل قیمت, اکسچنجیم',
  alternates: {
    canonical: '/blog',
  },
  openGraph: {
    title: 'وبلاگ اکسچنجیم | آخرین اخبار و مقالات ارزهای دیجیتال',
    description: 'آخرین اخبار، تحلیل‌ها و مقالات آموزشی در حوزه ارزهای دیجیتال و بلاکچین',
    url: 'https://exchangim.com/blog',
    siteName: 'Exchangim',
    images: [
      {
        url: '/images/blog-banner.png',
        width: 1200,
        height: 630,
        alt: 'وبلاگ اکسچنجیم',
      },
    ],
    locale: 'fa_IR',
    type: 'website',
  },
};

export default function Blog() {
  return <BlogPage />;
}
