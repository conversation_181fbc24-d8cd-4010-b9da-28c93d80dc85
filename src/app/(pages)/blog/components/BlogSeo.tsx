import JsonLd from '@/components/seo/JsonLd';

export default function BlogSeo() {
  // Blog schema
  const blogData = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'وبلاگ اکسچنجیم',
    description: 'آخرین اخبار و مقالات در حوزه ارزهای دیجیتال و بلاکچین',
    url: 'https://exchangim.com/blog',
    publisher: {
      '@type': 'Organization',
      name: 'اکسچنجیم',
      logo: {
        '@type': 'ImageObject',
        url: 'https://exchangim.com/images/main-logo.png',
      },
    },
  };

  // BreadcrumbList schema
  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'صفحه اصلی',
        item: 'https://exchangim.com',
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'وبلاگ',
        item: 'https://exchangim.com/blog',
      },
    ],
  };

  return (
    <>
      <JsonLd data={blogData} />
      <JsonLd data={breadcrumbData} />
    </>
  );
}
