'use client';

import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import { useState } from "react";
import JsonLd from '@/components/seo/JsonLd';
import FaqSeoContent from './FaqSeoContent';

// Add missing ChevronUp and ChevronDown components
const ChevronUp = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polyline points="18 15 12 9 6 15"></polyline>
  </svg>
);

const ChevronDown = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polyline points="6 9 12 15 18 9"></polyline>
  </svg>
);

export default function FaqPage() {
  // Add missing state for FAQ toggle
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  // Add missing faqs data
  const faqs = [
    {
      question: "چگونه می توانم یک حساب کاربری جدید در پلتفرم شما ایجاد کنم؟",
      answer: "برای ایجاد یک حساب کاربری جدید در پلتفرم ما، ابتدا به صفحه ثبت نام بروید. سپس اطلاعات مورد نیاز شامل ایمیل، نام کاربری و رمز عبور خود را وارد کنید. پس از آن برای تکمیل مراحل ثبت نام یک پیامک حاوی کد امنیتی به شما ارسال می شود که باید تایید کنید. علاوه بر این، بسته به الزامات امنیتی، احراز هویت نیز ممکن است ضروری باشد."
    },
    {
      question: "ایجاد حساب کاربری جدید چه شرایط و قوانینی دارد؟",
      answer: "برای ایجاد حساب کاربری در اکسچنجیم، باید حداقل 18 سال سن داشته باشید و مدارک هویتی معتبر ارائه دهید. همچنین باید قوانین و مقررات سایت را مطالعه کرده و بپذیرید. اطلاعات شخصی شما باید دقیق و صحیح باشد و مسئولیت حفظ امنیت حساب کاربری بر عهده شماست."
    },
    {
      question: "آیا واریز یا برداشت ریال از طریق حساب بانکی که به نام خودم نیست، امکان پذیر است؟",
      answer: "خیر، به دلیل قوانین مبارزه با پولشویی و الزامات امنیتی، واریز و برداشت ریال فقط از طریق حساب بانکی که به نام صاحب حساب کاربری در اکسچنجیم است، امکان‌پذیر می‌باشد. استفاده از حساب بانکی دیگران برای واریز یا برداشت مجاز نیست."
    },
    {
      question: "کارمزدها در اکسچنجیم چطور محاسبه می شود؟",
      answer: "کارمزد معاملات در اکسچنجیم بر اساس نوع ارز دیجیتال و حجم معامله متفاوت است. برای خرید و فروش ارزهای دیجیتال، کارمزد بین 0.1% تا 0.3% متغیر است. کاربران با حجم معاملات بالاتر از تخفیف در کارمزد بهره‌مند می‌شوند. برای اطلاعات دقیق‌تر به صفحه کارمزدها مراجعه کنید."
    },
    {
      question: "احراز هویت برای چیست؟ چطور انجام بدم؟ آیا اطلاعات من پیش شما محفوظ است و از آن سوء استفاده نمی شود؟",
      answer: "احراز هویت برای تأمین امنیت کاربران و جلوگیری از فعالیت‌های مجرمانه مانند پولشویی انجام می‌شود. برای احراز هویت، باید تصویر کارت ملی، سلفی با کارت ملی و در برخی موارد مدارک تکمیلی را ارسال کنید. اطلاعات شما کاملاً محرمانه و با استانداردهای امنیتی بالا نگهداری می‌شود و فقط برای اهداف قانونی مورد استفاده قرار می‌گیرد."
    },
    {
      question: "حداقل میزان خرید و فروش، واریز و برداشت در اکسچنجیم چقدر است؟",
      answer: "حداقل میزان خرید و فروش در اکسچنجیم 100,000 تومان است. برای واریز ریال حداقل مبلغ 50,000 تومان و برای برداشت ریال حداقل مبلغ 200,000 تومان تعیین شده است. حداقل میزان واریز و برداشت ارزهای دیجیتال بسته به نوع ارز متفاوت است و در صفحه هر ارز قابل مشاهده است."
    },
    {
      question: "خرید و فروش در سایت چه مقدار زمانبر است و آیا قوانین خاصی دارد؟",
      answer: "خرید و فروش در اکسچنجیم به صورت آنی انجام می‌شود و معمولاً کمتر از چند دقیقه طول می‌کشد. برای معاملات با حجم بالا ممکن است زمان بیشتری نیاز باشد. قوانین خاص شامل محدودیت‌های روزانه معاملات بر اساس سطح احراز هویت، ممنوعیت معاملات مشکوک و الزام به رعایت قوانین مبارزه با پولشویی است."
    },
    {
      question: "آیا من میتوانم برای دیگران از حساب کاربری خود خرید کنم؟",
      answer: "خیر، هر حساب کاربری در اکسچنجیم فقط برای استفاده شخصی صاحب حساب است و انجام معاملات برای دیگران با حساب شخصی مجاز نیست. این محدودیت برای رعایت قوانین مبارزه با پولشویی و حفظ امنیت کاربران اعمال شده است."
    },
  ];

  // FAQPage schema for structured data
  const faqPageSchema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  // BreadcrumbList schema
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'صفحه اصلی',
        item: 'https://exchangim.com'
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'سوالات متداول',
        item: 'https://exchangim.com/faq'
      }
    ]
  };

  return (
    <div className="bg-[url('/images/main-bg.jpg')] bg-cover bg-no-repeat min-h-screen text-white bg-fixed">
      {/* Structured Data */}
      <JsonLd data={faqPageSchema} />
      <JsonLd data={breadcrumbSchema} />

      <div className="flex justify-center w-full bg-transparent fixed top-0 left-0 right-0 z-50">
        <div className="w-full max-w-[1100px]">
          <Navbar
            className="py-4 border-b border-gray-800 backdrop-blur-sm bg-transparent"
            hideSearch
            hideSignUp
          />
        </div>
      </div>

      {/* Header */}
      <header className="text-center py-12 bg-transparent">
        <div className="flex justify-center items-center bg-transparent mt-10">
          <img
            src="/images/user-q.png"
            alt="سوالات متداول اکسچنجیم"
            className="w-[355px] sm:w-[250px] bg-transparent"
          />
        </div>
        <h1 className="text-4xl font-bold mt-5 bg-transparent">سوالات متداول کاربران</h1>

        <div className="relative mt-4 w-[90%] md:w-[450px] lg:w-[550px] mx-auto">
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 z-10">
            <img
              src="/images/search-icon.png"
              alt="جستجو"
              className="w-5 h-5 bg-transparent"
            />
          </div>
          <input
            type="text"
            placeholder="جستجو"
            className="p-2 pr-10 bg-[#23262F] rounded-md w-full h-[45px] text-right"
            aria-label="جستجو در سوالات متداول"
          />
        </div>
      </header>

      {/* آموزش - Modified to display in one line */}
      <section className="container py-8 bg-transparent px-15">
        <h2 className="text-[32px] text-center mb-6 bg-transparent font-bold">سوالات شامل سر موضوعات زیر می باشد</h2>
        <div className="flex flex-wrap justify-center bg-transparent mx-auto">
          {/* کارت‌های گالری - Modified to be in one line with smaller width and larger height */}
          {[
            { title: 'حساب کاربری', icon: '/images/id1.png' },
            { title: 'تراکنش ریال', icon: '/images/wallet.png' },
            { title: 'پشتیبانی', icon: '/images/id6.png' },
            { title: 'کارمزد ها', icon: '/images/coinchart.png' },
            { title: 'اطلاعات', icon: '/images/info.png' },
            { title: 'معاملات رمز ارز', icon: '/images/jar.png' }
          ].map((item, index) => (
            <div
              key={index}
              className="p-4 bg-[#23262F]/60 border border-gray-700 rounded-lg flex flex-col justify-center items-center relative h-[184px] w-[194px] mx-2 mb-4"
            >
              <div
                className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_50px_130px_25px_rgba(72,153,235,0.5)]"
                style={{
                  background: 'linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)',
                }}
              />
              <div className="flex flex-col justify-center items-center text-center bg-transparent">
                <img src={item.icon} alt={`آیکون ${item.title}`} className="w-32 h-22 mb-3" />
                <div className="mt-2 text-lg font-bold">{item.title}</div>
              </div>
            </div>
          ))}
        </div>
      </section>

      <div className="max-w-5xl w-full mx-auto p-6 text-white rounded-xl">
        <div className="space-y-4 w-full max-w-4xl mx-auto">
          {faqs.map((faq, index) => (
            <div key={index} className="border border-gray-700 bg-[#18191D] rounded-lg overflow-hidden relative w-full max-w-4xl mx-auto">
              {/* لاینر آبی روی بردر بالای سوال */}
              {openIndex === index && (
                <div
                  className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_0px_50px_10px_rgba(72,153,235,0.7)]"
                  style={{
                    background: 'linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)',
                  }}
                ></div>
              )}

              {/* سوال */}
              <button
                className="w-full flex items-center justify-between p-4 text-right focus:outline-none"
                onClick={() => toggleFAQ(index)}
                aria-expanded={openIndex === index}
                aria-controls={`faq-answer-${index}`}
              >
                {/* آیکون سمت راست */}
                {openIndex === index ? (
                  <ChevronUp className="border border-white p-1 rounded-full text-white ml-3" />
                ) : (
                  <ChevronDown className="border border-[#B1B5C3] p-1 rounded-full text-[#B1B5C3] ml-3" />
                )}

                {/* متن سوال راست‌چین */}
                <span
                  className={`w-full bg-transparent text-base ${
                    openIndex === index ? 'text-white' : 'text-[#B1B5C3]'
                  }`}
                >
                  {faq.question}
                </span>
              </button>

              {/* پاسخ */}
              {openIndex === index && (
                <>
                  <div className="border-t border-gray-800 mx-4"></div>
                  <div
                    id={`faq-answer-${index}`}
                    className="p-4 text-[#B1B5C3] text-right text-sm"
                  >
                    {faq.answer}
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* SEO Content Section - Rich Text for Search Engines */}
      <FaqSeoContent />

      <Footer/>
    </div>
  );
}
