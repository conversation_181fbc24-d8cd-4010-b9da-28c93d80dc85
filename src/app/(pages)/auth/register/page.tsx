"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { register } from "@/requests/authRequest";
import toast from "react-hot-toast";
import BTN from "@/components/form/BTN";
import OTPInput from "react-otp-input";
import { motion } from "framer-motion";

export default function Register() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const phone = searchParams.get("phone");
  const [firstname, setFirstname] = useState<string>("");
  const [lastname, setLastname] = useState<string>("");
  const [gender, setGender] = useState<string>("");
  const [national_id, setNational_id] = useState<string>("");
  const [smsCode, setSmsCode] = useState("");

  const [timeLeft, setTimeLeft] = useState(30);
  const [timerRunning, setTimerRunning] = useState(true);

  useEffect(() => {
    if (!phone) {
      router.push("/auth/login");
    }
  }, [phone, router]);

  useEffect(() => {
    if (timerRunning && timeLeft > 0) {
      const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
      return () => clearInterval(timer);
    } else if (timeLeft === 0) {
      setTimerRunning(false);
    }
  }, [timeLeft, timerRunning]);

  async function handleSubmit() {
    const result = await register(
      smsCode,
      phone,
      firstname,
      lastname,
      gender,
      national_id
    );
    if (result.isError) {
      toast.error("خطایی رخ خطایی رخ داد لطفا دو دقیقه دیگر تلاش فرمایید");
    } else {
      toast.success(result.message);
      router.push("/dashboard");
    }
  }

  return (
    <div
      className="flex flex-col font-dmSans items-center justify-center min-h-screen bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('/images/main-bg.jpg')",
        backgroundSize: "cover",
      }}
    >
      {/* Animated background overlay */}
      <div className="absolute inset-0 bg-[#18191D]/70 backdrop-blur-md"></div>

      {/* Animated particles */}
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      {/* Decorative elements */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.15 }}
        transition={{ duration: 1.5 }}
        className="absolute top-20 right-20 w-64 h-64 bg-blue-500 rounded-full blur-[120px]"
      />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1.5, delay: 0.3 }}
        className="absolute bottom-20 left-20 w-72 h-72 bg-purple-500 rounded-full blur-[120px]"
      />

      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        action={handleSubmit}
        className="relative bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24]/80 p-8 border border-gray-800/50 rounded-2xl shadow-2xl w-[480px] text-center backdrop-blur-xl z-10 overflow-hidden"
      >
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.7)" }}
          whileTap={{ scale: 0.95 }}
          onClick={() => router.push("/auth/login")}
          className="absolute top-4 left-4 text-white bg-blue-600/60 px-4 py-2 rounded-lg transition-all duration-300 z-20 backdrop-blur-sm border border-blue-500/30 shadow-md"
        >
          بازگشت
        </motion.button>

        {/* Top gradient line */}
        <div
          className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_0px_15px_5px_rgba(72,153,235,0.5)]"
          style={{
            background:
              "linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
          }}
        />

        {/* Decorative grid pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

        {/* Decorative circles */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500 opacity-5 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-500 opacity-5 rounded-full -ml-16 -mb-16"></div>

        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-between my-8 mx-[50] bg-transparent"
        >
          <img
            src="/images/dot-l.png"
            alt="Left Image"
            className="w-20 h-12 bg-transparent"
          />
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <img
              src="/images/icon.png"
              alt="Image"
              className="w-20 h-12 bg-transparent"
            />
          </motion.div>
          <img
            src="/images/dot-r.png"
            alt="Right Image"
            className="w-20 h-12 bg-transparent"
          />
        </motion.div>

        <motion.h1
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-2xl font-bold mb-6 text-white bg-transparent"
        >
          ثبت نام
        </motion.h1>

        <div className="space-y-4">
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="relative group"
          >
            <input
              type="text"
              name="firstName"
              placeholder="نام"
              value={firstname}
              onChange={(e) => setFirstname(e.target.value)}
              className="w-full p-3 pl-4 border border-gray-700 rounded-xl bg-[#141416]/80 text-white h-[50px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right"
            />
            <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
          </motion.div>

          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="relative group"
          >
            <input
              type="text"
              name="lastName"
              placeholder="نام خانوادگی"
              value={lastname}
              onChange={(e) => setLastname(e.target.value)}
              className="w-full p-3 pl-4 border border-gray-700 rounded-xl bg-[#141416]/80 text-white h-[50px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right"
            />
            <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
          </motion.div>

          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="relative group"
          >
            <select
              name="gender"
              value={gender}
              onChange={(e) => setGender(e.target.value)}
              className="w-full p-3 px-4 border border-gray-700 rounded-xl bg-[#141416]/80 text-gray-300 h-[50px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right appearance-none"
              style={{ backgroundImage: "url('/images/arrow-down.png')", backgroundRepeat: "no-repeat", backgroundPosition: "left 16px center", backgroundSize: "16px" }}
            >
              <option value="">انتخاب جنسیت</option>
              <option value="male">مرد</option>
              <option value="female">زن</option>
            </select>
            <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
          </motion.div>

          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            className="relative group"
          >
            <input
              type="tel"
              maxLength={10}
              name="nationalId"
              placeholder="کد ملی"
              value={national_id}
              onChange={(e) => setNational_id(e.target.value)}
              className="w-full p-3 pl-4 border border-gray-700 rounded-xl bg-[#141416]/80 text-white h-[50px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right"
            />
            <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
          </motion.div>
        </div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.9 }}
          className="mt-6"
        >
          <p className="text-lg text-white font-bold bg-transparent">
            کد امنیت پیامکی
          </p>
          <p className="text-gray-300 text-center text-sm bg-transparent mt-1">
            ما یک کد تایید 6 رقمی به شما پیامک کرده‌ایم. لطفاً کد را در کادر زیر
            وارد کنید تا درخواست ورود خود را تایید کنید
          </p>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 1 }}
          className="mt-4 flex gap-2 justify-center bg-transparent z-20 relative"
          dir="ltr"
        >
          <OTPInput
            inputType="tel"
            value={smsCode}
            numInputs={6}
            onChange={setSmsCode}
            renderInput={(props) => (
              <input
                {...props}
                className="!w-[50px] !h-[54px] !text-white !font-bold !bg-[#141416]/80 !border !border-gray-700 !rounded-xl !mx-1 focus:!border-blue-500 focus:!shadow-[0px_0px_15px_rgba(72,153,235,0.3)] !transition-all !duration-300 !outline-none"
              />
            )}
            containerStyle="flex justify-center items-center gap-2"
          />
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.1 }}
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="mt-8 w-full"
        >
          <BTN
            label="ثبت نام"
            type="submit"
            className="cursor-pointer w-full bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 py-3 rounded-xl text-white transition-all duration-300 shadow-lg hover:shadow-blue-500/30 font-medium text-lg relative overflow-hidden"
          />
          <div className="w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/50 to-blue-600/0 mt-1"></div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.2 }}
          className="mt-4 text-sm text-gray-300 flex justify-between w-full bg-transparent"
        >
          <motion.p
            whileHover={{ scale: 1.05 }}
            className="text-gray-300 bg-transparent px-3 py-1 rounded-lg bg-gray-800/30 border border-gray-700/30"
          >
            {`00:${timeLeft < 10 ? `0${timeLeft}` : timeLeft}`}
          </motion.p>
          <motion.button
            whileHover={{ scale: 1.05, color: "#3b82f6" }}
            whileTap={{ scale: 0.95 }}
            className="text-sm text-gray-300 hover:text-blue-400 transition-colors duration-300"
            onClick={() => router.push("/auth/login")}
          >
            کد ورود را دریافت نکردید؟
          </motion.button>
        </motion.div>
      </motion.form>
    </div>
  );
}
