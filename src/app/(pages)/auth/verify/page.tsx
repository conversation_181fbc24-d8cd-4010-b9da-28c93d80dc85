"use client";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { verifyOtp } from "@/requests/authRequest";
import { resendOtp } from "@/actions/clientActions";
import toast from "react-hot-toast";
import BTN from "@/components/form/BTN";
import OTPInput from "react-otp-input";
import { motion } from "framer-motion";

const Verify = () => {
  const [smsCode, setSmsCode] = useState("");
  const [error, setError] = useState("");
  const [timeLeft, setTimeLeft] = useState(120);
  const [isResending, setIsResending] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const phone = searchParams.get("phone");

  useEffect(() => {
    if (!phone) {
      router.push("/auth/login");
    }
  }, [phone, router]);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
      return () => clearInterval(timer);
    }
  }, [timeLeft]);

  useEffect(() => {
    if (smsCode.length === 6) {
      handleSubmit();
    }
  }, [smsCode]);

  const handleSubmit = async () => {
    setError("");
    const result = await verifyOtp(phone, smsCode);
    if (result.isError) {
      toast.error("خطایی رخ داد لطفا دو دقیقه دیگر تلاش فرمایید");
    } else {
      toast.success(result.message);
      router.replace(`/dashboard`);
    }
  };

  const handleResendOtp = async () => {
    if (timeLeft > 0 || !phone) return;

    setIsResending(true);
    try {
      // Format phone number (remove leading zero if present)
      const formattedPhone = phone.startsWith('0') ? phone.substring(1) : phone;

      const result = await resendOtp(formattedPhone);
      if (result.isError) {
        toast.error("خطایی در ارسال مجدد کد رخ داد. لطفا دوباره تلاش کنید.");
      } else {
        toast.success("کد تایید جدید ارسال شد.");
        setTimeLeft(120); // Reset timer to 2 minutes
        setSmsCode(""); // Clear input field
      }
    } catch (error) {
      toast.error("خطایی در ارسال مجدد کد رخ داد. لطفا دوباره تلاش کنید.");
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div
      className="flex flex-col font-dmSans items-center justify-center min-h-screen bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('/images/main-bg.jpg')",
        backgroundSize: "cover",
      }}
    >
      {/* Animated background overlay */}
      <div className="absolute inset-0 bg-[#18191D]/70 backdrop-blur-md"></div>

      {/* Animated particles */}
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      {/* Decorative elements */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.15 }}
        transition={{ duration: 1.5 }}
        className="absolute top-20 right-20 w-64 h-64 bg-blue-500 rounded-full blur-[120px]"
      />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1.5, delay: 0.3 }}
        className="absolute bottom-20 left-20 w-72 h-72 bg-purple-500 rounded-full blur-[120px]"
      />

      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        action={handleSubmit}
        className="relative bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24]/80 p-8 border border-gray-800/50 rounded-2xl shadow-2xl w-[420px] text-center backdrop-blur-xl z-10 overflow-hidden"
      >
        {/* Top gradient line */}
        <div
          className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_0px_15px_5px_rgba(72,153,235,0.5)]"
          style={{
            background:
              "linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
          }}
        />

        {/* Decorative grid pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

        {/* Decorative circles */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500 opacity-5 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-500 opacity-5 rounded-full -ml-16 -mb-16"></div>

        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-center my-8 mx-[50] bg-transparent"
        >
          <img
            src="/images/dot-l.png"
            alt="Left Image"
            className="w-20 h-12 bg-transparent"
          />
          <motion.h2
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-2xl font-bold text-white bg-transparent"
          >

          </motion.h2>
          <img
            src="/images/dot-r.png"
            alt="Right Image"
            className="w-20 h-12 bg-transparent"
          />
        </motion.div>

        <motion.h1
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-2xl font-bold bg-transparent text-white"
        >
          تایید کد پیامکی
        </motion.h1>

        <motion.p
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mt-2 text-gray-300 text-[14px] bg-transparent"
        >
          کد ۶ رقمی ارسال شده را وارد کنید
        </motion.p>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-8 flex gap-2 justify-center bg-transparent z-20 relative"
          dir="ltr"
        >
          <OTPInput
            inputType="tel"
            value={smsCode}
            numInputs={6}
            onChange={setSmsCode}
            renderInput={(props) => (
              <input
                {...props}
                className="!w-[50px] !h-[54px] !text-white !font-bold !bg-[#141416]/80 !border !border-gray-700 !rounded-xl !mx-1 focus:!border-blue-500 focus:!shadow-[0px_0px_15px_rgba(72,153,235,0.3)] !transition-all !duration-300 !outline-none"
              />
            )}
            containerStyle="flex justify-center items-center gap-2"
          />
        </motion.div>

        {error && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-4 text-red-400 text-sm bg-transparent"
          >
            {error}
          </motion.p>
        )}

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="mt-8 w-full"
        >
          <BTN
            label="تایید کد"
            type="submit"
            className="cursor-pointer w-full bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 py-3 rounded-xl text-white transition-all duration-300 shadow-lg hover:shadow-blue-500/30 font-medium text-lg relative overflow-hidden"
          />
          <div className="w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/50 to-blue-600/0 mt-1"></div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="mt-4 text-sm text-gray-300 flex justify-between w-full bg-transparent"
        >
          <motion.p
            whileHover={{ scale: 1.05 }}
            className="text-gray-300 bg-transparent px-3 py-1 rounded-lg bg-gray-800/30 border border-gray-700/30"
          >
            {`${Math.floor(timeLeft / 60)}:${timeLeft % 60 < 10 ? "0" : ""}${timeLeft % 60}`}
          </motion.p>
          <motion.button
            whileHover={{ scale: 1.05, color: "#3b82f6" }}
            whileTap={{ scale: 0.95 }}
            disabled={timeLeft > 0 || isResending}
            onClick={handleResendOtp}
            className={`text-sm transition-colors duration-300 ${
              timeLeft > 0 || isResending
                ? "text-gray-600 cursor-not-allowed"
                : "text-gray-300 hover:text-blue-400"
            }`}
          >
            {isResending ? "در حال ارسال..." : "هنوز کد را دریافت نکردید؟"}
          </motion.button>
        </motion.div>
      </motion.form>
    </div>
  );
};

export default Verify;
