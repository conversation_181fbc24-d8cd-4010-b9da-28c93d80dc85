"use client";
import { useState, ChangeEvent } from "react";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { loginUser } from "@/requests/authRequest";
import toast from "react-hot-toast";
import BTN from "@/components/form/BTN";
import { motion } from "framer-motion";

const Login = () => {
  const [phone, setPhone] = useState<string>("");
  const [error, setError] = useState<string>("");
  const router = useRouter();

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPhone(value);

    // Accept 10 digits or 11 digits with leading zero
    const phonePattern = /^(0?[0-9]{10})$/;
    if (phonePattern.test(value)) {
      setError("");
    } else {
      setError("لطفا یک شماره تلفن معتبر وارد کنید.");
    }
  };

  const handleLogin = async () => {
    // Remove leading zero if present
    const formattedPhone = phone.startsWith('0') ? phone.substring(1) : phone;

    const result = await loginUser(formattedPhone);
    if (result.isError) {
      toast.error("خطایی رخ داد لطفا دو دقیقه دیگر تلاش فرمایید");
    } else {
      if (result.status) {
        router.replace(`/auth/verify?phone=${formattedPhone}`);
      } else {
        router.push(`/auth/register?phone=${formattedPhone}`);
      }
      toast.success(result.message);
    }
  };

  return (
    <div
      className="flex flex-col font-dmSans items-center justify-center min-h-screen bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('/images/main-bg.jpg')",
        backgroundSize: "cover",
      }}
    >
      {/* Animated background overlay */}
      <div className="absolute inset-0 bg-[#18191D]/70 backdrop-blur-md"></div>

      {/* Animated particles */}
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      {/* Decorative elements */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.15 }}
        transition={{ duration: 1.5 }}
        className="absolute top-20 right-20 w-64 h-64 bg-blue-500 rounded-full blur-[120px]"
      />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1.5, delay: 0.3 }}
        className="absolute bottom-20 left-20 w-72 h-72 bg-purple-500 rounded-full blur-[120px]"
      />

      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        action={handleLogin}
        className="relative bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24]/80 p-8 border border-gray-800/50 rounded-2xl shadow-2xl w-[420px] text-center backdrop-blur-xl z-10 overflow-hidden"
      >
        {/* Top gradient line */}
        <div
          className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_0px_15px_5px_rgba(72,153,235,0.5)]"
          style={{
            background:
              "linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
          }}
        />

        {/* Decorative grid pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

        {/* Decorative circles */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500 opacity-5 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-500 opacity-5 rounded-full -ml-16 -mb-16"></div>

        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-center my-8 mx-[50] bg-transparent"
        >
          <img
            src="/images/dot-l.png"
            alt="Left Image"
            className="w-20 h-12 bg-transparent"
          />
      
          <img
            src="/images/dot-r.png"
            alt="Right Image"
            className="w-20 h-12 bg-transparent"
          />
        </motion.div>

        <motion.h1
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-2xl font-bold bg-transparent text-white"
        >
          به اکسچنجیم خوش آمدید
        </motion.h1>

        <motion.p
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mt-2 text-gray-300 text-[14px] bg-transparent"
        >
          لطفا شماره تلفن خود را برای ورود یا ثبت نام وارد کنید
        </motion.p>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="relative mt-8 w-full bg-transparent"
        >
          <motion.div
            whileFocus={{ scale: 1.02 }}
            className="relative group"
          >
            <input
              type="tel"
              autoFocus
              placeholder="شماره تماس"
              value={phone}
              onChange={handleInputChange}
              className="w-full p-4 pl-12 border border-gray-700 rounded-xl bg-[#141416]/80 text-white h-[60px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300"
            />
            <span className="absolute left-4 top-1/2 -translate-y-1/2 w-[24px] h-[24px] text-gray-400 bg-transparent transition-all duration-300 group-focus-within:text-blue-400">
              <img
                src="/images/phone-icon.png"
                className="w-5 h-5 bg-transparent"
                alt="Phone icon"
              />
            </span>

            {/* Subtle glow effect on focus */}
            <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
          </motion.div>
        </motion.div>

        {error && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-2 text-red-400 text-sm bg-transparent"
          >
            {error}
          </motion.p>
        )}

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="mt-8 w-full"
        >
          <BTN
            label="مرحله بعد"
            type="submit"
            className="cursor-pointer w-full bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 py-3 rounded-xl text-white transition-all duration-300 shadow-lg hover:shadow-blue-500/30 font-medium text-lg relative overflow-hidden"
          />
          <div className="w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/50 to-blue-600/0 mt-1"></div>
        </motion.div>
      </motion.form>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mt-6 text-gray-300 text-sm bg-transparent flex items-center justify-center gap-3 w-full z-10"
      >
      
      </motion.div>

    </div>
  );
};

export default Login;
