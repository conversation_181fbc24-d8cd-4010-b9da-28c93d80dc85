'use client';

import Image from "next/image";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import Link from 'next/link';
import JsonLd from '@/components/seo/JsonLd';

export default function NotFoundPage() {
  // BreadcrumbList schema
  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'صفحه اصلی',
        item: 'https://exchangim.com',
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'صفحه یافت نشد',
        item: 'https://exchangim.com/404',
      },
    ],
  };

  return (
    <div className="bg-[url('/images/main-bg.jpg')] bg-cover bg-center bg-no-repeat bg-fixed min-h-screen text-white relative">
      {/* SEO Structured Data */}
      <JsonLd data={breadcrumbData} />
      
      {/* Blue Glow Effect */}
      <div
        className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_60px_180px_50px_rgba(72,153,235,0.3)]"
        style={{
          background: 'linear-gradient(90deg, rgba(211, 211, 211, 0) 0%, rgba(72,153,235,0.7) 50%, rgba(211, 211, 211, 0) 100%)',
        }}
      />
      
      <div className="flex justify-center w-full bg-transparent fixed top-0 left-0 right-0 z-50">
        <div className="w-full max-w-[1100px]">
          <Navbar 
            className="py-4 border-b border-gray-800 backdrop-blur-sm bg-transparent" 
            hideSearch 
            hideSignUp 
          />
        </div>
      </div>

      {/* ارتباط با پشتیبانی */}
      <section className="container py-8 text-center rounded bg-transparent px-5 md:px-10 relative z-10">
        <div className="p-8 mx-auto rounded-lg relative max-w-5xl mt-10">
          <div className="flex justify-center items-center bg-transparent mb-6">
            <img
              src="/images/404.png"
              alt="صفحه 404 - صفحه مورد نظر یافت نشد"
              className="w-100 bg-transparent"
            />
          </div>
          
          <h1 className="text-[42px] bg-transparent mb-4"> متاسفیم! صفحه مورد نظر شناسایی نشد. </h1>
          
          <p className="text-gray-400 mt-2 bg-transparent">
            شاید شما URL را اشتباه تایپ کرده اید؟ حتما املای خود را چک کنید.
          </p>
          
          <div className="mt-8 flex justify-center">
            <Link href="/" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
              بازگشت به صفحه اصلی
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
