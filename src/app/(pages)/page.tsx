import { Metadata } from "next";
import Navbar from "./components/Navbar";
import Header from "./components/Header";
import Price from "./components/Price";
import Gallery from "./components/Gallery";
import Mobile from "./components/Mobile";
import GallerySec from "./components/GallerySec";
import HowItWorks from "./components/HowItWorks";
import Testimonials from "./components/Testimonials";
import Question from "./components/Question";
import Footer from "./components/Footer";
import ParticlesBackground from "./components/ParticlesBackground";
import HomeSeo from "./components/HomeSeo";
import SeoContent from "./components/SeoContent";

export const metadata: Metadata = {
  title: "اکسچنجیم | صرافی معتبر ارز دیجیتال | خرید و فروش امن ارز دیجیتال",
  description: "اکسچنجیم، صرافی معتبر ارز دیجیتال با امنیت بالا و کارمزد پایین. خرید و فروش آنی بیت کوین و ارزهای دیجیتال با بهترین قیمت",
  keywords: "صرافی, صرافی ارز دیجیتال, صرافی معتبر, خرید ارز, خرید بیت کوین, فروش ارز دیجیتال, اکسچنجیم, رمز ارز, کریپتو, بیت کوین, اتریوم, تتر, خرید تتر, قیمت ارز دیجیتال, قیمت بیت کوین, قیمت اتریوم, معاملات ارز دیجیتال, کیف پول ارز دیجیتال, صرافی آنلاین, خرید ارز دیجیتال, فروش ارز دیجیتال",
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "اکسچنجیم | صرافی معتبر ارز دیجیتال | خرید و فروش امن ارز دیجیتال",
    description: "اکسچنجیم، صرافی معتبر ارز دیجیتال با امنیت بالا و کارمزد پایین. خرید و فروش آنی بیت کوین و ارزهای دیجیتال با بهترین قیمت",
    url: "https://exchangim.com",
    siteName: "Exchangim - اکسچنجیم",
    images: [
      {
        url: "/images/main-logo.png",
        width: 800,
        height: 600,
        alt: "اکسچنجیم - صرافی معتبر ارز دیجیتال",
      },
    ],
    locale: "fa_IR",
    type: "website",
  },
};

export default function Home() {
  return (
    <div className="bg-[#0F1116]" dir="ltr">
      {/* SEO Structured Data */}
      <HomeSeo />

      {/* Hero Section with Particles Background */}
      <div className="relative min-h-screen overflow-hidden">
        <ParticlesBackground />
        <div className="absolute inset-0 bg-gradient-to-b from-[#0F1116]/10 via-[#0F1116]/50 to-[#0F1116] pointer-events-none"></div>
        <Navbar />
        <Header />
      </div>

      {/* Live Price Section */}
      <div className="relative">
        <div className="inset-0 bg-[#0F1116] opacity-90"></div>
        <Price />
      </div>

      {/* Features Gallery */}
      <div className="relative py-20">
        <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-[#0F1116] to-transparent"></div>
        <Gallery />
      </div>

      {/* Mobile App Section */}
      <div className="relative py-10">
        <div className="absolute inset-0 bg-gradient-radial from-[#1A1E2D] to-[#0F1116] opacity-50"></div>
        <Mobile />
      </div>



      {/* How It Works Section */}
      <HowItWorks />

      {/* Testimonials Section */}
      <Testimonials />

      {/* FAQ Section */}
      <div className="relative py-16">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#131725] to-[#0F1116]"></div>
        <Question />
      </div>

      {/* SEO Content Section - Rich Text for Search Engines */}
      <SeoContent />

      {/* Footer */}
      <Footer />
    </div>
  );
}
