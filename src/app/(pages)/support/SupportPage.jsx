"use client";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import Link from "next/link";
import JsonLd from '@/components/seo/JsonLd';
import SupportSeoContent from './SupportSeoContent';

export default function SupportPage() {
  // BreadcrumbList schema
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'صفحه اصلی',
        item: 'https://exchangim.com'
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'پشتیبانی و راهنما',
        item: 'https://exchangim.com/support'
      }
    ]
  };

  // HowTo schema for support page
  const howToSchema = {
    '@context': 'https://schema.org',
    '@type': 'HowTo',
    name: 'راهنمای استفاده از اکسچنجیم',
    description: 'آموزش گام به گام استفاده از خدمات صرافی ارز دیجیتال اکسچنجیم',
    totalTime: 'PT30M',
    tool: [
      {
        '@type': 'HowToTool',
        name: 'حساب کاربری اکسچنجیم'
      },
      {
        '@type': 'HowToTool',
        name: 'مدارک شناسایی معتبر'
      }
    ],
    step: [
      {
        '@type': 'HowToStep',
        name: 'ثبت نام و ایجاد حساب کاربری',
        text: 'به وبسایت اکسچنجیم مراجعه کرده و با وارد کردن شماره موبایل و ایمیل خود، حساب کاربری ایجاد کنید.',
        url: 'https://exchangim.com/auth/register',
        image: 'https://exchangim.com/images/pers.png'
      },
      {
        '@type': 'HowToStep',
        name: 'احراز هویت',
        text: 'مدارک هویتی خود را آپلود کرده و فرآیند احراز هویت را تکمیل کنید.',
        url: 'https://exchangim.com/dashboard/profile',
        image: 'https://exchangim.com/images/pers.png'
      },
      {
        '@type': 'HowToStep',
        name: 'افزودن حساب بانکی',
        text: 'اطلاعات حساب بانکی خود را به پروفایل خود اضافه کنید.',
        url: 'https://exchangim.com/dashboard/profile',
        image: 'https://exchangim.com/images/cart.png'
      },
      {
        '@type': 'HowToStep',
        name: 'واریز و برداشت',
        text: 'حساب ریالی خود را شارژ کرده یا برداشت انجام دهید.',
        url: 'https://exchangim.com/dashboard/wallet',
        image: 'https://exchangim.com/images/deposit.png'
      }
    ]
  };

  // ContactPage schema
  const contactSchema = {
    '@context': 'https://schema.org',
    '@type': 'ContactPage',
    name: 'تماس با پشتیبانی اکسچنجیم',
    description: 'راه‌های ارتباط با پشتیبانی صرافی ارز دیجیتال اکسچنجیم',
    url: 'https://exchangim.com/support',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '021-12345678',
      contactType: 'customer service',
      email: '<EMAIL>',
      areaServed: 'IR',
      availableLanguage: ['Persian', 'English'],
      hoursAvailable: 'Mo-Fr 09:00-18:00'
    }
  };

  return (
    <div className="bg-[url('/images/main-bg.jpg')] bg-cover bg-center bg-no-repeat bg-fixed min-h-screen text-white">
      {/* Structured Data */}
      <JsonLd data={breadcrumbSchema} />
      <JsonLd data={howToSchema} />
      <JsonLd data={contactSchema} />

      <div className="flex justify-center w-full bg-transparent fixed top-0 left-0 right-0 z-50">
        <div className="w-full max-w-[1100px]">
          <Navbar
            className="py-4 border-b border-gray-800 backdrop-blur-sm bg-transparent"
            hideSearch
            hideSignUp
          />
        </div>
      </div>

      {/* Header */}
      <header className="text-center py-12 bg-transparent">
        <div className="flex justify-center items-center bg-transparent mt-10">
          <img
            src="/images/robot.png"
            alt="راهنمای استفاده از اکسچنجیم"
            className="w-[355px] sm:w-[250px] bg-transparent"
          />
        </div>
        <h1 className="text-4xl font-bold mt-5 bg-transparent">
          به راهنمای فعالیت در اکسچنجیم خوش آمدید
        </h1>

        <div className="relative mt-4 w-[90%] md:w-[450px] lg:w-[550px] mx-auto">
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 z-10">
            <img
              src="/images/search-icon.png"
              alt="جستجو"
              className="w-5 h-5 bg-transparent"
            />
          </div>
          <input
            type="text"
            placeholder="جستجو"
            className="p-2 pr-10 bg-[#23262F] rounded-md w-full h-[45px] text-right"
            aria-label="جستجو در راهنما"
          />
        </div>
      </header>

      {/* آموزش */}
      <section className="container py-8 bg-transparent px-15">
        <h2 className="text-[32px] text-right mb-6 bg-transparent font-bold">
          آموزش
        </h2>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 justify-center bg-transparent mx-auto">
          {/* کارت‌های گالری */}
          {[
            { title: "ارسال مدارک", icon: "/images/docu.png" },
            { title: "احراز هویت", icon: "/images/pers.png" },
            { title: "واریز و برداشت", icon: "/images/deposit.png" },
            { title: "افزودن حساب بانکی", icon: "/images/cart.png" },
            { title: "سطوح کاربری", icon: "/images/level.png" },
            { title: "قابلیت های حساب کاربری", icon: "/images/cart.png" },
          ].map((item, index) => (
            <div
              key={index}
              className="p-4 bg-[#18191D]/35 border border-gray-700 rounded-lg flex flex-col justify-center items-center relative h-[120px]"
            >
              <div
                className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_50px_130px_25px_rgba(72,153,235,0.5)]"
                style={{
                  background:
                    "linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
                }}
              />
              <div className="flex justify-center items-center text-center bg-transparent">
                <img src={item.icon} alt={`آیکون ${item.title}`} className="ml-2 w-5 h-5" />
                {item.title}
              </div>
            </div>
          ))}
        </div>
      </section>
      <section className="container mx-auto py-8 px-8 sm:px-10 md:px-12 lg:px-16 bg-transparent">
        <h2 className="text-[32px] text-right mb-6 bg-transparent font-bold">
          قوانین و سیاست ها
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 text-center mx-auto">
          <Link href="/privacy" className="block">
            <div className="p-4 bg-[#18191D]/35 border border-gray-700 rounded-lg relative h-[180px] flex items-center justify-center">
              <div
                className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_50px_130px_25px_rgba(72,153,235,0.5)]"
                style={{
                  background:
                    "linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
                }}
              />
              <span className="text-xl font-semibold">قوانین</span>
            </div>
          </Link>
          <Link href="/privacy" className="block">
            <div className="p-4 bg-[#18191D]/35 border border-gray-700 rounded-lg relative h-[180px] flex items-center justify-center">
              <div
                className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_50px_130px_25px_rgba(72,153,235,0.5)]"
                style={{
                  background:
                    "linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
                }}
              />
              <span className="text-xl font-semibold">سیاست های اکسچنجیم</span>
            </div>
          </Link>
        </div>
      </section>

      {/* ارتباط با پشتیبانی */}
      <section className="container py-8 text-center rounded bg-transparent px-5 md:px-10">
        <div className="p-8 mx-auto rounded-lg relative border border-gray-700 bg-[#18191D]/35 h-auto max-w-7xl">
          <div className="flex justify-center items-center bg-transparent mb-6">
            <img
              src="/images/headset.png"
              alt="پشتیبانی اکسچنجیم"
              className="w-52 h-42 bg-transparent"
            />
          </div>

          <h2 className="text-xl font-semibold bg-transparent mb-4">
            ارتباط با پشتیبانی
          </h2>

          <p className="text-gray-400 mt-2 bg-transparent">
            پشتیبانی تلفنی ما همه روزه از ساعت 9 الی 18 به غیر از تعطیلات رسمی
            آماده پاسخگویی به شما عزیزان می باشند.
          </p>

          <p className="text-gray-400 mt-2 mb-6 bg-transparent">
            جهت پیگیری امور مالی و فنی در ساعات تعطیل از "تیکت" استفاده کنید.
          </p>

          {/* Contact details (email and phone) with button */}
          <div className="flex flex-col md:flex-row justify-center items-center mt-6 bg-transparent">
            <div className="order-1 md:order-1">
              <button className="px-6 py-3 bg-[#4899EB] rounded-lg w-full md:w-auto h-[50px]">
                <Link href="/auth/login" className="bg-transparent">
                  ایجاد تیکت
                </Link>
              </button>
            </div>

            <div className="order-2 md:order-2 mt-4 md:mt-0 md:mr-6">
              <div className="flex flex-col space-y-1 bg-transparent">
                <div className="flex items-center justify-end bg-transparent">
                  <a href="mailto:<EMAIL>" className="text-gray-400 text-left bg-transparent ltr">
                    <EMAIL>
                  </a>
                  <img
                    src="/images/email-icon.png"
                    alt="ایمیل"
                    className="w-5 h-5 bg-transparent mr-2"
                  />
                </div>

                <div className="flex items-center justify-end bg-transparent">
                  <a href="tel:02112345678" className="text-gray-400 text-left bg-transparent ltr">
                    021-12345678
                  </a>
                  <img
                    src="/images/phone-icon.png"
                    alt="تلفن"
                    className="w-5 h-5 bg-transparent mr-2"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SEO Content Section - Rich Text for Search Engines */}
      <SupportSeoContent />

      <Footer />
    </div>
  );
}
