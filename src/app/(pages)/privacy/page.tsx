import { Metadata } from 'next';
import PrivacyPage from './PrivacyPage';

export const metadata: Metadata = {
  title: 'قوانین و حریم خصوصی | اکسچنجیم - صرافی معتبر ارز دیجیتال',
  description: 'قوانین و مقررات استفاده از خدمات صرافی ارز دیجیتال اکسچنجیم و سیاست‌های حفظ حریم خصوصی کاربران. اطلاعات مهم درباره نحوه جمع‌آوری، پردازش و محافظت از داده‌های شخصی',
  keywords: 'قوانین صرافی ارز دیجیتال, حریم خصوصی, سیاست حفظ اطلاعات, قوانین و مقررات اکسچنجیم, شرایط استفاده از خدمات صرافی, حفاظت از داده‌های شخصی',
  alternates: {
    canonical: '/privacy',
  },
  openGraph: {
    title: 'قوانین و حریم خصوصی | اکسچنجیم - صرافی معتبر ارز دیجیتال',
    description: 'قوانین و مقررات استفاده از خدمات صرافی ارز دیجیتال اکسچنجیم و سیاست‌های حفظ حریم خصوصی کاربران',
    url: 'https://exchangim.com/privacy',
    siteName: 'Exchangim - اکسچنجیم',
    locale: 'fa_IR',
    type: 'website',
  },
};

export default function Privacy() {
  return <PrivacyPage />;
}
