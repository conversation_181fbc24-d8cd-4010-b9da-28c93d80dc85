import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { PRIVATE_ROUTE } from "./lib/routes";
import { cookies } from "next/headers";

export async function middleware(request: NextRequest) {
  const cookieStore = await cookies();
  const token = cookieStore.get("token");
  const isAuthenticated = token?.value;
  const { nextUrl } = request;

  const isPrivateRoute = PRIVATE_ROUTE.find((route) =>
    nextUrl.pathname.startsWith(route)
  );

  if (!isAuthenticated && isPrivateRoute)
    return NextResponse.redirect(new URL("/auth/login", nextUrl));
}

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
