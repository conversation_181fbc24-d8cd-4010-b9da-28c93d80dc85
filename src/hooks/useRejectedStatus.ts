"use client";
import { useState, useEffect } from "react";
import { getProfile } from "@/requests/dashboardRequest";

export function useRejectedStatus() {
  const [isRejected, setIsRejected] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkUserStatus = async () => {
      setIsLoading(true);
      try {
        const result = await getProfile();
        if (!result.isError && result.status === "rejected") {
          setIsRejected(true);
          setIsModalOpen(true);
        }
      } catch (error) {
        console.error("Error checking user status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkUserStatus();
  }, []);

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return {
    isRejected,
    isModalOpen,
    isLoading,
    closeModal,
  };
}
