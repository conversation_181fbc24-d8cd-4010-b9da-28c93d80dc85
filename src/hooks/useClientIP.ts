/**
 * React hook for managing client IP address
 * این hook برای مدیریت IP کاربر در کامپوننت‌های React استفاده می‌شود
 */

import { useState, useEffect } from 'react';
import { getClientIP, getCachedIP, preloadClientIP } from '@/utils/getClientIP';

interface UseClientIPReturn {
  ip: string | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to get and manage client IP address
 * @returns Object containing IP, loading state, error, and refetch function
 */
export function useClientIP(): UseClientIPReturn {
  const [ip, setIP] = useState<string | null>(getCachedIP());
  const [isLoading, setIsLoading] = useState<boolean>(!getCachedIP());
  const [error, setError] = useState<string | null>(null);

  const fetchIP = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const clientIP = await getClientIP();
      setIP(clientIP);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get IP';
      setError(errorMessage);
      setIP('unknown');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // If IP is not cached, fetch it
    if (!getCachedIP()) {
      fetchIP();
    }
  }, []);

  const refetch = async () => {
    await fetchIP();
  };

  return {
    ip,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook to preload IP address (use in app initialization)
 * این hook را در ابتدای اپلیکیشن استفاده کنید
 */
export function usePreloadIP(): void {
  useEffect(() => {
    preloadClientIP();
  }, []);
}
