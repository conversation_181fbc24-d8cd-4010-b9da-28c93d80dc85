import forge from 'node-forge';

// RSA Public Key for encryption
export const RSA_PUBLIC_KEY = `-----B<PERSON>IN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArklssikVb+KmZV7gEgCL
Ht7TTChjAiIg6rjUjubAaZH5TLyG9pNJGdoeyV6pMG8WUZ3SLqrzzamXIF7mYsxq
SbbZmYn4wHCqNgpG9aejo/p5AZjTSZtWvtnrvgHOnneQCzhMD5tqlu4lDdCJLoi1
0DEWK/LMyqRU4yLeYPBHjJHN4EsnYEiQrYNZwQ7o9kBAHBMVg76+IGoZsjqYGwNm
uoOJR23jjcrsrES7q9LvXor/feTkvbnL3k6KvVgWBmL7x+w1m90gvygplYwSNWf9
BuPxqI0naDt0dUxuY2GN7dppTGE2RZzafvyRTRNQyPxnVU3XWuJw3x+pIEwRe6b2
swIDAQAB
-----END PUBLIC KEY-----`;

/**
 * Encrypts data using RSA-OAEP with the provided public key
 * @param data - The data to encrypt (will be JSON stringified)
 * @param publicKeyPem - The RSA public key in PEM format
 * @returns Base64 encoded encrypted string
 */
export function encryptRSA(data: any, publicKeyPem: string = RSA_PUBLIC_KEY): string {
  try {
    const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);
    const jsonData = JSON.stringify(data);
    const encrypted = publicKey.encrypt(jsonData, 'RSA-OAEP');
    return forge.util.encode64(encrypted); // تبدیل به base64 برای ارسال
  } catch (error) {
    console.error('RSA encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Checks if the data should be encrypted
 * @param data - The request data
 * @returns boolean indicating if encryption is needed
 */
export function shouldEncryptData(data: any): boolean {
  // Skip encryption for FormData (file uploads) or if data is null/undefined
  if (!data || data instanceof FormData) {
    return false;
  }
  
  // Skip encryption if data is already encrypted (has 'data' property with string value)
  if (typeof data === 'object' && 'data' in data && typeof data.data === 'string') {
    return false;
  }
  
  return true;
}
