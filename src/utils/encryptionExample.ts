/**
 * Example usage of RSA encryption for manual encryption
 * This file demonstrates how to use the encryption utilities
 * when you need to encrypt data manually (outside of axios)
 */

import { encryptRSA, RSA_PUBLIC_KEY } from './rsaEncrypt';
import { getClientIP } from './getClientIP';

// Example 1: Login data encryption with IP
export const encryptLoginData = async (phone: string, password: string) => {
  const clientIP = await getClientIP();
  const formData = {
    phone,
    password,
    client_ip: clientIP,
    user_ip: clientIP
  };

  return encryptRSA(formData, RSA_PUBLIC_KEY);
};

// Example 2: User profile data encryption with IP
export const encryptProfileData = async (profileData: any) => {
  const clientIP = await getClientIP();
  const dataWithIP = {
    ...profileData,
    client_ip: clientIP,
    user_ip: clientIP
  };

  return encryptRSA(dataWithIP, RSA_PUBLIC_KEY);
};

// Example 3: Manual fetch with encryption and IP (alternative to axios)
export const manualEncryptedFetch = async (url: string, data: any) => {
  const clientIP = await getClientIP();
  const dataWithIP = {
    ...data,
    client_ip: clientIP,
    user_ip: clientIP
  };

  const encryptedData = encryptRSA(dataWithIP, RSA_PUBLIC_KEY);

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Client-IP': clientIP,
      'X-Real-IP': clientIP,
      'X-Forwarded-For': clientIP,
    },
    body: JSON.stringify({
      data: encryptedData,
    }),
  });

  return response;
};

// Example usage:
/*
// For login
const encryptedLoginData = encryptLoginData('09121234567', 'testpass123');
console.log('Encrypted login data:', encryptedLoginData);

// For profile update
const profileData = {
  name: 'احمد',
  family: 'احمدی',
  nationalId: '1234567890'
};
const encryptedProfile = encryptProfileData(profileData);
console.log('Encrypted profile data:', encryptedProfile);

// Manual fetch example
manualEncryptedFetch('/api/user/login', {
  phone: '09121234567',
  password: 'testpass123'
}).then(response => {
  console.log('Response:', response);
});
*/
