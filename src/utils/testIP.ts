/**
 * Test functions for IP functionality
 * این فایل برای تست عملکرد IP استفاده می‌شود
 */

import { getClientIP, getCachedIP, clearIPCache } from './getClientIP';

/**
 * Test IP functionality
 */
export const testIPFunctionality = async () => {
  console.log('🌐 Testing IP Functionality...\n');

  try {
    // Test 1: Clear cache and get fresh IP
    console.log('🧹 Clearing IP cache...');
    clearIPCache();
    console.log('✅ Cache cleared\n');

    // Test 2: Get IP for the first time
    console.log('📡 Fetching client IP...');
    const startTime = Date.now();
    const ip = await getClientIP();
    const fetchTime = Date.now() - startTime;
    console.log(`✅ IP obtained: ${ip}`);
    console.log(`⏱️ Fetch time: ${fetchTime}ms\n`);

    // Test 3: Get cached IP (should be instant)
    console.log('💾 Getting cached IP...');
    const cachedStartTime = Date.now();
    const cachedIP = getCachedIP();
    const cachedTime = Date.now() - cachedStartTime;
    console.log(`✅ Cached IP: ${cachedIP}`);
    console.log(`⏱️ Cache time: ${cachedTime}ms\n`);

    // Test 4: Get IP again (should use cache)
    console.log('🔄 Getting IP again (should use cache)...');
    const secondStartTime = Date.now();
    const secondIP = await getClientIP();
    const secondTime = Date.now() - secondStartTime;
    console.log(`✅ Second IP: ${secondIP}`);
    console.log(`⏱️ Second fetch time: ${secondTime}ms\n`);

    // Test 5: Verify IPs match
    if (ip === cachedIP && cachedIP === secondIP) {
      console.log('✅ All IP values match - caching works correctly!\n');
    } else {
      console.log('❌ IP values don\'t match - caching issue!\n');
    }

    // Test 6: Show example request data
    console.log('📦 Example request data with IP:');
    const exampleData = {
      phone: '09121234567',
      password: 'testpass123',
      client_ip: ip,
      user_ip: ip
    };
    console.log(JSON.stringify(exampleData, null, 2));
    console.log('\n');

    console.log('🎉 All IP tests completed successfully!');
    return {
      success: true,
      ip,
      fetchTime,
      cachedTime,
      secondTime
    };

  } catch (error) {
    console.error('❌ IP test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Test IP in browser console
 * Run this in browser console to test IP functionality
 */
export const testIPInConsole = () => {
  if (typeof window !== 'undefined') {
    testIPFunctionality().then(result => {
      console.log('Test result:', result);
    });
  } else {
    console.log('This function should be run in browser environment');
  }
};

// Export for global access in browser
if (typeof window !== 'undefined') {
  (window as any).testIP = testIPInConsole;
  console.log('💡 Run testIP() in console to test IP functionality');
}
