/**
 * Test file for RSA encryption functionality
 * Run this to verify that encryption is working correctly
 */

import { encryptRSA, shouldEncryptData, RSA_PUBLIC_KEY } from './rsaEncrypt';

// Test data
const testData = {
  phone: '09121234567',
  password: 'testpass123',
  name: 'احمد',
  family: 'احمدی'
};

// Test function
export const testEncryption = () => {
  console.log('🔐 Testing RSA Encryption...\n');
  
  try {
    // Test 1: Basic encryption
    console.log('📝 Original data:', testData);
    const encrypted = encryptRSA(testData);
    console.log('🔒 Encrypted data:', encrypted);
    console.log('✅ Encryption successful!\n');
    
    // Test 2: shouldEncryptData function
    console.log('🧪 Testing shouldEncryptData function:');
    console.log('- Normal object:', shouldEncryptData(testData)); // should be true
    console.log('- FormData:', shouldEncryptData(new FormData())); // should be false
    console.log('- null:', shouldEncryptData(null)); // should be false
    console.log('- undefined:', shouldEncryptData(undefined)); // should be false
    console.log('- Already encrypted:', shouldEncryptData({ data: 'encrypted_string' })); // should be false
    console.log('✅ shouldEncryptData tests passed!\n');
    
    // Test 3: Verify encrypted data format
    const encryptedFormat = { data: encrypted };
    console.log('📦 Final format for server:', encryptedFormat);
    console.log('✅ Format verification passed!\n');
    
    console.log('🎉 All encryption tests passed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Encryption test failed:', error);
    return false;
  }
};

// Uncomment the line below to run the test
// testEncryption();
