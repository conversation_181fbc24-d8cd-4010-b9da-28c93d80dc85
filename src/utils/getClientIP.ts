/**
 * Utility functions to get client IP address
 * این فایل برای گرفتن IP address واقعی کاربر استفاده می‌شود
 */

// Cache for storing IP address to avoid multiple API calls
let cachedIP: string | null = null;
let ipFetchPromise: Promise<string> | null = null;

/**
 * Get client IP address using multiple fallback methods
 * @returns Promise<string> - The client's IP address
 */
export async function getClientIP(): Promise<string> {
  // Return cached IP if available
  if (cachedIP) {
    return cachedIP;
  }

  // Return existing promise if IP fetch is in progress
  if (ipFetchPromise) {
    return ipFetchPromise;
  }

  // Create new promise for fetching IP
  ipFetchPromise = fetchClientIP();
  
  try {
    const ip = await ipFetchPromise;
    cachedIP = ip;
    return ip;
  } catch (error) {
    console.error('Failed to get client IP:', error);
    return 'unknown';
  } finally {
    ipFetchPromise = null;
  }
}

/**
 * Internal function to fetch IP from various services
 */
async function fetchClientIP(): Promise<string> {
  const ipServices = [
    // Primary services (fast and reliable)
    { url: 'https://api.ipify.org?format=json', key: 'ip' },
    { url: 'https://ipapi.co/json/', key: 'ip' },
    { url: 'https://httpbin.org/ip', key: 'origin' },
    
    // Fallback services
    { url: 'https://api.my-ip.io/ip.json', key: 'ip' },
    { url: 'https://ipinfo.io/json', key: 'ip' },
  ];

  for (const service of ipServices) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(service.url, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        const ip = data[service.key];
        
        if (ip && isValidIP(ip)) {
          console.log(`✅ IP obtained from ${service.url}:`, ip);
          return ip;
        }
      }
    } catch (error) {
      console.warn(`❌ Failed to get IP from ${service.url}:`, error);
      continue;
    }
  }

  throw new Error('Unable to determine client IP address');
}

/**
 * Validate if the string is a valid IP address
 * @param ip - IP address string to validate
 * @returns boolean
 */
function isValidIP(ip: string): boolean {
  // IPv4 regex
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // IPv6 regex (simplified)
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * Clear cached IP (useful for testing or when IP might change)
 */
export function clearIPCache(): void {
  cachedIP = null;
  ipFetchPromise = null;
}

/**
 * Get IP synchronously from cache (returns null if not cached)
 * @returns string | null
 */
export function getCachedIP(): string | null {
  return cachedIP;
}

/**
 * Preload IP address (call this early in app lifecycle)
 * @returns Promise<void>
 */
export async function preloadClientIP(): Promise<void> {
  try {
    await getClientIP();
    console.log('✅ Client IP preloaded successfully');
  } catch (error) {
    console.warn('⚠️ Failed to preload client IP:', error);
  }
}
