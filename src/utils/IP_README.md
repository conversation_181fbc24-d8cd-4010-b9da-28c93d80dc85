# 🌐 Client IP Integration

این فایل‌ها برای اضافه کردن IP واقعی کاربر به درخواست‌های ارسالی به سرور ایجاد شده‌اند.

## 📁 فایل‌های ایجاد شده:

### 1. `getClientIP.ts` - اصلی‌ترین فایل
- گرفتن IP کاربر از سرویس‌های مختلف
- Cache کردن IP برای جلوگیری از درخواست‌های اضافی
- Fallback به چندین سرویس مختلف

### 2. `useClientIP.ts` - React Hook
- Hook برای استفاده در کامپوننت‌های React
- مدیریت state و loading
- امکان refetch کردن IP

### 3. `IPPreloader.tsx` - کامپوننت Preloader
- کامپوننت نامرئی برای preload کردن IP
- در layout اصلی اضافه شده

### 4. `testIP.ts` - فایل تست
- تست عملکرد IP functionality
- قابل اجرا در browser console

## 🔧 نحوه کار:

### 1. **Automatic Integration (پیشنهادی)**
همه درخواست‌های axios به صورت خودکار IP کاربر را اضافه می‌کنند:

```typescript
// هیچ کد اضافی نیاز نیست!
const response = await axiosInstance.post('/api/user/login', {
  phone: '09121234567',
  password: 'testpass123'
});

// IP به صورت خودکار اضافه می‌شود:
// Headers: X-Client-IP, X-Real-IP, X-Forwarded-For
// Data: client_ip, user_ip
```

### 2. **Manual Usage**
برای استفاده دستی:

```typescript
import { getClientIP } from '@/utils/getClientIP';

const ip = await getClientIP();
console.log('User IP:', ip);
```

### 3. **React Hook Usage**
در کامپوننت‌های React:

```typescript
import { useClientIP } from '@/hooks/useClientIP';

function MyComponent() {
  const { ip, isLoading, error, refetch } = useClientIP();
  
  if (isLoading) return <div>Loading IP...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return <div>Your IP: {ip}</div>;
}
```

## 📊 نحوه ارسال به سرور:

### Headers:
```
X-Client-IP: *************
X-Real-IP: *************
X-Forwarded-For: *************
```

### Data (encrypted):
```json
{
  "data": "encrypted_base64_string_containing_ip"
}
```

### Data (unencrypted):
```json
{
  "phone": "09121234567",
  "password": "testpass123",
  "client_ip": "*************",
  "user_ip": "*************"
}
```

## 🧪 تست کردن:

### 1. Browser Console:
```javascript
// در console مرورگر اجرا کنید:
testIP()
```

### 2. Manual Test:
```typescript
import { testIPFunctionality } from '@/utils/testIP';

testIPFunctionality().then(result => {
  console.log('Test result:', result);
});
```

## 🔄 Cache Management:

IP کاربر cache می‌شود تا از درخواست‌های اضافی جلوگیری شود:

```typescript
import { clearIPCache, getCachedIP } from '@/utils/getClientIP';

// گرفتن IP از cache
const cachedIP = getCachedIP();

// پاک کردن cache
clearIPCache();
```

## 🌐 IP Services:

سرویس‌های استفاده شده برای گرفتن IP:
1. `api.ipify.org` (اصلی)
2. `ipapi.co`
3. `httpbin.org`
4. `api.my-ip.io`
5. `ipinfo.io`

## ⚡ Performance:

- **اولین بار**: ~500-2000ms (بسته به سرعت اینترنت)
- **بار‌های بعدی**: ~0-5ms (از cache)
- **Timeout**: 5 ثانیه برای هر سرویس
- **Fallback**: اگر یک سرویس کار نکرد، سرویس بعدی امتحان می‌شود

## 🔒 امنیت:

- IP فقط از سرویس‌های معتبر گرفته می‌شود
- Validation برای IP address
- Error handling برای حالت‌های مختلف
- Fallback به 'unknown' در صورت عدم دسترسی

## 🚀 نکات مهم:

1. **IPPreloader** در layout اصلی اضافه شده تا IP از ابتدا آماده باشد
2. **Cache** باعث می‌شود درخواست‌های بعدی سریع باشند
3. **Multiple Headers** برای سازگاری با سرور‌های مختلف
4. **Both Data & Headers** برای اطمینان از دریافت IP توسط سرور
