{"name": "exchangim", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/node-forge": "^1.3.11", "axios": "^1.8.1", "framer-motion": "^12.6.3", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jalaali-js": "^1.2.8", "jspdf": "^3.0.1", "lucide-react": "^0.477.0", "next": "15.2.0", "next-auth": "^4.24.11", "node-forge": "^1.3.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-otp-input": "^3.1.1", "react-signature-canvas": "^1.1.0-alpha.2", "react-ts-tradingview-widgets": "^1.2.8", "swiper": "^11.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jalaali-js": "^1.2.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "tailwindcss": "^4", "typescript": "^5"}}